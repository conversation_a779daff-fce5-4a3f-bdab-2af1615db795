# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Geda is a comprehensive iOS task management and Pomodoro timer application built with SwiftUI, implementing modern iOS development patterns. The app follows a mature MVVM architecture with Repository pattern and includes CloudKit synchronization capabilities.

## Build & Development Commands

### Core Commands
```bash
# Build the project
xcodebuild -project Geda.xcodeproj -scheme Geda -destination 'platform=iOS Simulator,name=iPhone 16' build

# Run on simulator
xcodebuild -project Geda.xcodeproj -scheme Geda -destination 'platform=iOS Simulator,name=iPhone 16' install

# Launch app on simulator
xcrun simctl launch "iPhone 16" com.ryan.Geda

# List available schemes and targets
xcodebuild -list -project Geda.xcodeproj

# Open project in Xcode
open Geda.xcodeproj
```

### Testing Commands
```bash
# Run unit tests (basic validation)
swift TestRunner.swift

# Run XCTests (when test targets are configured)
xcodebuild test -project Geda.xcodeproj -scheme Geda -destination 'platform=iOS Simulator,name=iPhone 16'
```

## High-Level Architecture

### Core Architecture Pattern: MVVM + Repository + Dependency Injection

The application follows a sophisticated multi-layered architecture:

1. **Presentation Layer**: SwiftUI Views with ViewModels
2. **Business Logic Layer**: ViewModels inheriting from BaseViewModel
3. **Data Access Layer**: Repository pattern with protocol abstractions
4. **Persistence Layer**: Core Data with CloudKit synchronization
5. **Service Layer**: Managers for cross-cutting concerns

### Key Architectural Components

#### Data Flow Architecture
```
View → ViewModel → Repository → CoreDataManager → Core Data/CloudKit
     ←           ←            ←               ←
```

#### Dependency Management
- **RepositoryManager**: Central dependency container
- **CoreDataManager**: Singleton managing Core Data stack
- **Service Managers**: Specialized managers (Notification, Authentication, CloudKit, Performance)

#### Repository Pattern Implementation
All data access goes through protocol-based repositories:
- `TaskRepository`: Task CRUD operations
- `UserRepository`: User management 
- `PlanRepository`: Plan management
- `PomodoroSessionRepository`: Timer session tracking
- `SubtaskRepository`: Subtask operations

#### Base Classes
- **BaseViewModel**: Common ViewModel functionality with error handling, loading states, and async operations
- **Repository Protocols**: Defined in `TestProtocols.swift` for dependency injection and testing

### Core Data Model Structure

The app uses a comprehensive Core Data model with CloudKit support:

**Primary Entities:**
- `User`: User profile and authentication
- `Plan`: High-level project containers
- `Task`: Individual tasks with Pomodoro tracking
- `Subtask`: Granular task breakdown
- `PomodoroSession`: Timer session records

**Key Relationships:**
- User → Plans (one-to-many)
- Plan → Tasks (one-to-many) 
- Task → Subtasks (one-to-many)
- Task → PomodoroSessions (one-to-many)

### CloudKit Integration Strategy

The app implements a sophisticated CloudKit sync strategy:
- **NSPersistentCloudKitContainer**: Automatic sync with iCloud
- **Authentication-aware sync**: Enables/disables based on user login
- **Conflict resolution**: Merge policies for data consistency
- **Remote change notifications**: Real-time sync updates

## Project Structure Deep Dive

### Core Application Layer (`App/`)
- **GedaApp.swift**: Main app entry point with sophisticated initialization
  - Performance monitoring integration
  - Authentication state management
  - CloudKit sync coordination
  - Notification system setup

### Models & Data Layer (`Models/`)
- **CoreData/**: Auto-generated Core Data classes
- **ViewModels/**: Business logic controllers inheriting from BaseViewModel
- **Enums.swift**: App-wide enumerations

### View Architecture (`Views/`)
- **Main/**: Primary app screens (HomeView, PlanView, FocusTimerView, etc.)
- **Components/**: Reusable UI components following design system
- **Modals/**: Modal presentation system with custom modifiers

### Services Architecture (`Services/`)
- **Repositories/**: Data access layer with protocol abstractions
- **Auth/**: Authentication services with Apple ID integration
- **CloudKit/**: Cloud synchronization with conflict resolution
- **Managers**: Cross-cutting concern handlers

### Design System (`Utils/`)
- **DesignTokens.swift**: Centralized design system
- **Constants.swift**: App-wide constants matching HTML prototype
- **Extensions/**: SwiftUI extensions for consistent styling

## Key Development Patterns

### Async/Await + Combine Integration
The app uses modern concurrency patterns:
```swift
// ViewModels use performAsyncOperation for consistent error handling
performAsyncOperation(
    repositoryManager.taskRepository.fetchTodayTasks()
        .mapError { $0 as Error }
        .eraseToAnyPublisher()
) { [weak self] tasks in
    self?.todayTasks = tasks
}
```

### Protocol-Based Testing Strategy
All repositories implement protocols for testability:
```swift
protocol TaskRepositoryProtocol {
    func fetchTasks() -> AnyPublisher<[Task], Error>
    func createTask(_ task: Task) -> AnyPublisher<Task, Error>
    // ... other methods
}
```

### Performance Monitoring Integration
Built-in performance tracking throughout the app:
- App launch time monitoring
- Data load performance tracking
- Custom metrics for key operations

### Error Handling Strategy
Comprehensive error handling with:
- Custom error types (`CoreDataError`, `GedaError`)
- User-friendly error messages
- Automatic error recovery where possible

## HTML Prototype Integration

This iOS app is designed to replicate an HTML prototype (`prd/geda-pages/`):
- **Design consistency**: UI components match HTML styling
- **Animation parity**: SwiftUI animations replicate CSS animations
- **Layout fidelity**: Maintains same visual hierarchy and spacing

## Development Guidelines

### Code Quality Standards
- Follow Apple's Swift Style Guide
- Use dependency injection for testability
- Implement proper error handling at all layers
- Maintain separation of concerns between layers

### Performance Considerations
- Lazy loading for large data sets
- Efficient Core Data queries with predicates
- Background context for heavy operations
- Memory management with weak references

### CloudKit Best Practices
- Authentication-aware sync enabling/disabling
- Proper conflict resolution with merge policies
- Remote change notification handling
- Graceful degradation when CloudKit unavailable

## Testing Strategy

### Current Test Infrastructure
- **TestRunner.swift**: Basic validation runner
- **TestProtocols.swift**: Protocol definitions for mocking
- **Repository tests**: Data layer validation
- **ViewModel tests**: Business logic verification

### Test Architecture
- Protocol-based mocking for repositories
- Core Data in-memory store for tests
- Combine-based async testing patterns

## Key Files for Understanding

**Architecture Entry Points:**
- `GedaApp.swift`: App initialization and coordination
- `CoreDataManager.swift`: Data persistence strategy
- `BaseViewModel.swift`: Common ViewModel patterns

**Data Layer:**
- `RepositoryManager.swift`: Dependency container
- `TestProtocols.swift`: Repository abstractions

**Business Logic:**
- `HomeViewModel.swift`: Main screen logic example
- `TaskViewModel.swift`: Task management patterns

**Design System:**
- `DesignTokens.swift`: Visual design constants
- `Constants.swift`: App behavior constants

This architecture represents a production-ready iOS application with enterprise-level patterns including comprehensive error handling, performance monitoring, cloud synchronization, and maintainable code organization.