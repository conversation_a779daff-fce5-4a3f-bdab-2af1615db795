# 咯嗒(Geda) iOS应用开发指导文档

## 文档目的
本文档为AI开发助手提供详细的开发指导，确保按照实际页面原型逐步构建完整的iOS应用。

## 项目概述
- **应用名称**: 咯嗒(Geda)
- **应用类型**: 智能时间管理应用（番茄钟专注法核心）
- **技术栈**: SwiftUI + Core Data + Combine + async/await
- **架构模式**: MVVM + Repository模式
- **最低支持版本**: iOS 18.0
- **目标设备**: iPhone (SE到16 Pro Max)
- **设计规范**: 基于geda-pages HTML原型

## 开发环境配置
- **Xcode版本**: 16.0+
- **Swift版本**: 6.0
- **部署目标**: iOS 18.0
- **设备方向**: Portrait (竖屏)
- **屏幕尺寸**: 420×880px (iPhone标准尺寸)
- **响应式编程**: Combine + async/await

## 实际应用架构 (基于页面原型)

### 主导航结构
```
NavigationView (主导航容器)
├── HomeView (主页面 - 应用入口)
│   ├── 顶部切换导航 (日程/计划)
│   ├── 日历组件 (紧凑模式)
│   ├── 今日提醒卡片
│   ├── 今日任务列表 (堆叠卡片设计)
│   └── 底部操作栏 (AI语音 + 快速添加)
│
├── PlanView (计划管理页面)
├── FocusTimerView (番茄钟专注页面)
├── ReviewView (数据复盘页面)
├── ProfileView (个人资料页面)
├── CalendarView (完整日历页面)
├── ScannerView (扫描页面)
├── TaskDetailView (任务详情页面)
└── 各种Modal弹窗
```

### 页面导航模式
- **主页面**: HomeView作为应用入口，包含日程/计划切换
- **页面跳转**: 使用NavigationView的push/pop导航
- **返回导航**: 所有子页面都有返回按钮
- **底部操作栏**: 仅在HomeView显示，包含AI语音和快速添加

### 数据模型层次
```
Core Data Models:
├── User (用户信息)
├── Plan (计划)
├── Task (任务)
├── Subtask (子任务)
└── PomodoroSession (番茄钟会话)
```

## 项目文件结构规范 (基于实际页面)

```
Geda/
├── App/
│   ├── GedaApp.swift
│   └── ContentView.swift
├── Models/
│   ├── CoreData/
│   │   ├── Geda.xcdatamodeld
│   │   ├── User+CoreDataClass.swift
│   │   ├── Plan+CoreDataClass.swift
│   │   ├── Task+CoreDataClass.swift
│   │   ├── Subtask+CoreDataClass.swift
│   │   └── PomodoroSession+CoreDataClass.swift
│   └── ViewModels/
│       ├── HomeViewModel.swift
│       ├── PlanViewModel.swift
│       ├── TaskViewModel.swift
│       ├── FocusTimerViewModel.swift
│       └── ReviewViewModel.swift
├── Views/
│   ├── Main/
│   │   ├── HomeView.swift (主页面 - 应用入口)
│   │   ├── PlanView.swift (计划管理页面)
│   │   ├── FocusTimerView.swift (番茄钟专注页面)
│   │   ├── ReviewView.swift (数据复盘页面)
│   │   ├── ProfileView.swift (个人资料页面)
│   │   ├── CalendarView.swift (完整日历页面)
│   │   ├── ScannerView.swift (扫描页面)
│   │   └── TaskDetailView.swift (任务详情页面)
│   ├── Components/
│   │   ├── StackedTaskCards.swift (堆叠任务卡片)
│   │   ├── GedaCard.swift (通用卡片组件)
│   │   ├── CircularProgressView.swift (环形进度条)
│   │   ├── GradientButton.swift (渐变按钮)
│   │   ├── BottomActionBar.swift (底部操作栏)
│   │   ├── TopNavigationSwitch.swift (顶部切换导航)
│   │   └── CompactCalendar.swift (紧凑日历组件)
│   └── Modals/
│       ├── NewTaskModal.swift (新建任务弹窗)
│       ├── NewPlanModal.swift (新建计划弹窗)
│       ├── NewReminderModal.swift (新建提醒弹窗)
│       ├── XuebaModeInfoModal.swift (学霸模式说明)
│       └── ConfirmationModals.swift (各种确认弹窗)
├── Services/
│   ├── CoreDataManager.swift
│   ├── NotificationManager.swift
│   ├── HapticManager.swift
│   ├── ScannerManager.swift
│   └── Repositories/
│       ├── UserRepository.swift
│       ├── PlanRepository.swift
│       └── TaskRepository.swift
├── Utils/
│   ├── Extensions/
│   │   ├── Color+Geda.swift (颜色扩展)
│   │   ├── LinearGradient+Geda.swift (渐变扩展)
│   │   └── View+Extensions.swift
│   ├── Constants.swift
│   └── Helpers/
└── Resources/
    ├── Assets.xcassets
    ├── Colors.xcassets (基于CSS变量的颜色)
    └── Localizable.strings
```

## 开发阶段详细指导 (基于实际页面原型)

### 阶段1: 项目基础架构 (优先级: 最高)

#### 步骤1.1: 重构项目结构
**目标**: 建立基于实际页面的项目文件组织结构
**执行指导**:
1. 在Xcode中创建上述文件夹结构
2. 移动现有文件到对应位置
3. 更新项目引用路径
4. 配置NavigationView主导航容器

#### 步骤1.2: 配置Core Data
**目标**: 建立数据持久化基础
**执行指导**:
1. 创建Geda.xcdatamodeld文件
2. 定义5个核心实体及其属性关系
3. 实现CoreDataManager单例类
4. 配置数据模型版本控制

#### 步骤1.3: 实现Repository层
**目标**: 建立数据访问抽象层
**执行指导**:
1. 创建Repository协议接口
2. 实现具体Repository类
3. 集成Core Data操作
4. 添加错误处理机制

### 阶段2: 设计系统和基础组件 (优先级: 高)

#### 步骤2.1: 设计系统建立 (基于CSS变量)
**目标**: 复现HTML原型的视觉设计
**执行指导**:
1. 定义颜色系统 (对应CSS变量: --accent-start: #818cf8, --accent-end: #a78bfa)
2. 创建渐变色定义 (LinearGradient.gedaGradient)
3. 实现卡片样式系统 (对应custom-card样式)
4. 建立字体层级系统 (Noto Sans SC + Inter)
5. 创建阴影和圆角规范

#### 步骤2.2: 核心UI组件开发
**目标**: 复现HTML原型的UI组件
**执行指导**:
1. GedaCard: 对应custom-card样式的卡片组件
2. GradientButton: 对应gradient-button样式的渐变按钮
3. StackedTaskCards: 实现堆叠任务卡片效果 (task-card-1/2/3)
4. CircularProgressView: 番茄钟环形进度条 (SVG样式)
5. BottomActionBar: 底部操作栏组件
6. TopNavigationSwitch: 日程/计划切换导航

### 阶段3: 主页面框架 (优先级: 高)

#### 步骤3.1: NavigationView主框架实现
**目标**: 建立页面导航系统
**执行指导**:
1. 创建NavigationView主容器
2. 配置页面跳转逻辑
3. 实现返回按钮功能
4. 添加页面切换动画

#### 步骤3.2: HomeView主页面开发
**目标**: 实现应用主入口页面
**执行指导**:
1. 实现顶部日程/计划切换导航
2. 开发紧凑日历组件
3. 实现今日提醒卡片
4. 创建堆叠任务卡片列表
5. 添加底部操作栏 (AI语音 + 快速添加菜单)
6. 配置页面布局和间距

### 阶段4: 核心页面开发 (优先级: 高)

#### 步骤4.1: PlanView计划页面开发
**目标**: 实现计划管理功能
**执行指导**:
1. 实现返回导航按钮
2. 创建导入分享码和探索模板按钮
3. 实现进行中计划列表 (带进度条)
4. 实现已搁置计划列表
5. 实现已完成计划列表
6. 添加计划分享功能

#### 步骤4.2: FocusTimerView专注页面开发
**目标**: 实现番茄钟计时功能
**执行指导**:
1. 实现页面导航栏 (返回按钮 + "专注中"标题)
2. 创建环形进度条计时器 (SVG样式，对应HTML)
3. 实现时间显示 (25:00格式) 和状态文本
4. 开发控制按钮组 (重置/播放暂停/跳过)
5. 实现番茄设置区域 (工作时间/休息时间调节)
6. 添加计时器逻辑和后台支持

#### 步骤4.3: ReviewView复盘页面开发
**目标**: 实现数据分析和可视化
**执行指导**:
1. 实现顶部导航栏 (返回按钮 + 时间范围选择器)
2. 创建学习总览渐变卡片 (紫色到蓝色渐变)
3. 实现统计指标网格布局 (平均专注时长/完成率等)
4. 开发学习时长图表组件
5. 实现成就展示区域
6. 添加数据筛选和统计功能

## 开发执行原则

### 代码质量标准
1. **Swift代码规范**: 遵循官方Swift Style Guide
2. **命名规范**: 清晰、一致的命名约定
3. **注释要求**: 关键逻辑必须添加中文注释
4. **错误处理**: 完善的错误处理和用户反馈
5. **性能考虑**: 避免内存泄漏，优化渲染性能

### 测试要求
1. **单元测试**: Repository层和ViewModel逻辑测试
2. **UI测试**: 主要用户流程的自动化测试
3. **真机测试**: 多设备兼容性验证

### 用户体验原则
1. **iOS设计规范**: 遵循Apple Human Interface Guidelines
2. **响应式设计**: 适配不同屏幕尺寸
3. **无障碍支持**: VoiceOver和动态字体支持
4. **性能优化**: 流畅的动画和快速响应

## 关键技术实现指导

### Core Data集成
- 使用NSPersistentContainer管理Core Data栈
- 实现数据模型迁移策略
- 优化批量数据操作性能

### SwiftUI最佳实践
- 合理使用@State、@ObservedObject、@StateObject
- 避免视图重复渲染
- 实现高效的列表渲染

### 通知系统
- 集成UNUserNotificationCenter
- 实现任务提醒和番茄钟提醒
- 处理通知权限和用户交互

### 性能优化
- 图片资源优化和懒加载
- 数据分页加载
- 内存使用监控

## 开发里程碑检查点

### 里程碑1: 基础架构完成
- [ ] 项目结构重构完成
- [ ] Core Data模型建立
- [ ] Repository层实现
- [ ] 基础UI组件库完成

### 里程碑2: 主框架完成
- [ ] 主导航TabView实现
- [ ] 5个主页面基础结构
- [ ] 页面间导航逻辑
- [ ] ViewModel集成

### 里程碑3: 核心功能完成
- [ ] 首页功能完整实现
- [ ] 任务管理CRUD完成
- [ ] 番茄钟计时器实现
- [ ] 数据持久化验证

### 里程碑4: 高级功能完成
- [ ] 计划管理系统
- [ ] 数据复盘模块
- [ ] 通知系统集成
- [ ] 扫描功能实现

### 里程碑5: 发布准备
- [ ] 全功能测试完成
- [ ] 性能优化验证
- [ ] 多设备适配测试
- [ ] App Store资料准备

## 下一步执行指导

**立即开始**: 从阶段1步骤1.1开始执行
**执行方式**: 严格按照检查清单逐项完成
**质量控制**: 每个里程碑完成后进行全面测试验证
**进度跟踪**: 及时更新开发进度和遇到的技术问题

## 详细技术实现规范

### Core Data模型定义

#### User实体属性
```swift
// User实体属性定义
id: String (Primary Key)
name: String
avatar: String (可选)
checkInDays: Int32 (默认0)
createdAt: Date
updatedAt: Date
```

#### Plan实体属性
```swift
// Plan实体属性定义
id: String (Primary Key)
title: String
planDescription: String (对应PRD中的description/座右铭)
status: String (active/paused/completed)
progress: Float (0.0-1.0)
startDate: Date
endDate: Date (可选)
totalTomatoes: Int32
createdAt: Date
updatedAt: Date
// 关系
tasks: To-Many relationship to Task
user: To-One relationship to User
```

#### Task实体属性
```swift
// Task实体属性定义
id: String (Primary Key)
title: String
startTime: Date
endTime: Date
priority: String (high/medium/low)
tomatoCount: Int32
isReminderEnabled: Bool
isCompleted: Bool
createdAt: Date
updatedAt: Date
// 关系
plan: To-One relationship to Plan
subtasks: To-Many relationship to Subtask
pomodoroSessions: To-Many relationship to PomodoroSession
```

#### Subtask实体属性
```swift
// Subtask实体属性定义
id: String (Primary Key)
title: String
isCompleted: Bool
createdAt: Date
updatedAt: Date
// 关系
task: To-One relationship to Task
```

#### PomodoroSession实体属性
```swift
// PomodoroSession实体属性定义
id: String (Primary Key)
sessionType: String (work/break)
duration: Double (秒数)
startTime: Date
endTime: Date (可选)
isCompleted: Bool
createdAt: Date
// 关系
task: To-One relationship to Task
```

### SwiftUI组件实现规范

#### GedaButton组件规范
```swift
// 按钮样式枚举
enum GedaButtonStyle {
    case primary    // 主要按钮 (渐变背景)
    case secondary  // 次要按钮 (边框样式)
    case text       // 文本按钮
    case icon       // 图标按钮
}

// 按钮尺寸枚举
enum GedaButtonSize {
    case small      // 高度32pt
    case medium     // 高度44pt
    case large      // 高度56pt
}
```

#### TaskCard组件规范
```swift
// 任务卡片必须包含的信息
- 任务标题 (最大2行，超出显示省略号)
- 时间范围 (HH:mm - HH:mm格式)
- 优先级标识 (彩色圆点: 红色=高，橙色=中，绿色=低)
- 番茄钟数量 (🍅图标 + 数字)
- 完成状态 (复选框，支持点击切换)
- 点击区域 (整个卡片可点击跳转详情)
```

#### CircularProgressView组件规范
```swift
// 环形进度条参数
- 外圆直径: 200pt
- 线条宽度: 8pt
- 进度颜色: 渐变色 (#667eea → #764ba2)
- 背景颜色: 浅灰色
- 动画时长: 0.3秒
- 支持进度值: 0.0-1.0
```

### 页面布局实现规范

#### HomeView布局结构
```swift
VStack(spacing: 0) {
    // 主内容区域
    ScrollView {
        VStack(spacing: 16) {
            // 顶部导航切换 (日程/计划)
            TopNavigationView()
                .padding(.top, 12) // 替代状态栏间距

            // 日历组件 (紧凑模式)
            CalendarCompactView()

            // 今日提醒卡片
            TodayReminderCard()

            // 今日任务标题和控制
            TaskSectionHeader()

            // 堆叠任务卡片列表
            StackedTaskCards(tasks: todayTasks)
        }
        .padding(.horizontal, 16)
    }

    // 底部操作栏
    BottomActionBar()
        .padding(.bottom, 16)
}
```

#### FocusView布局结构
```swift
VStack(spacing: 0) {
    // 导航栏
    NavigationHeaderView(title: "专注")

    Spacer()

    // 中央计时器区域
    VStack(spacing: 32) {
        // 任务名称显示
        Text(currentTask.title)
            .font(.headline)
            .foregroundColor(.secondary)

        // 环形进度条 + 时间显示
        ZStack {
            CircularProgressView(progress: timerProgress)

            VStack(spacing: 8) {
                Text(timeString)
                    .font(.system(size: 48, weight: .light, design: .monospaced))

                Text(sessionTypeText)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }

        // 控制按钮组
        HStack(spacing: 24) {
            Button("重置") { resetTimer() }

            Button(isRunning ? "暂停" : "开始") { toggleTimer() }
                .buttonStyle(PrimaryButtonStyle())

            Button("跳过") { skipSession() }
        }
    }

    Spacer()

    // 时间设置区域
    TimerSettingsView()

    Spacer().frame(height: 83)
}
```

### 数据流管理规范

#### ViewModel实现模式
```swift
// 标准ViewModel结构
class HomeViewModel: ObservableObject {
    // Published属性 (UI绑定)
    @Published var todayTasks: [Task] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?

    // 依赖注入
    private let taskRepository: TaskRepositoryProtocol
    private let userRepository: UserRepositoryProtocol

    // 初始化
    init(taskRepository: TaskRepositoryProtocol,
         userRepository: UserRepositoryProtocol) {
        self.taskRepository = taskRepository
        self.userRepository = userRepository
        loadTodayTasks()
    }

    // 公共方法
    func loadTodayTasks() { }
    func createTask(_ task: Task) { }
    func updateTask(_ task: Task) { }
    func deleteTask(_ task: Task) { }
}
```

#### Repository实现模式
```swift
// Repository协议定义
protocol TaskRepositoryProtocol {
    func fetchTasks() -> AnyPublisher<[Task], Error>
    func fetchTasksForDate(_ date: Date) -> AnyPublisher<[Task], Error>
    func createTask(_ task: Task) -> AnyPublisher<Task, Error>
    func updateTask(_ task: Task) -> AnyPublisher<Task, Error>
    func deleteTask(_ task: Task) -> AnyPublisher<Void, Error>
}

// 具体实现
class TaskRepository: TaskRepositoryProtocol {
    private let coreDataManager: CoreDataManager

    init(coreDataManager: CoreDataManager) {
        self.coreDataManager = coreDataManager
    }

    // 实现协议方法...
}
```

### 通知系统实现规范

#### 通知类型定义
```swift
enum NotificationType {
    case taskReminder(taskId: String)
    case pomodoroComplete(sessionId: String)
    case breakComplete(sessionId: String)
    case dailyReview
}
```

#### 通知调度规范
```swift
// 任务提醒通知
- 提前15分钟提醒
- 通知标题: "任务提醒"
- 通知内容: "您的任务「{任务名称}」即将开始"
- 操作按钮: "开始专注" / "延后5分钟"

// 番茄钟完成通知
- 工作时间结束提醒
- 通知标题: "专注时间结束"
- 通知内容: "恭喜完成25分钟专注，休息一下吧"
- 操作按钮: "开始休息" / "继续专注"
```

### 性能优化指导

#### 列表渲染优化
```swift
// 使用LazyVStack替代VStack处理大量数据
LazyVStack(spacing: 12) {
    ForEach(tasks) { task in
        TaskCard(task: task)
            .onAppear {
                // 预加载逻辑
                if task == tasks.last {
                    loadMoreTasks()
                }
            }
    }
}
```

#### 图片加载优化
```swift
// 异步图片加载
AsyncImage(url: URL(string: user.avatar)) { image in
    image
        .resizable()
        .aspectRatio(contentMode: .fill)
} placeholder: {
    Circle()
        .fill(Color.gray.opacity(0.3))
        .overlay(
            Image(systemName: "person.fill")
                .foregroundColor(.gray)
        )
}
.frame(width: 60, height: 60)
.clipShape(Circle())
```

### 错误处理规范

#### 错误类型定义
```swift
enum GedaError: LocalizedError {
    case networkError
    case dataCorruption
    case userNotFound
    case taskNotFound
    case validationError(String)

    var errorDescription: String? {
        switch self {
        case .networkError:
            return "网络连接失败，请检查网络设置"
        case .dataCorruption:
            return "数据损坏，请重新启动应用"
        case .userNotFound:
            return "用户信息不存在"
        case .taskNotFound:
            return "任务不存在或已被删除"
        case .validationError(let message):
            return message
        }
    }
}
```

#### 错误展示规范
```swift
// 使用Alert展示错误信息
.alert("错误", isPresented: $showError) {
    Button("确定") { }
} message: {
    Text(errorMessage ?? "未知错误")
}

// 使用Toast展示轻量级提示
.toast(message: "任务创建成功", isShowing: $showToast)
```

## 关键技术实现补充 (基于页面原型细节分析)

### 精确动画系统实现

#### 堆叠卡片动画 (对应tasks-expanded CSS类)
```swift
struct StackedTaskCards: View {
    @State private var isExpanded = false
    let tasks: [Task]

    var body: some View {
        ZStack {
            ForEach(tasks.indices, id: \.self) { index in
                TaskCard(task: tasks[index])
                    .scaleEffect(isExpanded ? 1.0 : (1.0 - Double(index) * 0.05))
                    .offset(
                        x: isExpanded ? 0 : CGFloat(index * 16),
                        y: isExpanded ? CGFloat(index * 144) : 0
                    )
                    .zIndex(Double(tasks.count - index))
                    .opacity(isExpanded ? 1.0 : (index == 0 ? 1.0 : (index == 1 ? 0.8 : 0.6)))
            }
        }
        .frame(height: isExpanded ? CGFloat(tasks.count * 144) : 128)
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.4)) {
                isExpanded.toggle()
            }
        }
    }
}
```

#### 环形进度条动画 (对应timer-circle-progress CSS)
```swift
struct CircularProgressView: View {
    let progress: Double
    let lineWidth: CGFloat = 8

    var body: some View {
        ZStack {
            // 背景圆环 (对应 stroke="#eef2ff")
            Circle()
                .stroke(Color(hex: "#eef2ff"), lineWidth: lineWidth)

            // 进度圆环 (对应 stroke="url(#gradient)")
            Circle()
                .trim(from: 0, to: progress)
                .stroke(
                    LinearGradient.gedaGradient,
                    style: StrokeStyle(lineWidth: lineWidth, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
                .animation(.linear(duration: 1), value: progress)
        }
    }
}
```

#### 扫描线动画 (对应scan-line CSS动画)
```swift
struct ScanningLine: View {
    @State private var animationOffset: CGFloat = -100

    var body: some View {
        Rectangle()
            .fill(Color.gedaAccentStart.opacity(0.5))
            .frame(height: 2)
            .offset(y: animationOffset)
            .onAppear {
                withAnimation(.easeInOut(duration: 2.5).repeatForever(autoreverses: true)) {
                    animationOffset = 100
                }
            }
    }
}
```

### 高级视觉效果实现

#### 毛玻璃底部操作栏 (对应backdrop-blur-lg)
```swift
struct BottomActionBar: View {
    @State private var showAddMenu = false

    var body: some View {
        HStack {
            // AI语音按钮
            Button(action: { /* AI语音功能 */ }) {
                Image(systemName: "mic.fill")
                    .foregroundColor(.white)
            }
            .frame(width: 48, height: 48)
            .background(LinearGradient.gedaGradient)
            .clipShape(Circle())
            .scaleEffect(showAddMenu ? 0.9 : 1.0) // 对应active:scale-90

            Spacer()

            Text("可以对我讲你的待办任务")
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()

            // 快速添加按钮
            Button(action: { showAddMenu.toggle() }) {
                Image(systemName: "plus")
                    .foregroundColor(.white)
            }
            .frame(width: 48, height: 48)
            .background(LinearGradient.gedaGradient)
            .clipShape(Circle())
        }
        .padding()
        .background(
            .regularMaterial, // SwiftUI的毛玻璃效果
            in: Capsule()
        )
        .shadow(color: .gray.opacity(0.2), radius: 20, x: 0, y: 10)
    }
}
```

#### GedaCard组件 (对应custom-card CSS)
```swift
struct GedaCard<Content: View>: View {
    let content: Content

    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }

    var body: some View {
        content
            .background(Color.white)
            .cornerRadius(20) // 对应border-radius: 1.25rem
            .shadow(
                color: Color.black.opacity(0.1),
                radius: 10,
                x: 0,
                y: 5
            ) // 对应--card-shadow CSS变量
    }
}
```

### 数据可视化组件实现

#### 学习趋势柱状图 (对应review.html图表)
```swift
struct LearningTrendChart: View {
    let data: [Double] = [0.4, 0.6, 0.8, 0.95, 0.7, 0.45, 0.85]
    let days = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]

    var body: some View {
        VStack {
            HStack(alignment: .bottom, spacing: 8) {
                ForEach(0..<data.count, id: \.self) { index in
                    VStack {
                        Rectangle()
                            .fill(
                                LinearGradient(
                                    colors: [
                                        Color(hex: "#a78bfa").opacity(0.3),
                                        Color(hex: "#818cf8")
                                    ],
                                    startPoint: .top,
                                    endPoint: .bottom
                                )
                            )
                            .frame(width: 32, height: CGFloat(data[index] * 120))
                            .cornerRadius(4, corners: [.topLeft, .topRight])

                        Text(days[index])
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .padding(.top, 8)
                    }
                }
            }
            .frame(height: 160)

            VStack {
                Text("本周平均每日学习时长")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text("2.3小时")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(Color(hex: "#818cf8"))
            }
            .padding(.top)
        }
    }
}
```

#### 学科分布进度条组件
```swift
struct SubjectProgressBar: View {
    let subject: String
    let hours: String
    let progress: Double
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                HStack(spacing: 12) {
                    Circle()
                        .fill(color)
                        .frame(width: 16, height: 16)

                    Text(subject)
                        .font(.subheadline)
                        .fontWeight(.medium)
                }

                Spacer()

                Text(hours)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: color))
                .frame(height: 8)
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}
```

### 复杂表单组件实现

#### 优先级按钮组 (对应new-task-modal.html)
```swift
struct PriorityButtonGroup: View {
    @Binding var selectedPriority: Priority

    var body: some View {
        HStack(spacing: 8) {
            ForEach(Priority.allCases, id: \.self) { priority in
                Button(action: {
                    selectedPriority = priority
                }) {
                    Text(priority.displayName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(
                            selectedPriority == priority ? .white : .primary
                        )
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(
                    selectedPriority == priority
                        ? LinearGradient.gedaGradient
                        : Color.clear
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.gray.opacity(0.4), lineWidth: 1)
                )
                .cornerRadius(8)
            }
        }
    }
}
```

#### 番茄钟数量控制器
```swift
struct PomodoroCountControl: View {
    @Binding var count: Int

    var body: some View {
        HStack {
            Button(action: {
                if count > 1 { count -= 1 }
            }) {
                Image(systemName: "minus")
                    .font(.title2)
                    .fontWeight(.bold)
            }
            .frame(width: 32, height: 32)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(8)

            Spacer()

            VStack {
                Text("\(count)")
                    .font(.title2)
                    .fontWeight(.bold)

                Text("个")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Button(action: {
                if count < 10 { count += 1 }
            }) {
                Image(systemName: "plus")
                    .font(.title2)
                    .fontWeight(.bold)
            }
            .frame(width: 32, height: 32)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(8)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}
```

### Modal弹窗系统实现

#### Modal展示修饰符
```swift
struct ModalPresentation: ViewModifier {
    @Binding var isPresented: Bool

    func body(content: Content) -> some View {
        ZStack {
            if isPresented {
                Color.black.opacity(0.5)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation {
                            isPresented = false
                        }
                    }

                content
                    .background(Color.white)
                    .cornerRadius(16)
                    .padding()
                    .scaleEffect(isPresented ? 1 : 0.95)
                    .opacity(isPresented ? 1 : 0)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: isPresented)
    }
}

extension View {
    func modalPresentation(isPresented: Binding<Bool>) -> some View {
        modifier(ModalPresentation(isPresented: isPresented))
    }
}
```

此文档将作为整个开发过程的指导手册，确保开发工作的系统性和完整性。每个技术实现都必须严格按照上述规范执行，以保证代码质量和用户体验的一致性。补充的技术实现要点确保能够完全复刻geda-pages HTML原型的所有视觉效果和交互功能。
