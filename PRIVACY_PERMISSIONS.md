# Geda iOS应用权限配置

## 隐私权限说明

为了完整使用Geda应用的所有功能，需要在Info.plist或者Xcode项目设置中添加以下权限配置：

### 1. 语音识别权限 (Speech Recognition)
```xml
<key>NSSpeechRecognitionUsageDescription</key>
<string>Geda需要访问语音识别功能，将您的语音转换为文字来快速创建任务。</string>
```

### 2. 麦克风访问权限 (Microphone)
```xml
<key>NSMicrophoneUsageDescription</key>
<string>Geda需要访问麦克风来录制您的语音，以便进行语音识别和任务创建。</string>
```

### 3. 相机访问权限 (Camera) - 已配置
```xml
<key>NSCameraUsageDescription</key>
<string>Geda需要访问相机来扫描二维码，分享和导入计划模板。</string>
```

### 4. 通知权限配置 (User Notifications)
```xml
<key>UIBackgroundModes</key>
<array>
    <string>background-processing</string>
    <string>audio</string>
</array>
```

## Xcode项目设置配置

如果使用Xcode 16+的现代化项目配置，请在项目设置中添加：

1. **Target Settings** → **Info** → **Custom iOS Target Properties**
2. 添加上述键值对

或者：

1. **Target Settings** → **Info** → **Privacy - Speech Recognition Usage Description**
2. **Target Settings** → **Info** → **Privacy - Microphone Usage Description**
3. 输入对应的权限说明文字

## 功能对应权限

- **语音输入功能**: 需要 Speech Recognition + Microphone 权限
- **二维码扫描**: 需要 Camera 权限  
- **番茄钟通知**: 需要 User Notifications 权限
- **任务提醒**: 需要 User Notifications 权限

## 开发注意事项

1. 这些权限仅在用户首次使用对应功能时才会请求
2. 用户可以在系统设置中随时更改权限
3. 应用需要优雅处理权限被拒绝的情况
4. 所有权限请求都有相应的fallback UI和错误处理

## 测试验证

在测试阶段，请确保：
- 权限请求弹窗正常显示
- 权限被拒绝时有正确的错误提示
- 权限授权后功能正常工作
- 可以在设置中重新开启权限

---

**注意**: 上述权限配置对于语音输入功能是必需的，请在发布前确保已正确配置。