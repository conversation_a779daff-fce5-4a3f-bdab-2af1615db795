# Geda iOS 项目开发要求

## 核心开发指导原则

### 主要目标
- **精确复刻HTML原型**: 确保iOS SwiftUI实现与HTML原型在视觉和功能上保持高度一致
- **基础功能实现**: 专注于核心的番茄钟和任务管理功能
- **架构清晰合理**: 采用专业的软件开发架构，确保代码质量
- **编译通过**: 每一步的改动都要确保项目能够成功编译

### 开发方法论
- **explore-plan-code-test**: 探索分析 → 规划设计 → 编码实现 → 测试验证
- **渐进式开发**: 分阶段完成，确保每个阶段的稳定性
- **HTML对标**: 所有UI组件和交互都要对应HTML原型中的实现

### 技术要求
- **iOS平台**: SwiftUI + Core Data + Combine
- **架构模式**: MVVM + Repository Pattern
- **兼容性**: iOS 18.2+ 
- **编译稳定**: 确保每次提交都能成功编译运行

### 功能边界
- ✅ **包含功能**: 
  - 任务管理 (创建、编辑、删除、完成)
  - 番茄钟计时器
  - 计划管理
  - 数据复盘和统计
  - 用户个人资料
  - 日历功能
  - 基础设置

- ❌ **不包含功能**:
  - Apple Watch适配
  - iPad专门优化
  - Siri快捷指令
  - 桌面小组件
  - 复杂的AI功能

### 语音输入说明
- 语音输入功能将由用户后期自行集成
- 当前实现中可以保留语音输入的UI入口和基础架构
- 不需要实现复杂的语音识别和AI处理逻辑

### 质量标准
- **代码质量**: 遵循Swift最佳实践，清晰的命名和结构
- **架构合理**: 层次分明，职责清晰，易于维护
- **HTML一致性**: UI组件、布局、交互与HTML原型保持一致
- **编译通过**: 无编译错误，无明显警告

### 开发重点
1. **精确复刻**: HTML→SwiftUI的精确转换
2. **核心功能**: 专注于任务管理和番茄钟的核心体验
3. **架构清晰**: 建立可维护的代码架构
4. **质量保证**: 确保编译稳定和基本功能正常

## 当前项目状态
- 项目完成度: 99%
- 主要功能: 已完成HTML原型的全部8个页面实现
- 架构状态: MVVM架构完整，Repository模式实现
- 编译状态: 项目在iPhone 16 Pro模拟器编译成功

## 开发原则总结
**保持简单，专注核心，精确复刻，架构清晰**