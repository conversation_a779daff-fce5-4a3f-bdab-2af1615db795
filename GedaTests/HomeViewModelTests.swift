//
//  HomeViewModelTests.swift
//  GedaTests
//
//  Created by AI Assistant on 2025-07-14.
//

import XCTest
import Combine
import CoreData
@testable import Geda

/// 主页ViewModel测试类
/// 测试HomeViewModel的业务逻辑
final class HomeViewModelTests: XCTestCase {
    
    // MARK: - 测试属性
    
    var homeViewModel: HomeViewModel!
    var mockTaskRepository: MockTaskRepository!
    var mockUserRepository: MockUserRepository!
    var cancellables: Set<AnyCancellable>!
    
    // MARK: - 测试生命周期
    
    override func setUpWithError() throws {
        super.setUp()
        
        // 创建模拟仓库
        mockTaskRepository = MockTaskRepository()
        mockUserRepository = MockUserRepository()
        
        // 创建待测试的ViewModel
        homeViewModel = HomeViewModel(
            taskRepository: mockTaskRepository,
            userRepository: mockUserRepository
        )
        
        // 初始化Combine订阅集合
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDownWithError() throws {
        // 清理资源
        homeViewModel = nil
        mockTaskRepository = nil
        mockUserRepository = nil
        cancellables = nil
        
        super.tearDown()
    }
    
    // MARK: - 加载今日任务测试
    
    func testLoadTodayTasks_Success() throws {
        // Given: 准备模拟数据
        let mockTasks = createMockTasks()
        mockTaskRepository.mockTodayTasks = mockTasks
        
        let expectation = XCTestExpectation(description: "加载今日任务成功")
        
        // 监听todayTasks变化
        homeViewModel.$todayTasks
            .dropFirst() // 跳过初始值
            .sink { tasks in
                // Then: 验证结果
                XCTAssertEqual(tasks.count, mockTasks.count)
                XCTAssertFalse(self.homeViewModel.isLoading)
                XCTAssertNil(self.homeViewModel.errorMessage)
                
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // When: 加载今日任务
        homeViewModel.loadTodayTasks()
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testLoadTodayTasks_Failure() throws {
        // Given: 设置模拟错误
        mockTaskRepository.shouldFail = true
        
        let expectation = XCTestExpectation(description: "加载今日任务失败")
        
        // 监听错误状态
        homeViewModel.$errorMessage
            .compactMap { $0 } // 过滤掉nil值
            .sink { errorMessage in
                // Then: 验证错误处理
                XCTAssertFalse(errorMessage.isEmpty)
                XCTAssertFalse(self.homeViewModel.isLoading)
                
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // When: 加载今日任务（会失败）
        homeViewModel.loadTodayTasks()
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    // MARK: - 任务操作测试
    
    func testToggleTaskCompletion_Success() throws {
        // Given: 准备一个未完成的任务
        let task = createMockTask(isCompleted: false)
        homeViewModel.todayTasks = [task]
        
        let expectation = XCTestExpectation(description: "切换任务完成状态成功")
        
        // 监听任务状态变化
        homeViewModel.$todayTasks
            .dropFirst() // 跳过初始值
            .sink { tasks in
                if let updatedTask = tasks.first {
                    // Then: 验证任务状态已更新
                    XCTAssertTrue(updatedTask.isCompleted)
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // When: 切换任务完成状态
        homeViewModel.toggleTaskCompletion(task)
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    // MARK: - 统计数据测试
    
    func testCompletedTasksCount() throws {
        // Given: 准备混合状态的任务
        let tasks = [
            createMockTask(isCompleted: true),
            createMockTask(isCompleted: false),
            createMockTask(isCompleted: true)
        ]
        
        // When: 设置任务列表
        homeViewModel.todayTasks = tasks
        
        // Then: 验证已完成任务数量
        XCTAssertEqual(homeViewModel.completedTasksCount, 2)
    }
    
    func testTotalTasksCount() throws {
        // Given: 准备任务列表
        let tasks = createMockTasks(count: 5)
        
        // When: 设置任务列表
        homeViewModel.todayTasks = tasks
        
        // Then: 验证总任务数量
        XCTAssertEqual(homeViewModel.totalTasksCount, 5)
    }
    
    func testCompletionPercentage() throws {
        // Given: 准备任务（2个完成，3个未完成）
        let tasks = [
            createMockTask(isCompleted: true),
            createMockTask(isCompleted: true),
            createMockTask(isCompleted: false),
            createMockTask(isCompleted: false),
            createMockTask(isCompleted: false)
        ]
        
        // When: 设置任务列表
        homeViewModel.todayTasks = tasks
        
        // Then: 验证完成百分比（2/5 = 40%）
        XCTAssertEqual(homeViewModel.completionPercentage, 0.4, accuracy: 0.01)
    }
    
    func testCompletionPercentage_NoTasks() throws {
        // Given: 空任务列表
        homeViewModel.todayTasks = []
        
        // When & Then: 验证空列表的完成百分比为0
        XCTAssertEqual(homeViewModel.completionPercentage, 0.0)
    }
    
    // MARK: - 用户数据测试
    
    func testLoadUserProfile_Success() throws {
        // Given: 准备模拟用户数据
        let mockUser = createMockUser()
        mockUserRepository.mockCurrentUser = mockUser
        
        let expectation = XCTestExpectation(description: "加载用户资料成功")
        
        // 监听currentUser变化
        homeViewModel.$currentUser
            .compactMap { $0 } // 过滤掉nil值
            .sink { user in
                // Then: 验证用户数据
                XCTAssertEqual(user.name, mockUser.name)
                XCTAssertEqual(user.checkInDays, mockUser.checkInDays)
                
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // When: 加载用户资料
        homeViewModel.loadUserProfile()
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    // MARK: - 刷新数据测试
    
    func testRefreshData() throws {
        // Given: 设置初始状态
        homeViewModel.todayTasks = []
        homeViewModel.currentUser = nil
        
        let expectation = XCTestExpectation(description: "刷新数据成功")
        expectation.expectedFulfillmentCount = 2 // 期望两个数据源都更新
        
        // 监听数据变化
        homeViewModel.$todayTasks
            .dropFirst()
            .sink { _ in
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        homeViewModel.$currentUser
            .compactMap { $0 }
            .sink { _ in
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // When: 刷新数据
        homeViewModel.refreshData()
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    // MARK: - 性能测试
    
    func testLoadTodayTasksPerformance() throws {
        // 性能测试：加载大量任务
        mockTaskRepository.mockTodayTasks = createMockTasks(count: 1000)
        
        measure {
            let expectation = XCTestExpectation(description: "性能测试")
            
            homeViewModel.$todayTasks
                .dropFirst()
                .sink { _ in
                    expectation.fulfill()
                }
                .store(in: &cancellables)
            
            homeViewModel.loadTodayTasks()
            
            wait(for: [expectation], timeout: 1.0)
        }
    }
}

// MARK: - 模拟仓库

class MockTaskRepository: TaskRepositoryProtocol {
    var mockTodayTasks: [Task] = []
    var shouldFail: Bool = false
    
    func fetchTasks() -> AnyPublisher<[Task], Error> {
        if shouldFail {
            return Fail(error: RepositoryError.dataCorruption)
                .eraseToAnyPublisher()
        }
        return Just(mockTodayTasks)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func fetchTodayTasks() -> AnyPublisher<[Task], Error> {
        return fetchTasks()
    }
    
    func fetchTasksForDate(_ date: Date) -> AnyPublisher<[Task], Error> {
        return fetchTasks()
    }
    
    func fetchTasks(for plan: Plan) -> AnyPublisher<[Task], Error> {
        return fetchTasks()
    }
    
    func createTask(_ task: Task) -> AnyPublisher<Task, Error> {
        return Just(task)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func createTask(title: String, startTime: Date, endTime: Date, priority: Priority, tomatoCount: Int32, isReminderEnabled: Bool) -> AnyPublisher<Task, Error> {
        let task = createMockTask()
        task.title = title
        return Just(task)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func updateTask(_ task: Task) -> AnyPublisher<Task, Error> {
        return Just(task)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func deleteTask(_ task: Task) -> AnyPublisher<Void, Error> {
        return Just(())
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func toggleTaskCompletion(_ task: Task) -> AnyPublisher<Task, Error> {
        task.isCompleted.toggle()
        return Just(task)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
}

class MockUserRepository: UserRepositoryProtocol {
    var mockCurrentUser: User?
    
    func getCurrentUser() -> AnyPublisher<User?, Error> {
        return Just(mockCurrentUser)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func createUser(name: String, avatar: String) -> AnyPublisher<User, Error> {
        let user = createMockUser(name: name)
        mockCurrentUser = user
        return Just(user)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func updateUser(_ user: User) -> AnyPublisher<User, Error> {
        return Just(user)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func deleteUser(_ user: User) -> AnyPublisher<Void, Error> {
        mockCurrentUser = nil
        return Just(())
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    func incrementCheckInDays() -> AnyPublisher<User, Error> {
        mockCurrentUser?.checkInDays += 1
        return Just(mockCurrentUser!)
            .setFailureType(to: Error.self)
            .eraseToAnyPublisher()
    }
    
    private func createMockUser(name: String) -> User {
        let context = createInMemoryContext()
        let user = User(context: context)
        user.name = name
        user.checkInDays = 0
        user.createdAt = Date()
        user.updatedAt = Date()
        return user
    }
    
    private func createInMemoryContext() -> NSManagedObjectContext {
        let container = NSPersistentContainer(name: "DataModel")
        let description = NSPersistentStoreDescription()
        description.type = NSInMemoryStoreType
        container.persistentStoreDescriptions = [description]
        
        container.loadPersistentStores { _, error in
            if let error = error {
                fatalError("Failed to load store: \(error)")
            }
        }
        return container.viewContext
    }
}

// MARK: - 测试辅助方法

extension HomeViewModelTests {
    
    func createMockTasks(count: Int = 3) -> [Task] {
        return (0..<count).map { index in
            createMockTask(title: "任务 \(index + 1)")
        }
    }
    
    func createMockTask(title: String = "测试任务", isCompleted: Bool = false) -> Task {
        let context = createInMemoryContext()
        let task = Task(context: context)
        task.title = title
        task.isCompleted = isCompleted
        task.startTime = Date()
        task.endTime = Date().addingTimeInterval(3600)
        task.priority = Priority.medium.rawValue
        task.tomatoCount = 1
        task.isReminderEnabled = true
        task.createdAt = Date()
        task.updatedAt = Date()
        return task
    }
    
    func createMockUser(name: String = "测试用户", checkInDays: Int32 = 5) -> User {
        let context = createInMemoryContext()
        let user = User(context: context)
        user.name = name
        user.checkInDays = checkInDays
        user.createdAt = Date()
        user.updatedAt = Date()
        return user
    }
    
    /// 创建内存Core Data上下文
    private func createInMemoryContext() -> NSManagedObjectContext {
        let container = NSPersistentContainer(name: "DataModel")
        let description = NSPersistentStoreDescription()
        description.type = NSInMemoryStoreType
        container.persistentStoreDescriptions = [description]
        
        container.loadPersistentStores { _, error in
            if let error = error {
                fatalError("Failed to load store: \(error)")
            }
        }
        return container.viewContext
    }
}