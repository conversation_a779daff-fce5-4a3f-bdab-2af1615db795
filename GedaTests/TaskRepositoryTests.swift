//
//  TaskRepositoryTests.swift
//  GedaTests
//
//  Created by AI Assistant on 2025-07-14.
//

import XCTest
import Combine
import CoreData
@testable import Geda

/// 任务仓库测试类
/// 测试TaskRepository的核心功能
final class TaskRepositoryTests: XCTestCase {
    
    // MARK: - 测试属性
    
    var taskRepository: TaskRepository!
    var mockCoreDataManager: MockCoreDataManager!
    var cancellables: Set<AnyCancellable>!
    
    // MARK: - 测试生命周期
    
    override func setUpWithError() throws {
        super.setUp()
        
        // 创建模拟的Core Data管理器
        mockCoreDataManager = MockCoreDataManager()
        
        // 创建待测试的仓库
        taskRepository = TaskRepository(coreDataManager: mockCoreDataManager)
        
        // 初始化Combine订阅集合
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDownWithError() throws {
        // 清理资源
        taskRepository = nil
        mockCoreDataManager = nil
        cancellables = nil
        
        super.tearDown()
    }
    
    // MARK: - 创建任务测试
    
    func testCreateTask_Success() throws {
        // Given: 准备测试数据
        let title = "测试任务"
        let startTime = Date()
        let endTime = Date().addingTimeInterval(3600)
        let priority = Priority.high
        
        let expectation = XCTestExpectation(description: "创建任务成功")
        
        // When: 执行创建任务操作
        taskRepository.createTask(
            title: title,
            startTime: startTime,
            endTime: endTime,
            priority: priority,
            tomatoCount: 2,
            isReminderEnabled: true
        )
        .sink(
            receiveCompletion: { completion in
                if case .failure(let error) = completion {
                    XCTFail("创建任务失败: \(error)")
                }
            },
            receiveValue: { task in
                // Then: 验证结果
                XCTAssertEqual(task.title, title)
                XCTAssertEqual(task.startTime, startTime)
                XCTAssertEqual(task.endTime, endTime)
                XCTAssertEqual(task.priority, priority.rawValue)
                XCTAssertEqual(task.tomatoCount, 2)
                XCTAssertTrue(task.isReminderEnabled)
                XCTAssertFalse(task.isCompleted)
                
                expectation.fulfill()
            }
        )
        .store(in: &cancellables)
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    func testCreateTask_EmptyTitle_Failure() throws {
        // Given: 空标题
        let expectation = XCTestExpectation(description: "空标题创建任务失败")
        
        // When: 尝试创建空标题任务
        taskRepository.createTask(
            title: "",
            startTime: Date(),
            endTime: Date().addingTimeInterval(3600),
            priority: .medium,
            tomatoCount: 1,
            isReminderEnabled: false
        )
        .sink(
            receiveCompletion: { completion in
                // Then: 应该失败
                if case .failure(let error) = completion {
                    if case RepositoryError.validationError = error {
                        expectation.fulfill()
                    } else {
                        XCTFail("错误类型不正确: \(error)")
                    }
                } else {
                    XCTFail("应该失败但成功了")
                }
            },
            receiveValue: { _ in
                XCTFail("空标题不应该创建成功")
            }
        )
        .store(in: &cancellables)
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    // MARK: - 获取任务测试
    
    func testFetchTasksForDate_Success() throws {
        // Given: 准备测试日期
        let testDate = Date()
        let expectation = XCTestExpectation(description: "获取指定日期任务成功")
        
        // When: 获取指定日期的任务
        taskRepository.fetchTasksForDate(testDate)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        XCTFail("获取任务失败: \(error)")
                    }
                },
                receiveValue: { tasks in
                    // Then: 验证结果
                    XCTAssertTrue(tasks.isEmpty || tasks.allSatisfy { task in
                        Calendar.current.isDate(task.startTime ?? Date(), inSameDayAs: testDate)
                    })
                    
                    expectation.fulfill()
                }
            )
            .store(in: &cancellables)
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    // MARK: - 更新任务状态测试
    
    func testToggleTaskCompletion_Success() throws {
        // Given: 创建一个测试任务
        let task = mockCoreDataManager.createMockTask()
        let originalStatus = task.isCompleted
        
        let expectation = XCTestExpectation(description: "切换任务完成状态成功")
        
        // When: 切换任务完成状态
        taskRepository.toggleTaskCompletion(task)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        XCTFail("切换任务状态失败: \(error)")
                    }
                },
                receiveValue: { updatedTask in
                    // Then: 验证状态已切换
                    XCTAssertEqual(updatedTask.isCompleted, !originalStatus)
                    expectation.fulfill()
                }
            )
            .store(in: &cancellables)
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    // MARK: - 删除任务测试
    
    func testDeleteTask_Success() throws {
        // Given: 创建一个测试任务
        let task = mockCoreDataManager.createMockTask()
        
        let expectation = XCTestExpectation(description: "删除任务成功")
        
        // When: 删除任务
        taskRepository.deleteTask(task)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        XCTFail("删除任务失败: \(error)")
                    } else {
                        expectation.fulfill()
                    }
                },
                receiveValue: { _ in
                    // Then: 删除成功
                }
            )
            .store(in: &cancellables)
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    // MARK: - 性能测试
    
    func testFetchTasksPerformance() throws {
        // 性能测试：批量获取任务
        measure {
            let expectation = XCTestExpectation(description: "性能测试")
            
            taskRepository.fetchTasks()
                .sink(
                    receiveCompletion: { _ in
                        expectation.fulfill()
                    },
                    receiveValue: { _ in }
                )
                .store(in: &cancellables)
            
            wait(for: [expectation], timeout: 0.1)
        }
    }
}

// MARK: - 模拟类

/// 模拟的Core Data管理器
class MockCoreDataManager: CoreDataManagerProtocol {
    
    // MARK: - 内存中的Core Data栈
    lazy var persistentContainer: NSPersistentContainer = {
        let container = NSPersistentContainer(name: "DataModel")
        let description = NSPersistentStoreDescription()
        description.type = NSInMemoryStoreType
        container.persistentStoreDescriptions = [description]
        
        container.loadPersistentStores { _, error in
            if let error = error {
                fatalError("Failed to load store: \(error)")
            }
        }
        return container
    }()
    
    var viewContext: NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    func create<T: NSManagedObject>(_ entityType: T.Type) -> T {
        return T(context: viewContext)
    }
    
    func createMockTask() -> Task {
        let task = Task(context: viewContext)
        task.title = "测试任务"
        task.startTime = Date()
        task.endTime = Date().addingTimeInterval(3600)
        task.priority = Priority.medium.rawValue
        task.tomatoCount = 1
        task.isReminderEnabled = true
        task.isCompleted = false
        task.createdAt = Date()
        task.updatedAt = Date()
        return task
    }
    
    func save() throws {
        if viewContext.hasChanges {
            try viewContext.save()
        }
    }
    
    func fetch<T>(_ request: NSFetchRequest<T>) throws -> [T] {
        return try viewContext.fetch(request)
    }
    
    func delete(_ object: NSManagedObject) {
        viewContext.delete(object)
    }
}


// MARK: - 测试辅助扩展

extension TaskRepositoryTests {
    
    /// 创建测试用的任务数据
    func createTestTaskData() -> (title: String, startTime: Date, endTime: Date, priority: Priority) {
        return (
            title: "测试任务 \(UUID().uuidString.prefix(6))",
            startTime: Date(),
            endTime: Date().addingTimeInterval(3600),
            priority: Priority.allCases.randomElement() ?? .medium
        )
    }
    
    /// 验证任务属性
    func assertTaskProperties(
        task: Task,
        expectedTitle: String,
        expectedStartTime: Date,
        expectedEndTime: Date,
        expectedPriority: Priority
    ) {
        XCTAssertEqual(task.title, expectedTitle, "任务标题不匹配")
        XCTAssertEqual(task.startTime, expectedStartTime, "开始时间不匹配")
        XCTAssertEqual(task.endTime, expectedEndTime, "结束时间不匹配")
        XCTAssertEqual(task.priority, expectedPriority.rawValue, "优先级不匹配")
    }
}