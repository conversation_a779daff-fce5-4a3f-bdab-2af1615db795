//
//  TestProtocols.swift
//  GedaTests
//
//  Created by AI Assistant on 2025/7/19.
//

import Foundation
import Combine
import CoreData
@testable import Geda

// MARK: - Repository协议定义（用于测试）

/// 任务仓库协议
protocol TaskRepositoryProtocol {
    func fetchTasks() -> AnyPublisher<[Task], Error>
    func fetchTodayTasks() -> AnyPublisher<[Task], Error>
    func fetchTasksForDate(_ date: Date) -> AnyPublisher<[Task], Error>
    func fetchTasks(for plan: Plan) -> AnyPublisher<[Task], Error>
    func createTask(title: String, startTime: Date, endTime: Date, priority: Priority, tomatoCount: Int32, isReminderEnabled: Bool) -> AnyPublisher<Task, Error>
    func createTask(_ task: Task) -> AnyPublisher<Task, Error>
    func updateTask(_ task: Task) -> AnyPublisher<Task, Error>
    func deleteTask(_ task: Task) -> AnyPublisher<Void, Error>
    func toggleTaskCompletion(_ task: Task) -> AnyPublisher<Task, Error>
}

/// 用户仓库协议
protocol UserRepositoryProtocol {
    func getCurrentUser() -> AnyPublisher<User?, Error>
    func createUser(name: String, avatar: String) -> AnyPublisher<User, Error>
    func updateUser(_ user: User) -> AnyPublisher<User, Error>
    func deleteUser(_ user: User) -> AnyPublisher<Void, Error>
    func incrementCheckInDays() -> AnyPublisher<User, Error>
}

/// 计划仓库协议
protocol PlanRepositoryProtocol {
    func fetchPlans() -> AnyPublisher<[Plan], Error>
    func fetchActivePlans() -> AnyPublisher<[Plan], Error>
    func fetchPausedPlans() -> AnyPublisher<[Plan], Error>
    func fetchCompletedPlans() -> AnyPublisher<[Plan], Error>
    func createPlan(_ plan: Plan) -> AnyPublisher<Plan, Error>
    func updatePlan(_ plan: Plan) -> AnyPublisher<Plan, Error>
    func deletePlan(_ plan: Plan) -> AnyPublisher<Void, Error>
    func pausePlan(_ plan: Plan) -> AnyPublisher<Plan, Error>
    func resumePlan(_ plan: Plan) -> AnyPublisher<Plan, Error>
    func completePlan(_ plan: Plan) -> AnyPublisher<Plan, Error>
}

// MARK: - Core Data管理器协议

/// Core Data管理器协议（用于依赖注入和测试）
protocol CoreDataManagerProtocol {
    var viewContext: NSManagedObjectContext { get }
    func save() throws
    func fetch<T: NSManagedObject>(_ request: NSFetchRequest<T>) throws -> [T]
    func delete(_ object: NSManagedObject)
    func create<T: NSManagedObject>(_ entityType: T.Type) -> T
}

// MARK: - 扩展现有类以支持协议

extension TaskRepository: TaskRepositoryProtocol {}
extension UserRepository: UserRepositoryProtocol {}
extension PlanRepository: PlanRepositoryProtocol {}
extension CoreDataManager: CoreDataManagerProtocol {
    func create<T: NSManagedObject>(_ entityType: T.Type) -> T {
        return T(context: viewContext)
    }
}