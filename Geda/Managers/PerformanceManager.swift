//
//  PerformanceManager.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/19.
//

import Foundation
import UIKit
import SwiftUI
import os.log

/// 性能监控管理器
/// 负责监控应用性能指标，包括内存使用、启动时间、UI渲染性能等
class PerformanceManager {
    static let shared = PerformanceManager()
    
    // MARK: - 性能指标
    private var startupTime: Date?
    private var memoryWarningCount = 0
    private var viewLoadTimes: [String: TimeInterval] = [:]
    private var dataLoadTimes: [String: TimeInterval] = [:]
    
    // MARK: - 日志
    private let performanceLogger = OSLog(subsystem: "com.ryan.Geda", category: "Performance")
    
    private init() {
        setupMemoryMonitoring()
    }
    
    // MARK: - 启动性能监控
    
    /// 记录应用启动开始时间
    func recordAppLaunchStart() {
        startupTime = Date()
        os_log("🚀 App launch started", log: performanceLogger, type: .info)
    }
    
    /// 记录应用启动完成时间
    func recordAppLaunchComplete() {
        guard let startTime = startupTime else { return }
        let launchTime = Date().timeIntervalSince(startTime)
        
        os_log("✅ App launch completed in %.2f seconds", log: performanceLogger, type: .info, launchTime)
        
        // 如果启动时间超过3秒，记录警告
        if launchTime > 3.0 {
            os_log("⚠️ Slow app launch detected: %.2f seconds", log: performanceLogger, type: .error, launchTime)
        }
    }
    
    // MARK: - 视图加载性能监控
    
    /// 开始监控视图加载
    func startViewLoad(for viewName: String) {
        viewLoadTimes[viewName] = Date().timeIntervalSince1970
        os_log("🔄 Started loading view: %{public}@", log: performanceLogger, type: .debug, viewName)
    }
    
    /// 结束监控视图加载
    func endViewLoad(for viewName: String) {
        guard let startTime = viewLoadTimes[viewName] else { return }
        let loadTime = Date().timeIntervalSince1970 - startTime
        
        os_log("✅ View loaded: %{public}@ in %.3f seconds", log: performanceLogger, type: .info, viewName, loadTime)
        
        // 如果视图加载时间超过1秒，记录警告
        if loadTime > 1.0 {
            os_log("⚠️ Slow view load: %{public}@ took %.3f seconds", log: performanceLogger, type: .error, viewName, loadTime)
        }
        
        viewLoadTimes.removeValue(forKey: viewName)
    }
    
    // MARK: - 数据加载性能监控
    
    /// 开始监控数据加载
    func startDataLoad(for operation: String) {
        dataLoadTimes[operation] = Date().timeIntervalSince1970
        os_log("📊 Started data load: %{public}@", log: performanceLogger, type: .debug, operation)
    }
    
    /// 结束监控数据加载
    func endDataLoad(for operation: String, itemCount: Int = 0) {
        guard let startTime = dataLoadTimes[operation] else { return }
        let loadTime = Date().timeIntervalSince1970 - startTime
        
        if itemCount > 0 {
            os_log("✅ Data loaded: %{public}@ (%d items) in %.3f seconds", log: performanceLogger, type: .info, operation, itemCount, loadTime)
        } else {
            os_log("✅ Data loaded: %{public}@ in %.3f seconds", log: performanceLogger, type: .info, operation, loadTime)
        }
        
        // 如果数据加载时间超过2秒，记录警告
        if loadTime > 2.0 {
            os_log("⚠️ Slow data load: %{public}@ took %.3f seconds", log: performanceLogger, type: .error, operation, loadTime)
        }
        
        dataLoadTimes.removeValue(forKey: operation)
    }
    
    // MARK: - 内存监控
    
    private func setupMemoryMonitoring() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(didReceiveMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    @objc private func didReceiveMemoryWarning() {
        memoryWarningCount += 1
        let memoryInfo = getMemoryInfo()
        
        os_log("⚠️ Memory warning received (count: %d), current usage: %.1f MB", 
               log: performanceLogger, type: .error, memoryWarningCount, memoryInfo.used)
        
        // 清理内存缓存以释放内存
        URLCache.shared.removeAllCachedResponses()
    }
    
    /// 获取当前内存使用情况
    func getMemoryInfo() -> (used: Float, available: Float) {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        let usedMB = Float(info.resident_size) / 1024 / 1024
        let availableMB = Float(ProcessInfo.processInfo.physicalMemory) / 1024 / 1024
        
        if result == KERN_SUCCESS {
            os_log("📱 Memory usage: %.1f MB / %.1f MB", log: performanceLogger, type: .debug, usedMB, availableMB)
        }
        
        return (used: usedMB, available: availableMB)
    }
    
    // MARK: - 性能报告
    
    /// 生成性能报告
    func generatePerformanceReport() -> PerformanceReport {
        let memoryInfo = getMemoryInfo()
        
        return PerformanceReport(
            memoryUsage: memoryInfo.used,
            memoryWarningCount: memoryWarningCount,
            activeViewLoads: viewLoadTimes.count,
            activeDataLoads: dataLoadTimes.count,
            timestamp: Date()
        )
    }
    
    /// 记录自定义性能指标
    func recordCustomMetric(name: String, value: Double, unit: String = "") {
        os_log("📈 Custom metric: %{public}@ = %.3f %{public}@", 
               log: performanceLogger, type: .info, name, value, unit)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - 性能报告数据模型

struct PerformanceReport {
    let memoryUsage: Float // MB
    let memoryWarningCount: Int
    let activeViewLoads: Int
    let activeDataLoads: Int
    let timestamp: Date
    
    var isMemoryHealthy: Bool {
        return memoryUsage < 150.0 && memoryWarningCount < 3
    }
    
    var hasActiveOperations: Bool {
        return activeViewLoads > 0 || activeDataLoads > 0
    }
}

// MARK: - 性能监控装饰器

extension View {
    /// 为视图添加性能监控
    func performanceMonitored(viewName: String) -> some View {
        self.onAppear {
            PerformanceManager.shared.startViewLoad(for: viewName)
        }
        .onDisappear {
            PerformanceManager.shared.endViewLoad(for: viewName)
        }
    }
}

// MARK: - 自动性能优化建议

extension PerformanceManager {
    
    /// 获取性能优化建议
    func getOptimizationSuggestions() -> [String] {
        var suggestions: [String] = []
        let report = generatePerformanceReport()
        
        // 内存优化建议
        if report.memoryUsage > 200.0 {
            suggestions.append("内存使用过高(\(String(format: "%.1f", report.memoryUsage))MB)，建议清理缓存或优化数据结构")
        }
        
        if report.memoryWarningCount > 2 {
            suggestions.append("频繁内存警告(\(report.memoryWarningCount)次)，建议优化内存管理")
        }
        
        // 加载性能建议
        if report.activeViewLoads > 3 {
            suggestions.append("同时加载的视图过多(\(report.activeViewLoads)个)，建议使用懒加载")
        }
        
        if report.activeDataLoads > 5 {
            suggestions.append("同时进行的数据操作过多(\(report.activeDataLoads)个)，建议优化并发策略")
        }
        
        return suggestions
    }
}