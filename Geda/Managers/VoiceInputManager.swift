//
//  VoiceInputManager.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/19.
//

import Foundation
import Speech
import AVFoundation
import Combine

/// 语音输入管理器
/// 负责语音识别、权限管理和语音转文字功能
class VoiceInputManager: NSObject, ObservableObject {
    static let shared = VoiceInputManager()
    
    // MARK: - Published Properties
    @Published var isRecording = false
    @Published var isListening = false
    @Published var recognizedText = ""
    @Published var hasPermission = false
    @Published var errorMessage: String?
    @Published var recordingLevel: Float = 0.0
    
    // MARK: - Private Properties
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioEngine = AVAudioEngine()
    private var recordingTimer: Timer?
    private var maxRecordingDuration: TimeInterval = 60.0 // 最大录音时长60秒
    
    override private init() {
        super.init()
        setupSpeechRecognizer()
        checkPermissions()
    }
    
    // MARK: - Public Methods
    
    /// 开始语音识别
    func startRecording() {
        guard hasPermission else {
            errorMessage = "没有语音识别权限"
            return
        }
        
        guard !audioEngine.isRunning else {
            stopRecording()
            return
        }
        
        do {
            try startSpeechRecognition()
            isRecording = true
            isListening = true
            recognizedText = ""
            errorMessage = nil
            
            // 设置最大录音时长
            recordingTimer = Timer.scheduledTimer(withTimeInterval: maxRecordingDuration, repeats: false) { [weak self] _ in
                self?.stopRecording()
            }
            
            print("开始语音识别...")
        } catch {
            handleError("语音识别启动失败: \(error.localizedDescription)")
        }
    }
    
    /// 停止语音识别
    func stopRecording() {
        audioEngine.stop()
        audioEngine.inputNode.removeTap(onBus: 0)
        
        recognitionRequest?.endAudio()
        recognitionRequest = nil
        recognitionTask?.cancel()
        recognitionTask = nil
        
        recordingTimer?.invalidate()
        recordingTimer = nil
        
        isRecording = false
        isListening = false
        recordingLevel = 0.0
        
        print("停止语音识别")
    }
    
    /// 清除识别的文本
    func clearRecognizedText() {
        recognizedText = ""
        errorMessage = nil
    }
    
    /// 请求语音识别权限
    func requestPermissions() {
        // 请求语音识别权限
        SFSpeechRecognizer.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                switch status {
                case .authorized:
                    self?.requestMicrophonePermission()
                case .denied, .restricted, .notDetermined:
                    self?.hasPermission = false
                    self?.errorMessage = "语音识别权限被拒绝"
                @unknown default:
                    self?.hasPermission = false
                    self?.errorMessage = "语音识别权限状态未知"
                }
            }
        }
    }
    
    /// 解析语音文本为任务信息
    func parseVoiceToTask(text: String) -> VoiceTaskInfo? {
        let lowercasedText = text.lowercased()
        
        // 简单的语音解析逻辑
        var title = text
        var priority = "medium"
        var tomatoCount = 1
        
        // 解析优先级
        if lowercasedText.contains("重要") || lowercasedText.contains("紧急") || lowercasedText.contains("高优先级") {
            priority = "high"
        } else if lowercasedText.contains("不重要") || lowercasedText.contains("低优先级") {
            priority = "low"
        }
        
        // 解析番茄钟数量
        if let match = lowercasedText.range(of: #"\d+个?番茄钟?"#, options: .regularExpression) {
            let matchText = String(lowercasedText[match])
            if let number = Int(matchText.replacingOccurrences(of: #"[^0-9]"#, with: "", options: .regularExpression)) {
                tomatoCount = max(1, min(number, 10)) // 限制在1-10个番茄钟
            }
        }
        
        // 清理标题文本
        title = title.replacingOccurrences(of: #"\d+个?番茄钟?"#, with: "", options: .regularExpression)
        title = title.replacingOccurrences(of: "重要|紧急|高优先级|不重要|低优先级", with: "", options: .regularExpression)
        title = title.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !title.isEmpty else { return nil }
        
        return VoiceTaskInfo(
            title: title,
            priority: priority,
            tomatoCount: Int32(tomatoCount)
        )
    }
    
    // MARK: - Private Methods
    
    private func setupSpeechRecognizer() {
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))
        speechRecognizer?.delegate = self
    }
    
    /// 检查权限状态
    func checkPermissions() {
        let speechStatus = SFSpeechRecognizer.authorizationStatus()
        let microphoneStatus = AVAudioSession.sharedInstance().recordPermission
        
        hasPermission = speechStatus == .authorized && microphoneStatus == .granted
    }
    
    private func requestMicrophonePermission() {
        AVAudioSession.sharedInstance().requestRecordPermission { [weak self] granted in
            DispatchQueue.main.async {
                self?.hasPermission = granted
                if !granted {
                    self?.errorMessage = "麦克风权限被拒绝"
                }
            }
        }
    }
    
    private func startSpeechRecognition() throws {
        // 配置音频会话
        let audioSession = AVAudioSession.sharedInstance()
        try audioSession.setCategory(.record, mode: .measurement, options: .duckOthers)
        try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        
        // 创建识别请求
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            throw VoiceInputError.recognitionRequestFailed
        }
        
        recognitionRequest.shouldReportPartialResults = true
        
        // 配置音频输入
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { [weak self] buffer, _ in
            self?.recognitionRequest?.append(buffer)
            
            // 计算音频电平
            self?.updateRecordingLevel(from: buffer)
        }
        
        // 启动音频引擎
        audioEngine.prepare()
        try audioEngine.start()
        
        // 开始识别
        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            if let result = result {
                DispatchQueue.main.async {
                    self?.recognizedText = result.bestTranscription.formattedString
                }
            }
            
            if let error = error {
                DispatchQueue.main.async {
                    self?.handleError("语音识别错误: \(error.localizedDescription)")
                    self?.stopRecording()
                }
            }
        }
    }
    
    private func updateRecordingLevel(from buffer: AVAudioPCMBuffer) {
        guard let channelData = buffer.floatChannelData?[0] else { return }
        
        let frames = buffer.frameLength
        var sum: Float = 0
        
        for i in 0..<Int(frames) {
            sum += abs(channelData[i])
        }
        
        let averageLevel = sum / Float(frames)
        
        DispatchQueue.main.async {
            self.recordingLevel = averageLevel
        }
    }
    
    private func handleError(_ message: String) {
        errorMessage = message
        stopRecording()
        print("VoiceInputManager Error: \(message)")
    }
}

// MARK: - SFSpeechRecognizerDelegate

extension VoiceInputManager: SFSpeechRecognizerDelegate {
    func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        DispatchQueue.main.async {
            if !available {
                self.handleError("语音识别服务不可用")
            }
        }
    }
}

// MARK: - 数据模型

/// 语音解析的任务信息
struct VoiceTaskInfo {
    let title: String
    let priority: String
    let tomatoCount: Int32
}

/// 语音输入错误类型
enum VoiceInputError: LocalizedError {
    case recognitionRequestFailed
    case permissionDenied
    case audioEngineFailed
    
    var errorDescription: String? {
        switch self {
        case .recognitionRequestFailed:
            return "创建语音识别请求失败"
        case .permissionDenied:
            return "语音识别权限被拒绝"
        case .audioEngineFailed:
            return "音频引擎启动失败"
        }
    }
}