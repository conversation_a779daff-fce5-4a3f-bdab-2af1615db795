//
//  StudyModeManager.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/19.
//

import Foundation
import Combine
import UserNotifications
import AVFoundation
import AudioToolbox

/// 学霸模式管理器
/// 负责管理专注会话、通知屏蔽、计时器等核心功能
class StudyModeManager: ObservableObject {
    static let shared = StudyModeManager()
    
    // MARK: - Published Properties
    @Published var isStudyModeActive = false
    @Published var currentSessionDuration: TimeInterval = 0
    @Published var remainingTime: TimeInterval = 0
    @Published var sessionStartTime: Date?
    @Published var isNotificationEnabled = true
    @Published var isSoundEnabled = true
    
    // MARK: - Private Properties
    private var sessionTimer: Timer?
    private var audioPlayer: AVAudioPlayer?
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupAudio()
        setupNotifications()
    }
    
    // MARK: - Public Methods
    
    /// 开始学霸模式会话
    /// - Parameters:
    ///   - duration: 专注时长（秒）
    ///   - enableNotifications: 是否启用通知
    ///   - enableSound: 是否启用声音
    func startStudySession(duration: TimeInterval, enableNotifications: Bool, enableSound: Bool) {
        // 设置会话参数
        currentSessionDuration = duration
        remainingTime = duration
        sessionStartTime = Date()
        isNotificationEnabled = enableNotifications
        isSoundEnabled = enableSound
        isStudyModeActive = true
        
        // 启动计时器
        startTimer()
        
        // 请求通知权限并设置通知
        if enableNotifications {
            scheduleSessionEndNotification()
        }
        
        // 记录会话开始
        logSessionStart()
        
        print("学霸模式已开启 - 时长: \(Int(duration/60))分钟")
    }
    
    /// 暂停学霸模式会话
    func pauseStudySession() {
        sessionTimer?.invalidate()
        sessionTimer = nil
        
        // 取消待发送的通知
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: ["study_session_end"])
        
        print("学霸模式已暂停")
    }
    
    /// 恢复学霸模式会话
    func resumeStudySession() {
        if isStudyModeActive && remainingTime > 0 {
            startTimer()
            
            // 重新安排通知
            if isNotificationEnabled {
                scheduleSessionEndNotification()
            }
            
            print("学霸模式已恢复")
        }
    }
    
    /// 结束学霸模式会话
    func endStudySession() {
        sessionTimer?.invalidate()
        sessionTimer = nil
        
        let completedDuration = currentSessionDuration - remainingTime
        
        // 记录会话结束
        logSessionEnd(completedDuration: completedDuration)
        
        // 播放完成提示音
        if isSoundEnabled {
            playCompletionSound()
        }
        
        // 发送完成通知
        if isNotificationEnabled {
            sendSessionCompletedNotification()
        }
        
        // 重置状态
        resetSession()
        
        print("学霸模式已结束 - 完成时长: \(Int(completedDuration/60))分钟")
    }
    
    /// 强制停止学霸模式会话
    func forceStopStudySession() {
        sessionTimer?.invalidate()
        sessionTimer = nil
        
        // 取消通知
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: ["study_session_end"])
        
        // 记录会话中止
        let completedDuration = currentSessionDuration - remainingTime
        logSessionAborted(completedDuration: completedDuration)
        
        // 重置状态
        resetSession()
        
        print("学霸模式已强制停止")
    }
    
    /// 获取当前会话进度（0.0 - 1.0）
    func getSessionProgress() -> Double {
        guard currentSessionDuration > 0 else { return 0.0 }
        let completedTime = currentSessionDuration - remainingTime
        return completedTime / currentSessionDuration
    }
    
    /// 获取已完成时间的格式化字符串
    func getCompletedTimeString() -> String {
        let completedTime = currentSessionDuration - remainingTime
        return formatTime(completedTime)
    }
    
    /// 获取剩余时间的格式化字符串
    func getRemainingTimeString() -> String {
        return formatTime(remainingTime)
    }
    
    // MARK: - Private Methods
    
    private func setupAudio() {
        // 设置音频会话
        do {
            try AVAudioSession.sharedInstance().setCategory(.ambient, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("音频会话设置失败: \(error)")
        }
    }
    
    private func setupNotifications() {
        // 请求通知权限
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if let error = error {
                print("通知权限请求失败: \(error)")
            } else if granted {
                print("通知权限已授予")
            } else {
                print("通知权限被拒绝")
            }
        }
    }
    
    private func startTimer() {
        sessionTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateTimer()
        }
    }
    
    private func updateTimer() {
        if remainingTime > 0 {
            remainingTime -= 1
        } else {
            // 会话自然结束
            endStudySession()
        }
    }
    
    private func scheduleSessionEndNotification() {
        let content = UNMutableNotificationContent()
        content.title = "专注时间结束"
        content.body = "恭喜您完成了 \(Int(currentSessionDuration/60)) 分钟的专注学习！"
        content.sound = isSoundEnabled ? .default : nil
        content.badge = 1
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: remainingTime, repeats: false)
        let request = UNNotificationRequest(identifier: "study_session_end", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("通知安排失败: \(error)")
            }
        }
    }
    
    private func sendSessionCompletedNotification() {
        let content = UNMutableNotificationContent()
        content.title = "学霸模式完成"
        content.body = "太棒了！您刚刚完成了 \(Int(currentSessionDuration/60)) 分钟的专注学习。"
        content.sound = isSoundEnabled ? .default : nil
        content.badge = 1
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(identifier: "study_session_completed", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("完成通知发送失败: \(error)")
            }
        }
    }
    
    private func playCompletionSound() {
        // 在实际应用中，应该使用项目中的音频文件
        // 这里使用系统声音作为示例
        AudioServicesPlaySystemSound(SystemSoundID(1322)) // 玻璃声音
    }
    
    private func resetSession() {
        isStudyModeActive = false
        currentSessionDuration = 0
        remainingTime = 0
        sessionStartTime = nil
    }
    
    private func logSessionStart() {
        // 在实际应用中，这里应该记录到数据库或Analytics
        let log = StudySessionLog(
            startTime: sessionStartTime ?? Date(),
            plannedDuration: currentSessionDuration,
            sessionType: .focus
        )
        print("会话开始记录: \(log)")
    }
    
    private func logSessionEnd(completedDuration: TimeInterval) {
        // 在实际应用中，这里应该记录到数据库或Analytics
        let log = StudySessionLog(
            startTime: sessionStartTime ?? Date(),
            endTime: Date(),
            plannedDuration: currentSessionDuration,
            actualDuration: completedDuration,
            sessionType: .focus,
            completed: true
        )
        print("会话完成记录: \(log)")
    }
    
    private func logSessionAborted(completedDuration: TimeInterval) {
        // 在实际应用中，这里应该记录到数据库或Analytics
        let log = StudySessionLog(
            startTime: sessionStartTime ?? Date(),
            endTime: Date(),
            plannedDuration: currentSessionDuration,
            actualDuration: completedDuration,
            sessionType: .focus,
            completed: false
        )
        print("会话中止记录: \(log)")
    }
    
    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let totalSeconds = Int(timeInterval)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
}

// MARK: - 数据模型

/// 学习会话记录
struct StudySessionLog {
    let id = UUID()
    let startTime: Date
    var endTime: Date?
    let plannedDuration: TimeInterval
    var actualDuration: TimeInterval?
    let sessionType: StudySessionType
    var completed: Bool = false
    
    init(startTime: Date, endTime: Date? = nil, plannedDuration: TimeInterval, actualDuration: TimeInterval? = nil, sessionType: StudySessionType, completed: Bool = false) {
        self.startTime = startTime
        self.endTime = endTime
        self.plannedDuration = plannedDuration
        self.actualDuration = actualDuration
        self.sessionType = sessionType
        self.completed = completed
    }
}

/// 学习会话类型
enum StudySessionType {
    case focus      // 专注学习
    case rest       // 休息时间
    case review     // 复盘时间
}

