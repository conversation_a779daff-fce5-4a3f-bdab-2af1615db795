//
//  SignInView.swift
//  Geda
//
//  Created by AI Assistant on 2025-07-22.
//

import SwiftUI
import AuthenticationServices

/// Apple ID登录界面
struct SignInView: View {
    @StateObject private var authService = AuthenticationService.shared
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var tapCount = 0  // 用于隐藏功能的点击计数
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                LinearGradient(
                    colors: [
                        Color.gedaAccentStart.opacity(0.1),
                        Color.gedaAccentEnd.opacity(0.05),
                        Color.gedaBgShell
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 0) {
                        Spacer(minLength: geometry.size.height * 0.15)
                        
                        // 顶部Logo和标题区域
                        logoSection
                        
                        Spacer(minLength: geometry.size.height * 0.1)
                        
                        // 功能介绍区域
                        featuresSection
                        
                        Spacer(minLength: geometry.size.height * 0.1)
                        
                        // 登录按钮区域
                        signInSection
                        
                        Spacer(minLength: 40)
                        
                        // 底部说明
                        footerSection
                        
                        Spacer(minLength: geometry.size.height * 0.1)
                    }
                    .frame(minHeight: geometry.size.height)
                }
            }
        }
        .navigationBarHidden(true)
        .alert("登录失败", isPresented: .constant(errorMessage != nil)) {
            Button("确定") {
                errorMessage = nil
            }
        } message: {
            if let errorMessage = errorMessage {
                Text(errorMessage)
            }
        }
        .onReceive(authService.$authenticationState) { state in
            handleAuthenticationState(state)
        }
    }
    
    // MARK: - 子视图
    
    private var logoSection: some View {
        VStack(spacing: 20) {
            // App图标
            ZStack {
                Circle()
                    .fill(LinearGradient.gedaGradient)
                    .frame(width: 120, height: 120)
                    .shadow(color: .gedaAccentStart.opacity(0.3), radius: 20, x: 0, y: 10)
                
                Image(systemName: "timer")
                    .font(.system(size: 50, weight: .light))
                    .foregroundColor(.white)
            }
            
            // App名称和标语
            VStack(spacing: 8) {
                Text("咯嗒")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.gedaTextPrimary)
                    .onTapGesture {
                        tapCount += 1
                        if tapCount >= 5 {
                            // 连续点击5次开启测试模式
                            authService.enableTestMode()
                            tapCount = 0
                        }
                    }
                
                Text("AI驱动的智能学习助手")
                    .font(.headline)
                    .fontWeight(.medium)
                    .foregroundColor(.gedaTextSecondary)
                    .multilineTextAlignment(.center)
                
                Text("专注 · 计划 · 成长")
                    .font(.subheadline)
                    .foregroundColor(.gedaTextTertiary)
                    .multilineTextAlignment(.center)
                    .padding(.top, 4)
                
                // 测试模式提示
                if authService.testModeEnabled {
                    Text("🔧 测试模式已启用")
                        .font(.caption)
                        .foregroundColor(.orange)
                        .padding(.top, 8)
                }
            }
        }
        .padding(.horizontal, 32)
    }
    
    private var featuresSection: some View {
        VStack(spacing: 24) {
            // 功能特性列表
            VStack(spacing: 20) {
                FeatureRow(
                    icon: "brain.head.profile",
                    title: "AI智能规划",
                    description: "一句话生成完整学习计划"
                )
                
                FeatureRow(
                    icon: "timer.circle",
                    title: "番茄钟专注",
                    description: "科学的专注时间管理方法"
                )
                
                FeatureRow(
                    icon: "icloud.and.arrow.up",
                    title: "云端同步",
                    description: "多设备无缝数据同步"
                )
                
                FeatureRow(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "学习分析",
                    description: "深度分析学习效果和进步"
                )
            }
        }
        .padding(.horizontal, 32)
    }
    
    private var signInSection: some View {
        VStack(spacing: 20) {
            // Apple ID登录按钮
            SignInWithAppleButton(.signIn) { request in
                request.requestedScopes = [.fullName, .email]
                isLoading = true
                authService.signInWithApple()
            } onCompletion: { result in
                isLoading = false
                // AuthenticationService会处理登录结果
            }
            .signInWithAppleButtonStyle(.black)
            .frame(height: 50)
            .cornerRadius(8)
            .disabled(isLoading)
            
            // 加载指示器
            if isLoading {
                HStack(spacing: 8) {
                    ProgressView()
                        .scaleEffect(0.8)
                    
                    Text("正在登录...")
                        .font(.subheadline)
                        .foregroundColor(.gedaTextSecondary)
                }
                .padding(.top, 8)
            }
            
            // 测试模式快速按钮（仅在Debug模式下显示）
            #if DEBUG
            if !authService.testModeEnabled {
                Button("🚀 启用测试模式（开发者）") {
                    authService.enableTestMode()
                }
                .font(.caption)
                .foregroundColor(.orange)
                .padding(.top, 16)
            }
            #endif
            
            // 登录说明
            VStack(spacing: 8) {
                Text("登录后即可享受完整功能")
                    .font(.subheadline)
                    .foregroundColor(.gedaTextSecondary)
                
                Text("• 数据云端安全存储  • 多设备同步  • 个人学习分析")
                    .font(.caption)
                    .foregroundColor(.gedaTextTertiary)
                    .multilineTextAlignment(.center)
            }
            .padding(.top, 16)
        }
        .padding(.horizontal, 32)
    }
    
    private var footerSection: some View {
        VStack(spacing: 12) {
            HStack(spacing: 4) {
                Text("登录即表示同意")
                    .font(.caption)
                    .foregroundColor(.gedaTextTertiary)
                
                Button("《服务条款》") {
                    // 打开服务条款
                }
                .font(.caption)
                .foregroundColor(.gedaAccentStart)
                
                Text("和")
                    .font(.caption)
                    .foregroundColor(.gedaTextTertiary)
                
                Button("《隐私政策》") {
                    // 打开隐私政策
                }
                .font(.caption)
                .foregroundColor(.gedaAccentStart)
            }
            
            Text("我们承诺保护您的隐私和数据安全")
                .font(.caption2)
                .foregroundColor(.gedaTextTertiary)
                .multilineTextAlignment(.center)
        }
        .padding(.horizontal, 32)
    }
    
    // MARK: - 方法
    
    private func handleAuthenticationState(_ state: AuthenticationService.AuthenticationState) {
        switch state {
        case .authenticating:
            isLoading = true
            errorMessage = nil
        case .authenticated:
            isLoading = false
            errorMessage = nil
        case .notAuthenticated:
            isLoading = false
        case .error(let error):
            isLoading = false
            errorMessage = error.localizedDescription
        case .unknown:
            isLoading = false
        }
    }
}

/// 功能特性行组件
struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            // 图标
            ZStack {
                Circle()
                    .fill(Color.gedaAccentStart.opacity(0.1))
                    .frame(width: 48, height: 48)
                
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.gedaAccentStart)
            }
            
            // 文本内容
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.gedaTextPrimary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.gedaTextSecondary)
                    .multilineTextAlignment(.leading)
            }
            
            Spacer()
        }
        .padding(.horizontal, 4)
    }
}

/// 离线模式提示视图
struct OfflineModeView: View {
    @Binding var showOfflineMode: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "wifi.slash")
                .font(.system(size: 48))
                .foregroundColor(.gedaTextSecondary)
            
            VStack(spacing: 8) {
                Text("网络连接不可用")
                    .font(.headline)
                    .fontWeight(.medium)
                    .foregroundColor(.gedaTextPrimary)
                
                Text("您可以继续使用离线模式，但部分功能将受限")
                    .font(.subheadline)
                    .foregroundColor(.gedaTextSecondary)
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 12) {
                Button("进入离线模式") {
                    showOfflineMode = true
                }
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color.gedaAccentStart)
                .cornerRadius(8)
                
                Button("重试连接") {
                    // 重新检查网络连接
                    AuthenticationService.shared.checkAuthenticationState()
                }
                .font(.subheadline)
                .foregroundColor(.gedaAccentStart)
            }
        }
        .padding(32)
    }
}

#Preview {
    SignInView()
}