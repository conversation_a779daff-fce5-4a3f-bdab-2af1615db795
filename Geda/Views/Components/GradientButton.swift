//
//  GradientButton.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI

/// GradientButton组件 - 对应HTML原型中的gradient-button CSS类
/// 提供统一的渐变按钮样式和行为
struct GradientButton: View {
    
    // MARK: - 属性
    let title: String
    let icon: String?
    let style: ButtonStyle
    let size: ButtonSize
    let isEnabled: Bool
    let isLoading: Bool
    let action: () -> Void
    
    // MARK: - 按钮样式枚举
    enum ButtonStyle {
        case primary        // 主要按钮 (渐变背景)
        case secondary      // 次要按钮 (灰色背景)
        case outline        // 轮廓按钮 (透明背景，渐变边框)
        case ghost          // 幽灵按钮 (透明背景，渐变文字)
        case danger         // 危险按钮 (红色背景)
        case success        // 成功按钮 (绿色背景)
        
        var background: AnyView {
            switch self {
            case .primary:
                return AnyView(LinearGradient.gedaGradient)
            case .secondary:
                return AnyView(Color.gedaGray100)
            case .outline, .ghost:
                return AnyView(Color.clear)
            case .danger:
                return AnyView(Color.gedaError)
            case .success:
                return AnyView(Color.gedaSuccess)
            }
        }
        
        var foregroundColor: Color {
            switch self {
            case .primary, .danger, .success:
                return .white
            case .secondary:
                return .gedaTextPrimary
            case .outline, .ghost:
                return .gedaAccentStart
            }
        }
        
        var borderColor: Color? {
            switch self {
            case .outline:
                return .gedaAccentStart
            default:
                return nil
            }
        }
        
        var hasShadow: Bool {
            switch self {
            case .primary, .danger, .success:
                return true
            default:
                return false
            }
        }
    }
    
    // MARK: - 按钮尺寸枚举
    enum ButtonSize {
        case small
        case medium
        case large
        
        var height: CGFloat {
            switch self {
            case .small:
                return DesignTokens.Size.buttonHeightSmall
            case .medium:
                return DesignTokens.Size.buttonHeight
            case .large:
                return DesignTokens.Size.buttonHeightLarge
            }
        }
        
        var font: Font {
            switch self {
            case .small:
                return .gedaButtonSmall
            case .medium, .large:
                return .gedaButton
            }
        }
        
        var iconSize: CGFloat {
            switch self {
            case .small:
                return DesignTokens.Size.iconSM
            case .medium:
                return DesignTokens.Size.iconMD
            case .large:
                return DesignTokens.Size.iconLG
            }
        }
        
        var padding: EdgeInsets {
            switch self {
            case .small:
                return EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16)
            case .medium:
                return EdgeInsets(top: 12, leading: 20, bottom: 12, trailing: 20)
            case .large:
                return EdgeInsets(top: 16, leading: 24, bottom: 16, trailing: 24)
            }
        }
    }
    
    // MARK: - 初始化
    init(
        _ title: String,
        icon: String? = nil,
        style: ButtonStyle = .primary,
        size: ButtonSize = .medium,
        isEnabled: Bool = true,
        isLoading: Bool = false,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.style = style
        self.size = size
        self.isEnabled = isEnabled
        self.isLoading = isLoading
        self.action = action
    }
    
    // MARK: - 视图主体
    var body: some View {
        Button(action: {
            if isEnabled && !isLoading {
                action()
            }
        }) {
            buttonContent
        }
        .buttonStyle(GradientButtonStyle(
            style: style,
            size: size,
            isEnabled: isEnabled
        ))
        .disabled(!isEnabled || isLoading)
    }
    
    // MARK: - 按钮内容
    @ViewBuilder
    private var buttonContent: some View {
        HStack(spacing: DesignTokens.Spacing.sm) {
            if isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: style.foregroundColor))
                    .scaleEffect(0.8)
            } else if let icon = icon {
                Image(systemName: icon)
                    .font(.system(size: size.iconSize, weight: .medium))
            }
            
            if !title.isEmpty {
                Text(title)
                    .font(size.font)
                    .lineLimit(1)
            }
        }
        .foregroundColor(style.foregroundColor)
        .padding(size.padding)
        .frame(minHeight: size.height)
    }
}

// MARK: - 按钮样式 - 精确复刻CSS active:scale-90效果
struct GradientButtonStyle: ButtonStyle {
    let style: GradientButton.ButtonStyle
    let size: GradientButton.ButtonSize
    let isEnabled: Bool
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .background(backgroundView)
            .cornerRadius(DesignTokens.CornerRadius.button)
            .overlay(overlayView)
            .scaleEffect(configuration.isPressed ? 0.9 : 1.0)  // 对应CSS active:scale-90
            .opacity(isEnabled ? (configuration.isPressed ? 0.9 : 1.0) : 0.6)
            .if(style.hasShadow && isEnabled) { view in
                view.shadow(
                    color: shadowColor(isPressed: configuration.isPressed),
                    radius: shadowRadius(isPressed: configuration.isPressed),
                    x: 0,
                    y: shadowY(isPressed: configuration.isPressed)
                )
            }
            .animation(.easeInOut(duration: 0.15), value: configuration.isPressed)  // 对应CSS transition-all duration-150
    }
    
    // 动态阴影颜色 - 根据按钮样式和按压状态调整
    private func shadowColor(isPressed: Bool) -> Color {
        switch style {
        case .primary:
            return Color.gedaAccentStart.opacity(isPressed ? 0.2 : 0.3)  // 渐变按钮使用品牌色阴影
        case .danger:
            return Color.gedaError.opacity(isPressed ? 0.15 : 0.25)
        case .success:
            return Color.gedaSuccess.opacity(isPressed ? 0.15 : 0.25)
        default:
            return Color.black.opacity(isPressed ? 0.1 : 0.15)
        }
    }
    
    // 动态阴影半径 - 按压时收缩
    private func shadowRadius(isPressed: Bool) -> CGFloat {
        return isPressed ? 6 : 8
    }
    
    // 动态阴影Y偏移 - 按压时向上收缩
    private func shadowY(isPressed: Bool) -> CGFloat {
        return isPressed ? 2 : 4
    }
    
    @ViewBuilder
    private var backgroundView: some View {
        style.background
    }
    
    @ViewBuilder
    private var overlayView: some View {
        if let borderColor = style.borderColor {
            RoundedRectangle(cornerRadius: DesignTokens.CornerRadius.button)
                .stroke(borderColor, lineWidth: 1.5)
        }
    }
}

// MARK: - 便捷构造器
extension GradientButton {
    
    /// 主要操作按钮
    static func primary(
        _ title: String,
        icon: String? = nil,
        isEnabled: Bool = true,
        isLoading: Bool = false,
        action: @escaping () -> Void
    ) -> GradientButton {
        GradientButton(
            title,
            icon: icon,
            style: .primary,
            isEnabled: isEnabled,
            isLoading: isLoading,
            action: action
        )
    }
    
    /// 次要操作按钮
    static func secondary(
        _ title: String,
        icon: String? = nil,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) -> GradientButton {
        GradientButton(
            title,
            icon: icon,
            style: .secondary,
            isEnabled: isEnabled,
            action: action
        )
    }
    
    /// 轮廓按钮
    static func outline(
        _ title: String,
        icon: String? = nil,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) -> GradientButton {
        GradientButton(
            title,
            icon: icon,
            style: .outline,
            isEnabled: isEnabled,
            action: action
        )
    }
    
    /// 危险操作按钮
    static func danger(
        _ title: String,
        icon: String? = "trash",
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) -> GradientButton {
        GradientButton(
            title,
            icon: icon,
            style: .danger,
            isEnabled: isEnabled,
            action: action
        )
    }
}

// MARK: - 预览
#Preview("GradientButton Styles") {
    ScrollView {
        VStack(spacing: DesignTokens.Spacing.lg) {
            // 主要按钮
            GradientButton.primary("主要按钮", icon: "plus") {
                print("主要按钮点击")
            }
            
            // 次要按钮
            GradientButton.secondary("次要按钮", icon: "gear") {
                print("次要按钮点击")
            }
            
            // 轮廓按钮
            GradientButton.outline("轮廓按钮") {
                print("轮廓按钮点击")
            }
            
            // 危险按钮
            GradientButton.danger("删除", icon: "trash") {
                print("删除按钮点击")
            }
            
            // 加载状态
            GradientButton.primary("加载中", isLoading: true) {
                print("加载按钮点击")
            }
            
            // 禁用状态
            GradientButton.primary("禁用按钮", isEnabled: false) {
                print("禁用按钮点击")
            }
            
            // 不同尺寸
            VStack(spacing: DesignTokens.Spacing.sm) {
                GradientButton("小按钮", style: .primary, size: .small) {
                    print("小按钮点击")
                }
                
                GradientButton("中等按钮", style: .primary, size: .medium) {
                    print("中等按钮点击")
                }
                
                GradientButton("大按钮", style: .primary, size: .large) {
                    print("大按钮点击")
                }
            }
        }
        .padding()
    }
    .background(Color.gedaBgShell)
}
