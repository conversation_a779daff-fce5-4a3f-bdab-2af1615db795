//
//  StackedTaskCards.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI
import CoreData

/// 堆叠任务卡片组件 - 精确复刻CSS堆叠动画效果
/// 对应HTML原型中的tasks-expanded CSS类动画
/// 精确映射CSS: task-card-1 (z-index: 30), task-card-2 (scale(0.95) translateX(16px)), task-card-3 (scale(0.9) translateX(32px))
struct StackedTaskCards: View {
    let tasks: [Task]
    let onTaskTap: ((Task) -> Void)?
    let onTaskComplete: ((Task) -> Void)?
    @State private var isExpanded = false
    @State private var dragOffset: CGSize = .zero
    
    // 精确的CSS映射配置
    private let maxVisibleCards = 3  // 对应task-card-1/2/3
    private let cardHeight: CGFloat = 128  // 对应CSS height: 128px
    private let expandedCardSpacing: CGFloat = 144  // 对应CSS top: 144px间距
    
    // CSS cubic-bezier(0.4, 0, 0.2, 1) 动画配置
    private let animationDuration: Double = 0.4  // 对应CSS transition: 0.4s
    private let springResponse: Double = 0.4
    private let springDamping: Double = 0.8
    
    // CSS精确的变换值
    private let card2Scale: CGFloat = 0.95  // 对应CSS scale(0.95)
    private let card2OffsetX: CGFloat = 16   // 对应CSS translateX(16px)
    private let card3Scale: CGFloat = 0.9    // 对应CSS scale(0.9)
    private let card3OffsetX: CGFloat = 32   // 对应CSS translateX(32px)
    
    init(
        tasks: [Task],
        onTaskTap: ((Task) -> Void)? = nil,
        onTaskComplete: ((Task) -> Void)? = nil
    ) {
        self.tasks = tasks
        self.onTaskTap = onTaskTap
        self.onTaskComplete = onTaskComplete
    }
    
    var body: some View {
        VStack(spacing: 0) {
            ZStack {
                ForEach(Array(tasks.prefix(maxVisibleCards).enumerated()), id: \.element.objectID) { index, task in
                    TaskCard(
                        task: task,
                        onTaskTap: { task in
                            onTaskTap?(task)
                        },
                        onTaskComplete: onTaskComplete
                    )
                    .scaleEffect(calculatePreciseScale(for: index))
                    .offset(calculatePreciseOffset(for: index))
                    .zIndex(calculateZIndex(for: index))
                    .opacity(calculatePreciseOpacity(for: index))
                    .background(calculateBackgroundColor(for: index))
                    .shadow(
                        color: Color.black.opacity(0.1),
                        radius: 8,
                        x: 0,
                        y: 4
                    )
                    .animation(
                        .timingCurve(0.4, 0, 0.2, 1, duration: animationDuration)
                        .delay(isExpanded ? Double(index) * 0.05 : 0),
                        value: isExpanded
                    )
                }
            }
            .frame(height: calculateContainerHeight())
            .clipped()
            .onTapGesture {
                toggleExpansion()
            }
            
            // 展开/折叠控制器 - 对应CSS中的collapse-tasks-wrapper
            HStack {
                if !isExpanded && tasks.count > maxVisibleCards {
                    Text("+\(tasks.count - maxVisibleCards)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.secondary.opacity(0.1))
                        .clipShape(Capsule())
                        .transition(.opacity.combined(with: .scale))
                }
                
                Spacer()
                
                Button(action: {
                    toggleExpansion()
                }) {
                    Image(systemName: isExpanded ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(.accentColor)
                }
                .opacity(isExpanded ? 1 : 0.8)
                .animation(.easeInOut(duration: 0.2), value: isExpanded)
            }
            .padding(.top, 12)
            .padding(.horizontal, 16)
            .opacity(tasks.isEmpty ? 0 : (isExpanded ? 1 : 0.7))
            .animation(.easeInOut(duration: 0.3), value: isExpanded)
        }
    }
    
    // MARK: - 精确CSS动画计算方法
    
    /// 计算精确的缩放比例 - 对应CSS transform: scale()
    private func calculatePreciseScale(for index: Int) -> CGFloat {
        if isExpanded {
            return 1.0  // 对应CSS: tasks-expanded .task-card { transform: scale(1) }
        } else {
            switch index {
            case 0: return 1.0      // task-card-1: 无缩放
            case 1: return card2Scale  // task-card-2: scale(0.95)
            case 2: return card3Scale  // task-card-3: scale(0.9)
            default: return 0.85
            }
        }
    }
    
    /// 计算精确的偏移量 - 对应CSS transform: translateX() 和 top 属性
    private func calculatePreciseOffset(for index: Int) -> CGSize {
        if isExpanded {
            // 展开状态：垂直排列，对应CSS中的top属性设置
            let yOffset: CGFloat
            switch index {
            case 0: yOffset = 0                    // task-card-1: top: 0
            case 1: yOffset = expandedCardSpacing  // task-card-2: top: 144px
            case 2: yOffset = expandedCardSpacing * 2  // task-card-3: top: 288px
            default: yOffset = CGFloat(index) * expandedCardSpacing
            }
            return CGSize(width: 0, height: yOffset)  // 展开时X偏移为0
        } else {
            // 堆叠状态：水平偏移，对应CSS translateX()
            let xOffset: CGFloat
            switch index {
            case 0: xOffset = 0              // task-card-1: 无偏移
            case 1: xOffset = card2OffsetX   // task-card-2: translateX(16px)
            case 2: xOffset = card3OffsetX   // task-card-3: translateX(32px)
            default: xOffset = CGFloat(index * 16)
            }
            return CGSize(width: xOffset, height: 0)  // 堆叠时Y偏移为0
        }
    }
    
    /// 计算Z轴层级 - 对应CSS z-index
    private func calculateZIndex(for index: Int) -> Double {
        switch index {
        case 0: return 30  // task-card-1: z-index: 30
        case 1: return 20  // task-card-2: z-index: 20
        case 2: return 10  // task-card-3: z-index: 10
        default: return Double(10 - index)
        }
    }
    
    /// 计算精确的透明度 - 对应CSS background-color rgba透明度
    private func calculatePreciseOpacity(for index: Int) -> Double {
        if isExpanded {
            return 1.0  // 展开时所有卡片完全不透明
        } else {
            switch index {
            case 0: return 1.0   // task-card-1: 完全不透明
            case 1: return 0.8   // task-card-2: rgba(255,255,255,0.8)对应的效果
            case 2: return 0.6   // task-card-3: rgba(255,255,255,0.6)对应的效果
            default: return 0.4
            }
        }
    }
    
    /// 计算背景颜色 - 对应CSS background-color
    private func calculateBackgroundColor(for index: Int) -> Color {
        if isExpanded {
            return Color.clear  // 展开时使用卡片本身的背景
        } else {
            switch index {
            case 0: return Color.clear  // task-card-1: 正常背景
            case 1: return Color.white.opacity(0.2)  // task-card-2: 轻微白色叠加
            case 2: return Color.white.opacity(0.4)  // task-card-3: 更多白色叠加
            default: return Color.white.opacity(0.6)
            }
        }
    }
    
    /// 计算容器高度 - 对应CSS height属性变化
    private func calculateContainerHeight() -> CGFloat {
        if isExpanded {
            // 展开状态高度：对应CSS .tasks-expanded { height: 416px }
            let visibleCount = min(tasks.count, maxVisibleCards)
            return CGFloat(visibleCount - 1) * expandedCardSpacing + cardHeight
        } else {
            // 堆叠状态高度：对应CSS .tasks-container { height: 128px }
            return cardHeight
        }
    }
    
    // MARK: - 交互处理方法
    
    /// 切换展开/折叠状态 - 使用精确的CSS cubic-bezier动画
    private func toggleExpansion() {
        withAnimation(
            .timingCurve(0.4, 0, 0.2, 1, duration: animationDuration)
        ) {
            isExpanded.toggle()
        }
        
        // 触觉反馈
        let impactGenerator = UIImpactFeedbackGenerator(style: .light)
        impactGenerator.impactOccurred()
    }
}

#Preview {
    let context = CoreDataManager.shared.viewContext
    let sampleTasks: [Task] = []
    
    return StackedTaskCards(tasks: sampleTasks)
        .padding()
        .environment(\.managedObjectContext, context)
}
