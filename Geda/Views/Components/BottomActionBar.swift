//
//  BottomActionBar.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI

/// 底部操作栏组件 - 实现毛玻璃效果和精确动画
/// 对应HTML原型中的底部fixed操作栏
struct BottomActionBar: View {
    @Binding var showAddMenu: Bool
    let onNewTask: (() -> Void)?
    let onNewPlan: (() -> Void)?
    let onNewReminder: (() -> Void)?
    let onVoiceInput: (() -> Void)?
    
    init(showAddMenu: Binding<Bool>, 
         onNewTask: (() -> Void)? = nil,
         onNewPlan: (() -> Void)? = nil,
         onVoiceInput: (() -> Void)? = nil) {
        self._showAddMenu = showAddMenu
        self.onNewTask = onNewTask
        self.onNewPlan = onNewPlan
        self.onNewReminder = nil
        self.onVoiceInput = onVoiceInput
    }
    
    var body: some View {
        ZStack(alignment: .bottomTrailing) {
            // 主操作栏
            HStack {
                // AI语音按钮
                Button(action: {
                    onVoiceInput?()
                }) {
                    Image(systemName: "mic.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.white)
                }
                .frame(width: 48, height: 48)
                .background(LinearGradient.gedaGradient)
                .clipShape(Circle())
                .scaleEffect(1.0)
                .animation(.easeInOut(duration: 0.1), value: showAddMenu)
                
                Spacer()
                
                Text("可以对我讲你的待办任务")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                // 快速添加按钮 - 精确对应HTML的加号按钮
                ZStack {
                    Button(action: {
                        withAnimation(.easeOut(duration: 0.15)) {
                            showAddMenu.toggle()
                        }
                    }) {
                        Image(systemName: "plus")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(.white)
                            .rotationEffect(.degrees(showAddMenu ? 45 : 0))
                    }
                    .frame(width: 48, height: 48)
                    .background(LinearGradient.gedaGradient)
                    .clipShape(Circle())
                    .scaleEffect(showAddMenu ? 0.9 : 1.0)
                    .animation(.easeInOut(duration: 0.1), value: showAddMenu)
                }
            }
            .padding(.horizontal, DesignTokens.Spacing.lg)
            .padding(.vertical, DesignTokens.Spacing.md)
            .background(
                enhancedBlurBackground
            )
            .clipShape(Capsule())
            .overlay(
                // 边框效果
                Capsule()
                    .strokeBorder(
                        LinearGradient(
                            colors: [Color.white.opacity(0.3), Color.white.opacity(0.1)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 1
                    )
            )
            .shadow(color: Color.gray.opacity(0.2), radius: 25, x: 0, y: 25)
            
            // 悬浮菜单 - 精确对应HTML的popup-menu定位
            floatingMenu
        }
    }
    
    // MARK: - 悬浮菜单 - 精确对应HTML的popup-menu样式和定位
    @ViewBuilder
    private var floatingMenu: some View {
        if showAddMenu {
            VStack(spacing: 0) {
                // 立即专注 - 对应HTML的特殊选项
                menuItem(
                    icon: "bolt.fill",
                    iconColor: .indigo,
                    title: "立即专注",
                    action: {
                        // 跳转到专注页面
                        onVoiceInput?() // 临时使用，实际需要导航到专注页面
                    }
                )
                
                // 分隔线 - 对应HTML: border-t my-1 -mx-2
                Divider()
                    .padding(.horizontal, -8)
                    .padding(.vertical, 4)
                
                // 新建任务
                menuItem(
                    icon: "plus.circle",
                    iconColor: .blue,
                    title: "新建任务",
                    action: {
                        onNewTask?()
                    }
                )
                
                // 新建计划
                menuItem(
                    icon: "folder.badge.plus",
                    iconColor: .green,
                    title: "新建计划",
                    action: {
                        onNewPlan?()
                    }
                )
            }
            .padding(8) // 对应CSS: p-2
            .background(
                // 精确对应CSS: bg-white rounded-lg shadow-xl
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.white)
                    .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
                    .shadow(color: .black.opacity(0.04), radius: 10, x: 0, y: 5)
            )
            .frame(width: 160) // 对应CSS: w-40 (160px)
            .offset(x: -8, y: -64) // 对应CSS: right-0 bottom-16，调整到右侧对齐，上方64px
            .transition(
                // 对应CSS: scale(0.95) -> scale(1) + opacity transition
                .asymmetric(
                    insertion: .scale(scale: 0.95).combined(with: .opacity),
                    removal: .scale(scale: 0.95).combined(with: .opacity)
                )
            )
            .animation(.easeOut(duration: 0.15), value: showAddMenu) // 对应CSS: transition: all 0.15s ease-out
            .zIndex(50) // 对应CSS: z-50
        }
    }
    
    // 菜单项组件
    private func menuItem(icon: String, iconColor: Color, title: String, action: @escaping () -> Void) -> some View {
        Button(action: {
            withAnimation(.easeOut(duration: 0.15)) {
                showAddMenu = false
            }
            // 延迟执行动作，确保菜单先关闭
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                action()
            }
        }) {
            HStack(spacing: 12) { // 对应CSS: mr-3
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(iconColor)
                    .frame(width: 16) // 对应CSS: w-4 h-4
                
                Text(title)
                    .font(.caption) // 对应CSS: text-sm
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            .padding(.horizontal, 12) // 对应CSS: px-3
            .padding(.vertical, 8) // 对应CSS: py-2
            .background(
                // 对应CSS: hover:bg-gray-100 rounded-md
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.clear)
            )
            .contentShape(Rectangle()) // 确保整个区域可点击
        }
        .buttonStyle(PlainButtonStyle())
        .overlay(
            // 悬停效果背景
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.gray.opacity(0.1))
                .opacity(0) // 静态状态下隐藏，实际的悬停效果由系统处理
        )
    }
    
    // MARK: - 精确复刻CSS backdrop-blur-lg效果
    
    @ViewBuilder
    private var enhancedBlurBackground: some View {
        ZStack {
            // 精确复刻CSS: bg-white/70 backdrop-blur-lg
            Rectangle()
                .fill(.regularMaterial)  // 对应CSS backdrop-blur-lg，更接近lg级别的模糊
                .background(
                    Color.white.opacity(0.7)  // 精确对应CSS bg-white/70
                )
            
            // 增强层叠效果（模拟backdrop-filter的深度）
            Rectangle()
                .fill(
                    Color.white.opacity(0.15)  // 轻微的额外白色叠加
                )
                .blur(radius: 0.5)  // 轻微模糊增加质感
        }
        .compositingGroup()  // 确保材质效果正确合成
    }
}

#Preview {
    VStack {
        Spacer()
        
        VStack(spacing: DesignTokens.Spacing.lg) {
            BottomActionBar(showAddMenu: .constant(false))
            
            BottomActionBar(showAddMenu: .constant(true))
        }
        .padding()
    }
    .background(
        LinearGradient(
            colors: [Color.gedaBgShell, Color.gedaGray100],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    )
}
