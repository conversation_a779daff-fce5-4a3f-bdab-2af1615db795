//
//  TaskSectionHeader.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI

struct TaskSectionHeader: View {
    let title: String
    let taskCount: Int
    let onAddTask: (() -> Void)?
    
    @State private var sortOption: TaskSortOption = TaskSortOption.startTime
    @State private var viewMode: TaskViewMode = .stacked
    @State private var showingSortMenu = false
    @State private var showingFilterMenu = false
    @State private var sortAscending = true
    
    // 可选的回调函数，用于与外部ViewModel通信
    var onSortChanged: ((TaskSortOption, Bool) -> Void)?
    var onFilterChanged: ((Priority?, Bool?) -> Void)?
    
    @State private var selectedPriorityFilter: Priority?
    @State private var selectedCompletionFilter: Bool?
    
    init(
        title: String = "今日任务",
        taskCount: Int = 0,
        onAddTask: (() -> Void)? = nil,
        onSortChanged: ((TaskSortOption, Bool) -> Void)? = nil,
        onFilterChanged: ((Priority?, Bool?) -> Void)? = nil
    ) {
        self.title = title
        self.taskCount = taskCount
        self.onAddTask = onAddTask
        self.onSortChanged = onSortChanged
        self.onFilterChanged = onFilterChanged
    }
    
    var body: some View {
        HStack {
            HStack(spacing: DesignTokens.Spacing.xs) {
                Text(title)
                    .font(.gedaHeadline)
                    .foregroundColor(.gedaTextPrimary)
                
                if taskCount > 0 {
                    Text("(\(taskCount))")
                        .font(.gedaBody)
                        .foregroundColor(.gedaTextSecondary)
                }
            }
            
            Spacer()
            
            HStack(spacing: DesignTokens.Spacing.sm) {
                // 筛选按钮
                Button(action: {
                    showingFilterMenu.toggle()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                            .font(.caption)
                        
                        Text("筛选")
                            .font(.gedaCaptionMedium)
                    }
                    .foregroundColor(hasActiveFilters ? .gedaAccentStart : .gedaTextSecondary)
                    .padding(.horizontal, DesignTokens.Spacing.xs)
                    .padding(.vertical, DesignTokens.Spacing.xxs)
                    .background(hasActiveFilters ? Color.gedaAccentStart.opacity(0.1) : Color.gedaGray100)
                    .cornerRadius(DesignTokens.CornerRadius.small)
                }
                .popover(isPresented: $showingFilterMenu) {
                    filterMenu
                }
                
                // 排序按钮
                Button(action: {
                    showingSortMenu.toggle()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: sortIconName)
                            .font(.caption)
                        
                        Text(sortOption.displayName)
                            .font(.gedaCaptionMedium)
                        
                        Image(systemName: sortAscending ? "chevron.up" : "chevron.down")
                            .font(.caption2)
                    }
                    .foregroundColor(.gedaTextSecondary)
                    .padding(.horizontal, DesignTokens.Spacing.xs)
                    .padding(.vertical, DesignTokens.Spacing.xxs)
                    .background(Color.gedaGray100)
                    .cornerRadius(DesignTokens.CornerRadius.small)
                }
                .popover(isPresented: $showingSortMenu) {
                    sortMenu
                }
                
                // 添加任务按钮
                if let onAddTask = onAddTask {
                    Button(action: onAddTask) {
                        Image(systemName: "plus")
                            .font(.subheadline)
                            .foregroundColor(.gedaAccentStart)
                            .fontWeight(.medium)
                    }
                }
                
                // 视图切换按钮
                Button(action: {
                    withAnimation {
                        viewMode = viewMode == .stacked ? .list : .stacked
                    }
                }) {
                    Image(systemName: viewMode == .stacked ? "list.bullet" : "square.stack.3d.up")
                        .font(.subheadline)
                        .foregroundColor(.gedaTextSecondary)
                        .padding(DesignTokens.Spacing.xs)
                        .background(Color.gedaGray100)
                        .cornerRadius(DesignTokens.CornerRadius.small)
                }
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var hasActiveFilters: Bool {
        selectedPriorityFilter != nil || selectedCompletionFilter != nil
    }
    
    private var sortIconName: String {
        switch sortOption {
        case .startTime:
            return "calendar"
        case .priority:
            return "exclamationmark.triangle"
        case .title:
            return "textformat.abc"
        case .createdAt:
            return "clock"
        }
    }
    
    // MARK: - 子视图
    
    private var sortMenu: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("排序方式")
                .font(.gedaHeadline)
                .padding(.horizontal, 16)
                .padding(.top, 16)
            
            VStack(spacing: 0) {
                ForEach(TaskSortOption.allCases, id: \.self) { option in
                    Button(action: {
                        if sortOption == option {
                            sortAscending.toggle()
                        } else {
                            sortOption = option
                            sortAscending = true
                        }
                        onSortChanged?(sortOption, sortAscending)
                        showingSortMenu = false
                    }) {
                        HStack {
                            Text(option.displayName)
                                .font(.gedaBody)
                                .foregroundColor(.gedaTextPrimary)
                            
                            Spacer()
                            
                            if sortOption == option {
                                Image(systemName: sortAscending ? "arrow.up" : "arrow.down")
                                    .font(.caption)
                                    .foregroundColor(.gedaAccentStart)
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(sortOption == option ? Color.gedaAccentStart.opacity(0.1) : Color.clear)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    if option != TaskSortOption.allCases.last {
                        Divider()
                            .padding(.leading, 16)
                    }
                }
            }
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .frame(width: 200)
    }
    
    private var filterMenu: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("筛选条件")
                .font(.gedaHeadline)
                .padding(.horizontal, 16)
                .padding(.top, 16)
            
            VStack(alignment: .leading, spacing: 12) {
                // 优先级筛选
                Text("优先级")
                    .font(.gedaSubheadline)
                    .fontWeight(.medium)
                    .padding(.horizontal, 16)
                
                VStack(spacing: 0) {
                    Button(action: {
                        selectedPriorityFilter = nil
                        onFilterChanged?(selectedPriorityFilter, selectedCompletionFilter)
                    }) {
                        HStack {
                            Text("全部")
                                .font(.gedaBody)
                                .foregroundColor(.gedaTextPrimary)
                            
                            Spacer()
                            
                            if selectedPriorityFilter == nil {
                                Image(systemName: "checkmark")
                                    .font(.caption)
                                    .foregroundColor(.gedaAccentStart)
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    ForEach(Priority.allCases, id: \.self) { priority in
                        Button(action: {
                            selectedPriorityFilter = selectedPriorityFilter == priority ? nil : priority
                            onFilterChanged?(selectedPriorityFilter, selectedCompletionFilter)
                        }) {
                            HStack {
                                HStack(spacing: 8) {
                                    Circle()
                                        .fill(priority.color)
                                        .frame(width: 8, height: 8)
                                    
                                    Text(priority.displayName)
                                        .font(.gedaBody)
                                        .foregroundColor(.gedaTextPrimary)
                                }
                                
                                Spacer()
                                
                                if selectedPriorityFilter == priority {
                                    Image(systemName: "checkmark")
                                        .font(.caption)
                                        .foregroundColor(.gedaAccentStart)
                                }
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                
                Divider()
                    .padding(.horizontal, 16)
                
                // 完成状态筛选
                Text("完成状态")
                    .font(.gedaSubheadline)
                    .fontWeight(.medium)
                    .padding(.horizontal, 16)
                
                VStack(spacing: 0) {
                    Button(action: {
                        selectedCompletionFilter = nil
                        onFilterChanged?(selectedPriorityFilter, selectedCompletionFilter)
                    }) {
                        HStack {
                            Text("全部")
                                .font(.gedaBody)
                                .foregroundColor(.gedaTextPrimary)
                            
                            Spacer()
                            
                            if selectedCompletionFilter == nil {
                                Image(systemName: "checkmark")
                                    .font(.caption)
                                    .foregroundColor(.gedaAccentStart)
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Button(action: {
                        selectedCompletionFilter = selectedCompletionFilter == true ? nil : true
                        onFilterChanged?(selectedPriorityFilter, selectedCompletionFilter)
                    }) {
                        HStack {
                            Text("已完成")
                                .font(.gedaBody)
                                .foregroundColor(.gedaTextPrimary)
                            
                            Spacer()
                            
                            if selectedCompletionFilter == true {
                                Image(systemName: "checkmark")
                                    .font(.caption)
                                    .foregroundColor(.gedaAccentStart)
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Button(action: {
                        selectedCompletionFilter = selectedCompletionFilter == false ? nil : false
                        onFilterChanged?(selectedPriorityFilter, selectedCompletionFilter)
                    }) {
                        HStack {
                            Text("未完成")
                                .font(.gedaBody)
                                .foregroundColor(.gedaTextPrimary)
                            
                            Spacer()
                            
                            if selectedCompletionFilter == false {
                                Image(systemName: "checkmark")
                                    .font(.caption)
                                    .foregroundColor(.gedaAccentStart)
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                
                // 清除筛选按钮
                if hasActiveFilters {
                    Button(action: {
                        selectedPriorityFilter = nil
                        selectedCompletionFilter = nil
                        onFilterChanged?(nil, nil)
                        showingFilterMenu = false
                    }) {
                        Text("清除筛选")
                            .font(.gedaSubheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.gedaAccentStart)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.gedaAccentStart.opacity(0.1))
                            .cornerRadius(8)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 8)
                }
            }
            .padding(.bottom, 16)
        }
        .frame(width: 250)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 8)
    }
}

enum TaskViewMode {
    case stacked, list
}

// TaskSortOption枚举已移至 Geda/Models/Enums.swift

#Preview {
    TaskSectionHeader(
        taskCount: 5,
        onAddTask: {},
        onSortChanged: { sort, ascending in
            print("Sort changed: \(sort.displayName), ascending: \(ascending)")
        },
        onFilterChanged: { priority, completion in
            print("Filter changed - Priority: \(priority?.displayName ?? "All"), Completion: \(completion?.description ?? "All")")
        }
    )
    .padding()
}
