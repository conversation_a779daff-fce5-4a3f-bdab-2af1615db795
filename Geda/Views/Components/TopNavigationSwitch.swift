//
//  TopNavigationSwitch.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI

struct TopNavigationSwitch: View {
    @Binding var selectedTab: HomeTab
    
    var body: some View {
        HStack {
            // 左侧切换按钮组
            HStack(spacing: 4) {
                ForEach(HomeTab.allCases, id: \.self) { tab in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            selectedTab = tab
                        }
                    }) {
                        Text(tab.rawValue)
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(selectedTab == tab ? Color.gedaAccentStart : .gray)
                            .padding(.horizontal, 20)
                            .padding(.vertical, 8)
                            .background(
                                selectedTab == tab 
                                    ? Color.white
                                    : Color.clear
                            )
                            .cornerRadius(25)
                            .shadow(
                                color: selectedTab == tab ? .black.opacity(0.1) : .clear,
                                radius: 4,
                                x: 0,
                                y: 2
                            )
                    }
                }
            }
            .padding(4)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(25)
            
            Spacer()
            
            // 右侧功能按钮
            HStack(spacing: Constants.Spacing.medium) {
                // 复盘按钮
                NavigationLink(destination: ReviewView()) {
                    Image(systemName: "chart.bar.doc.horizontal")
                        .font(.system(size: 20))
                        .foregroundColor(.gray)
                }
                
                // 扫描按钮  
                NavigationLink(destination: ScannerView()) {
                    Image(systemName: "qrcode.viewfinder")
                        .font(.system(size: 20))
                        .foregroundColor(.gray)
                }
                
                // 个人资料按钮
                NavigationLink(destination: ProfileView()) {
                    Circle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: 36, height: 36)
                        .overlay(
                            Image(systemName: "person.fill")
                                .font(.system(size: 18))
                                .foregroundColor(.gray)
                        )
                }
            }
        }
    }
}

#Preview {
    TopNavigationSwitch(selectedTab: .constant(.schedule))
        .padding()
}
