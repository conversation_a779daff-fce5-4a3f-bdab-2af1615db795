//
//  CircularProgressView.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI

/// CircularProgressView组件 - 精确复刻HTML原型中的环形进度条
/// 对应focus-timer.html中的timer-circle-progress CSS类
/// 精确实现stroke-dasharray: 565.48 和 stroke-dashoffset动画
struct CircularProgressView: View {
    
    // MARK: - 属性
    let progress: Double
    let size: CGFloat
    let lineWidth: CGFloat
    let style: ProgressStyle
    let showPercentage: Bool
    let animationDuration: Double
    
    // 精确的CSS映射值
    private let cssStrokeDasharray: CGFloat = 565.48  // 对应CSS stroke-dasharray: 565.48
    
    // 动画控制属性
    @State private var animatedProgress: Double = 0
    @State private var strokeOffset: CGFloat = 565.48  // 对应CSS初始状态 stroke-dashoffset: 565.48
    
    // MARK: - 进度条样式枚举
    enum ProgressStyle {
        case gradient       // 渐变样式 (默认)
        case solid(Color)   // 纯色样式
        case large          // 大型计时器样式

        var strokeStyle: AnyShapeStyle {
            switch self {
            case .gradient, .large:
                return AnyShapeStyle(LinearGradient.gedaGradient)
            case .solid(let color):
                return AnyShapeStyle(color)
            }
        }
        
        var lineWidth: CGFloat {
            switch self {
            case .large:
                return 12  // 对应HTML中stroke-width="12"
            default:
                return 8
            }
        }
    }
    
    // MARK: - 初始化
    init(
        progress: Double,
        size: CGFloat = 200,
        lineWidth: CGFloat? = nil,  // 可选，如果为nil则使用style的默认lineWidth
        style: ProgressStyle = .gradient,
        showPercentage: Bool = false,
        animationDuration: Double = 1.0
    ) {
        self.progress = max(0, min(1, progress)) // 确保进度在0-1之间
        self.size = size
        self.lineWidth = lineWidth ?? style.lineWidth  // 使用传入值或style默认值
        self.style = style
        self.showPercentage = showPercentage
        self.animationDuration = animationDuration
    }
    
    // 便捷初始化方法，支持自定义颜色数组
    init(
        progress: Double,
        lineWidth: CGFloat = 8,
        colors: [Color]
    ) {
        self.progress = max(0, min(1, progress))
        self.size = 200
        self.lineWidth = lineWidth
        self.style = .gradient
        self.showPercentage = false
        self.animationDuration = 1.0
    }
    
    // MARK: - 视图主体
    var body: some View {
        ZStack {
            // 背景圆环 - 对应CSS中stroke="#eef2ff"的背景环
            Circle()
                .stroke(
                    Color(hex: "#eef2ff"),
                    style: StrokeStyle(lineWidth: lineWidth, lineCap: .round)
                )
                .frame(width: size, height: size)
            
            // 进度圆环 - 精确复刻CSS timer-circle-progress
            Circle()
                .stroke(
                    style.strokeStyle,
                    style: StrokeStyle(
                        lineWidth: lineWidth,
                        lineCap: .round,
                        dash: [cssStrokeDasharray],
                        dashPhase: strokeOffset
                    )
                )
                .frame(width: size, height: size)
                .rotationEffect(.degrees(-90))  // 对应CSS transform="rotate(-90 100 100)"
                .animation(
                    .linear(duration: animationDuration),  // 对应CSS transition: stroke-dashoffset 1s linear
                    value: strokeOffset
                )
            
            // 百分比文本 (可选)
            if showPercentage {
                percentageText
            }
        }
        .onAppear {
            startPreciseCSSAnimation()
        }
        .onChange(of: progress) { oldValue, newValue in
            animateProgressChange(from: oldValue, to: newValue)
        }
    }
    
    // MARK: - 百分比文本
    @ViewBuilder
    private var percentageText: some View {
        VStack(spacing: DesignTokens.Spacing.xs) {
            Text("\(Int(animatedProgress * 100))%")
                .font(.gedaNumberLarge)
                .foregroundColor(.gedaTextPrimary)
            
            Text("完成")
                .font(.gedaCaptionMedium)
                .foregroundColor(.gedaTextSecondary)
        }
    }
    
    // MARK: - 精确CSS动画方法
    
    /// 启动精确的CSS映射动画 - 对应stroke-dashoffset变化
    private func startPreciseCSSAnimation() {
        // 计算精确的stroke-dashoffset值
        // 根据进度计算偏移：完整偏移 * (1 - 进度)
        let targetOffset = cssStrokeDasharray * (1.0 - progress)
        
        withAnimation(.linear(duration: animationDuration)) {
            strokeOffset = targetOffset
            animatedProgress = progress
        }
    }
    
    /// 处理进度变化动画 - 精确复刻CSS transition
    private func animateProgressChange(from oldValue: Double, to newValue: Double) {
        let targetOffset = cssStrokeDasharray * (1.0 - newValue)
        
        withAnimation(.linear(duration: animationDuration)) {
            strokeOffset = targetOffset
            animatedProgress = newValue
        }
    }
}

// MARK: - 便捷构造器
extension CircularProgressView {
    
    /// 番茄钟计时器样式
    static func timer(
        progress: Double,
        size: CGFloat = 200
    ) -> CircularProgressView {
        CircularProgressView(
            progress: progress,
            size: size,
            lineWidth: 8,
            style: .gradient,
            showPercentage: false,
            animationDuration: 0.3
        )
    }
    
    /// 任务进度样式
    static func taskProgress(
        progress: Double,
        priority: String,
        size: CGFloat = 120
    ) -> CircularProgressView {
        CircularProgressView(
            progress: progress,
            size: size,
            lineWidth: 6,
            style: .solid(Color.priorityColor(for: priority)),
            showPercentage: true,
            animationDuration: 0.5
        )
    }
    
    /// 计划进度样式
    static func planProgress(
        progress: Double,
        size: CGFloat = 80
    ) -> CircularProgressView {
        CircularProgressView(
            progress: progress,
            size: size,
            lineWidth: 4,
            style: .gradient,
            showPercentage: true,
            animationDuration: 0.8
        )
    }
    
    /// 小型指示器样式
    static func indicator(
        progress: Double,
        color: Color = .gedaAccentStart,
        size: CGFloat = 40
    ) -> CircularProgressView {
        CircularProgressView(
            progress: progress,
            size: size,
            lineWidth: 3,
            style: .solid(color),
            showPercentage: false,
            animationDuration: 0.3
        )
    }
}

// MARK: - 带动画的进度更新
struct AnimatedCircularProgressView: View {
    @State private var animatedProgress: Double = 0
    let targetProgress: Double
    let size: CGFloat
    let style: CircularProgressView.ProgressStyle
    let showPercentage: Bool
    
    init(
        progress: Double,
        size: CGFloat = 200,
        style: CircularProgressView.ProgressStyle = .gradient,
        showPercentage: Bool = false
    ) {
        self.targetProgress = progress
        self.size = size
        self.style = style
        self.showPercentage = showPercentage
    }
    
    var body: some View {
        CircularProgressView(
            progress: animatedProgress,
            size: size,
            style: style,
            showPercentage: showPercentage
        )
        .onAppear {
            withAnimation(.easeInOut(duration: 1.5)) {
                animatedProgress = targetProgress
            }
        }
        .onChange(of: targetProgress) { _, newValue in
            withAnimation(.easeInOut(duration: 0.5)) {
                animatedProgress = newValue
            }
        }
    }
}

// MARK: - 多层进度环
struct MultiLayerCircularProgress: View {
    let layers: [ProgressLayer]
    let size: CGFloat
    
    struct ProgressLayer {
        let progress: Double
        let color: Color
        let lineWidth: CGFloat
        let offset: CGFloat
        
        init(progress: Double, color: Color, lineWidth: CGFloat = 6, offset: CGFloat = 0) {
            self.progress = progress
            self.color = color
            self.lineWidth = lineWidth
            self.offset = offset
        }
    }
    
    init(layers: [ProgressLayer], size: CGFloat = 200) {
        self.layers = layers
        self.size = size
    }
    
    var body: some View {
        ZStack {
            ForEach(Array(layers.enumerated()), id: \.offset) { index, layer in
                let layerSize = size - (layer.offset * 2)
                
                Circle()
                    .stroke(
                        Color.gedaGray200,
                        style: StrokeStyle(lineWidth: layer.lineWidth, lineCap: .round)
                    )
                    .frame(width: layerSize, height: layerSize)
                
                Circle()
                    .trim(from: 0, to: layer.progress)
                    .stroke(
                        layer.color,
                        style: StrokeStyle(lineWidth: layer.lineWidth, lineCap: .round)
                    )
                    .frame(width: layerSize, height: layerSize)
                    .rotationEffect(.degrees(-90))
                    .animation(.linear(duration: 1.0), value: layer.progress)
            }
        }
    }
}

// MARK: - 预览
#Preview("CircularProgressView Styles") {
    ScrollView {
        VStack(spacing: DesignTokens.Spacing.xxl) {
            // 基础样式
            VStack(spacing: DesignTokens.Spacing.lg) {
                Text("基础样式")
                    .gedaTitle3Style()
                
                HStack(spacing: DesignTokens.Spacing.xl) {
                    VStack {
                        CircularProgressView(progress: 0.75)
                        Text("渐变样式")
                            .gedaCaptionStyle()
                    }
                    
                    VStack {
                        CircularProgressView(
                            progress: 0.6,
                            style: .solid(.gedaSuccess),
                            showPercentage: true
                        )
                        Text("纯色样式")
                            .gedaCaptionStyle()
                    }
                }
            }
            
            // 便捷构造器
            VStack(spacing: DesignTokens.Spacing.lg) {
                Text("便捷构造器")
                    .gedaTitle3Style()
                
                HStack(spacing: DesignTokens.Spacing.xl) {
                    VStack {
                        CircularProgressView.timer(progress: 0.45)
                        Text("番茄钟样式")
                            .gedaCaptionStyle()
                    }
                    
                    VStack {
                        CircularProgressView.taskProgress(
                            progress: 0.8,
                            priority: "high"
                        )
                        Text("任务进度样式")
                            .gedaCaptionStyle()
                    }
                }
            }
            
            // 动画效果
            VStack(spacing: DesignTokens.Spacing.lg) {
                Text("动画效果")
                    .gedaTitle3Style()
                
                AnimatedCircularProgressView(
                    progress: 0.85,
                    showPercentage: true
                )
                Text("带动画的进度")
                    .gedaCaptionStyle()
            }
            
            // 多层进度
            VStack(spacing: DesignTokens.Spacing.lg) {
                Text("多层进度")
                    .gedaTitle3Style()
                
                MultiLayerCircularProgress(
                    layers: [
                        .init(progress: 0.8, color: .gedaAccentStart, lineWidth: 8, offset: 0),
                        .init(progress: 0.6, color: .gedaSuccess, lineWidth: 6, offset: 20),
                        .init(progress: 0.4, color: .gedaWarning, lineWidth: 4, offset: 35)
                    ]
                )
                Text("多层环形进度")
                    .gedaCaptionStyle()
            }
        }
        .padding()
    }
    .background(Color.gedaBgShell)
}
