//
//  TodayReminderCard.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI

struct TodayReminderCard: View {
    var body: some View {
        HStack(spacing: Constants.Spacing.small) {
            // 提醒图标
            Circle()
                .fill(Color.yellow.opacity(0.2))
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: "bell.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.yellow)
                )
            
            // 提醒内容
            VStack(alignment: .leading, spacing: 4) {
                Text("今日提醒")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("晚上八点, CPA经济法报名?")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 更多操作按钮
            Button(action: {
                // TODO: 显示提醒操作菜单
            }) {
                Image(systemName: "ellipsis")
                    .font(.system(size: 16))
                    .foregroundColor(.gray)
                    .padding(8)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding(Constants.Spacing.medium)
        .gedaCardStyle()
    }
}

#Preview {
    TodayReminderCard()
        .padding()
}
