//
//  TaskRowView.swift
//  Geda
//
//  Created by 杨瑞光 on 2025/7/11.
//

import SwiftUI

/// 任务行视图组件
struct TaskRowView: View {
    let task: Task
    @ObservedObject var viewModel: TaskViewModel
    
    var body: some View {
        HStack(spacing: DesignTokens.Spacing.lg) {
            // 完成状态按钮
            Button(action: {
                viewModel.toggleTaskCompletion(task)
            }) {
                Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(task.isCompleted ? .gedaSuccess : .gedaTextSecondary)
                    .font(.title2)
            }
            .buttonStyle(.plain)
            
            // 任务内容
            VStack(alignment: .leading, spacing: DesignTokens.Spacing.xs) {
                // 任务标题
                Text(task.title ?? "未命名任务")
                    .font(.gedaBody)
                    .foregroundColor(task.isCompleted ? .gedaTextSecondary : .gedaTextPrimary)
                    .strikethrough(task.isCompleted)
                
                // 时间和优先级信息
                HStack(spacing: DesignTokens.Spacing.sm) {
                    // 时间范围
                    if let startTime = task.startTime,
                       let endTime = task.endTime {
                        HStack(spacing: 2) {
                            Image(systemName: "clock")
                                .font(.gedaCaption)
                            Text("\(startTime, style: .time) - \(endTime, style: .time)")
                                .font(.gedaCaption)
                        }
                        .foregroundColor(.gedaTextSecondary)
                    }
                    
                    // 优先级指示器
                    let priority = Priority(rawValue: task.priority ?? "medium") ?? .medium
                    HStack(spacing: 4) {
                        Circle()
                            .fill(priority.color)
                            .frame(width: 8, height: 8)
                        Text(priority.displayName)
                            .font(.gedaCaption)
                            .foregroundColor(.gedaTextSecondary)
                    }
                    
                    Spacer()
                    
                    // 番茄钟数量
                    HStack(spacing: 2) {
                        Image(systemName: "timer")
                            .font(.gedaCaption)
                        Text("\(task.tomatoCount)")
                            .font(.gedaCaption)
                    }
                    .foregroundColor(.gedaAccentStart)
                }
            }
            
            Spacer()
            
            // 更多操作按钮
            Menu {
                Button(action: {
                    viewModel.showTaskDetails(task)
                }) {
                    Label("查看详情", systemImage: "info.circle")
                }
                
                Button(action: {
                    viewModel.showTaskEditor(task)
                }) {
                    Label("编辑", systemImage: "pencil")
                }
                
                Divider()
                
                Button(role: .destructive, action: {
                    viewModel.confirmDeleteTask(task)
                }) {
                    Label("删除", systemImage: "trash")
                }
            } label: {
                Image(systemName: "ellipsis")
                    .foregroundColor(.gedaTextSecondary)
                    .font(.title3)
            }
            .buttonStyle(.plain)
        }
        .padding(DesignTokens.Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: DesignTokens.CornerRadius.md)
                .fill(Color.gedaBackgroundSecondary)
                .shadow(
                    color: .black.opacity(0.05),
                    radius: 2,
                    x: 0,
                    y: 1
                )
        )
        .opacity(task.isCompleted ? 0.7 : 1.0)
        .contentShape(Rectangle())
        .onTapGesture {
            viewModel.showTaskDetails(task)
        }
    }
}

#Preview {
    let context = CoreDataManager.shared.viewContext
    let sampleTask = Task(context: context)
    sampleTask.id = UUID().uuidString
    sampleTask.title = "学习考研英语"
    sampleTask.startTime = Date()
    sampleTask.endTime = Date().addingTimeInterval(3600)
    sampleTask.priority = Priority.high.rawValue
    sampleTask.tomatoCount = 2
    sampleTask.isCompleted = false
    
    let completedTask = Task(context: context)
    completedTask.id = UUID().uuidString
    completedTask.title = "已完成的任务"
    completedTask.startTime = Date()
    completedTask.endTime = Date().addingTimeInterval(1800)
    completedTask.priority = Priority.medium.rawValue
    completedTask.tomatoCount = 1
    completedTask.isCompleted = true
    
    return VStack {
        TaskRowView(task: sampleTask, viewModel: TaskViewModel())
        TaskRowView(task: completedTask, viewModel: TaskViewModel())
    }
    .padding()
    .background(Color.gedaBackgroundPrimary)
}
