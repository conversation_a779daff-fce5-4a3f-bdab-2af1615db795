//
//  OnboardingOverlay.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/21.
//

import SwiftUI

/// 首次启动引导遮罩 - 体验AI创建任务
/// 精确复刻HTML原型中的onboarding-overlay功能
struct OnboardingOverlay: View {
    @Binding var isPresented: Bool
    @State private var arrowAnimationOffset: CGFloat = 0
    
    var body: some View {
        ZStack {
            // 背景遮罩 - 精确对应CSS: bg-black/70 backdrop-blur-sm
            Rectangle()
                .fill(.regularMaterial)
                .background(Color.black.opacity(0.7))
                .ignoresSafeArea()
                .onTapGesture {
                    // 点击背景关闭遮罩
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isPresented = false
                    }
                }
            
            // 引导内容 - 对应CSS: relative w-full px-6 pb-28 text-center
            VStack(spacing: 0) {
                Spacer()
                
                VStack(spacing: DesignTokens.Spacing.lg) {
                    // 动画箭头指示 - 对应CSS: animate-bounce
                    Image(systemName: "arrow.down")
                        .font(.system(size: 48, weight: .medium))
                        .foregroundColor(.white)
                        .offset(y: arrowAnimationOffset)
                        .onAppear {
                            // 启动弹跳动画 - 对应CSS: animation: arrow-bounce 1.5s infinite
                            withAnimation(
                                .easeInOut(duration: 0.75)
                                .repeatForever(autoreverses: true)
                            ) {
                                arrowAnimationOffset = -10
                            }
                        }
                    
                    VStack(spacing: DesignTokens.Spacing.md) {
                        // 引导标题 - 对应CSS: text-2xl font-bold text-white mt-4
                        Text("体验AI创建任务")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        // 引导描述 - 对应CSS: text-base text-gray-200 mt-2
                        Text("点击下方的麦克风按钮，试着对我说出你的待办事项，比如\"周五晚上八点去看电影\"。")
                            .font(.body)
                            .foregroundColor(.gray200)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, DesignTokens.Spacing.lg)
                    }
                    
                    // 我知道了按钮 - 对应CSS: mt-6 px-6 py-2 bg-white/20 text-white font-semibold rounded-full hover:bg-white/30
                    Button("我知道了") {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isPresented = false
                        }
                        
                        // 保存首次启动状态
                        UserDefaults.standard.set(true, forKey: "hasSeenOnboarding")
                    }
                    .font(.body)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, DesignTokens.Spacing.xl)
                    .padding(.vertical, DesignTokens.Spacing.sm)
                    .background(
                        Capsule()
                            .fill(Color.white.opacity(0.2))
                    )
                    .overlay(
                        Capsule()
                            .strokeBorder(Color.white.opacity(0.3), lineWidth: 1)
                    )
                    .scaleEffect(1.0)
                    .onTapGesture {
                        // 点击时的缩放反馈
                        withAnimation(.easeInOut(duration: 0.1)) {
                            // 短暂缩放效果已通过默认按钮样式处理
                        }
                    }
                }
                
                // 底部间距 - 对应CSS: pb-28 (112px)
                Spacer()
                    .frame(height: 112)
            }
            .padding(.horizontal, DesignTokens.Spacing.xl)
        }
        .opacity(isPresented ? 1 : 0)
        .animation(.easeInOut(duration: 0.5), value: isPresented)
    }
}

// MARK: - 扩展：Color.gray200 支持
extension Color {
    static let gray200 = Color(red: 0.93, green: 0.94, blue: 0.95)
}

// MARK: - Preview
#Preview {
    ZStack {
        // 模拟应用背景
        LinearGradient(
            colors: [Color.blue.opacity(0.3), Color.purple.opacity(0.3)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        // 模拟应用内容
        VStack {
            Text("应用主页内容")
                .font(.title)
                .foregroundColor(.black)
            
            Spacer()
            
            // 模拟底部操作栏
            HStack {
                Circle()
                    .fill(.blue)
                    .frame(width: 48, height: 48)
                Spacer()
                Text("可以对我讲你的待办任务")
                    .font(.caption)
                Spacer()
                Circle()
                    .fill(.blue)
                    .frame(width: 48, height: 48)
            }
            .padding()
            .background(.regularMaterial)
            .clipShape(Capsule())
            .padding()
        }
        
        // 引导遮罩
        OnboardingOverlay(isPresented: .constant(true))
    }
}