//
//  TaskCard.swift
//  Geda
//
//  Created by AI Assistant on 2025-07-14.
//

import SwiftUI
import CoreData

/// 任务卡片组件
/// 对应HTML原型中的task-card样式
struct TaskCard: View {
    let task: Task
    let onTaskTap: ((Task) -> Void)?
    let onTaskComplete: ((Task) -> Void)?
    
    init(
        task: Task,
        onTaskTap: ((Task) -> Void)? = nil,
        onTaskComplete: ((Task) -> Void)? = nil
    ) {
        self.task = task
        self.onTaskTap = onTaskTap
        self.onTaskComplete = onTaskComplete
    }
    
    var body: some View {
        GedaCard {
            VStack(spacing: DesignTokens.Spacing.sm) {
                // 任务标题和完成状态
                HStack(alignment: .top, spacing: DesignTokens.Spacing.sm) {
                    // 完成状态复选框
                    Button(action: {
                        onTaskComplete?(task)
                    }) {
                        Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                            .font(.title3)
                            .foregroundColor(task.isCompleted ? .gedaAccentStart : .gedaTextSecondary)
                    }
                    
                    // 任务内容
                    VStack(alignment: .leading, spacing: DesignTokens.Spacing.xs) {
                        // 任务标题
                        Text(task.title ?? "未命名任务")
                            .font(.gedaSubheadline)
                            .fontWeight(.medium)
                            .foregroundColor(task.isCompleted ? .gedaTextSecondary : .gedaTextPrimary)
                            .strikethrough(task.isCompleted)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                        
                        // 时间范围
                        if let startTime = task.startTime, let endTime = task.endTime {
                            Text("\(startTime.formatted(.dateTime.hour().minute())) - \(endTime.formatted(.dateTime.hour().minute()))")
                                .font(.gedaCaptionMedium)
                                .foregroundColor(.gedaTextSecondary)
                        }
                    }
                    
                    Spacer()
                    
                    // 优先级指示器
                    PriorityIndicator(priority: Priority(rawValue: task.priority ?? "") ?? .medium)
                }
                
                // 底部信息
                HStack {
                    // 番茄钟数量
                    HStack(spacing: DesignTokens.Spacing.xs) {
                        Image(systemName: "timer")
                            .font(.caption)
                            .foregroundColor(.gedaAccentStart)
                        
                        Text("\(task.tomatoCount)")
                            .font(.gedaCaptionMedium)
                            .foregroundColor(.gedaTextSecondary)
                        
                        Text("个番茄钟")
                            .font(.gedaCaptionSmall)
                            .foregroundColor(.gedaTextSecondary)
                    }
                    
                    Spacer()
                    
                    // 提醒状态
                    if task.isReminderEnabled {
                        Image(systemName: "bell.fill")
                            .font(.caption)
                            .foregroundColor(.gedaWarning)
                    }
                    
                    // 所属计划
                    if let plan = task.plan {
                        HStack(spacing: DesignTokens.Spacing.xs) {
                            Image(systemName: "folder.fill")
                                .font(.caption)
                                .foregroundColor(.blue)
                            
                            Text(plan.title ?? "计划")
                                .font(.gedaCaptionSmall)
                                .foregroundColor(.gedaTextSecondary)
                                .lineLimit(1)
                        }
                    }
                }
            }
        }
        .onTapGesture {
            onTaskTap?(task)
        }
        .opacity(task.isCompleted ? 0.7 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: task.isCompleted)
    }
}

/// 优先级指示器
struct PriorityIndicator: View {
    let priority: Priority
    
    var body: some View {
        Circle()
            .fill(priority.color)
            .frame(width: 12, height: 12)
            .overlay(
                Circle()
                    .stroke(priority.color.opacity(0.3), lineWidth: 2)
                    .scaleEffect(1.3)
            )
    }
}


/// SwiftUI预览
struct TaskCard_Previews: PreviewProvider {
    static var previews: some View {
        let context = CoreDataManager.shared.viewContext
        
        VStack(spacing: 16) {
            // 普通任务
            TaskCard(
                task: createMockTask(
                    context: context,
                    title: "完成iOS应用开发",
                    isCompleted: false,
                    priority: .high,
                    tomatoCount: 3
                ),
                onTaskTap: { _ in print("任务点击") },
                onTaskComplete: { _ in print("任务完成") }
            )
            
            // 已完成任务
            TaskCard(
                task: createMockTask(
                    context: context,
                    title: "阅读SwiftUI文档",
                    isCompleted: true,
                    priority: .medium,
                    tomatoCount: 2
                ),
                onTaskTap: { _ in print("任务点击") },
                onTaskComplete: { _ in print("任务完成") }
            )
            
            // 低优先级任务
            TaskCard(
                task: createMockTask(
                    context: context,
                    title: "整理桌面文件",
                    isCompleted: false,
                    priority: .low,
                    tomatoCount: 1
                ),
                onTaskTap: { _ in print("任务点击") },
                onTaskComplete: { _ in print("任务完成") }
            )
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .previewDisplayName("TaskCard 组件预览")
    }
    
    static func createMockTask(
        context: NSManagedObjectContext,
        title: String,
        isCompleted: Bool,
        priority: Priority,
        tomatoCount: Int32
    ) -> Task {
        let task = Task(context: context)
        task.title = title
        task.isCompleted = isCompleted
        task.priority = priority.rawValue
        task.tomatoCount = tomatoCount
        task.isReminderEnabled = true
        task.startTime = Date()
        task.endTime = Date().addingTimeInterval(3600)
        return task
    }
}