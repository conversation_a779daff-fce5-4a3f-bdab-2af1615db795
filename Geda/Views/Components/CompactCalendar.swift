//
//  CompactCalendar.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI

struct CompactCalendar: View {
    @State private var currentDate = Date()
    
    var body: some View {
        VStack(spacing: 0) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(monthString)
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.primary)
                    
                    Text("开始新的一天吧")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            .padding(.bottom, Constants.Spacing.medium)
            
            // 简化的周视图
            HStack(spacing: 0) {
                ForEach(weekDays, id: \.self) { day in
                    VStack(spacing: 8) {
                        Text(day.weekday)
                            .font(.caption)
                            .foregroundColor(day.isToday ? .white : .secondary)
                        
                        Text("\(day.dayNumber)")
                            .font(.caption)
                            .fontWeight(day.isToday ? .bold : .regular)
                            .foregroundColor(day.isToday ? .white : .secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(
                        day.isToday
                            ? AnyShapeStyle(LinearGradient.gedaGradient)
                            : AnyShapeStyle(Color.clear)
                    )
                    .cornerRadius(20)
                }
            }
            
            // 展开按钮
            HStack {
                Spacer()
                
                NavigationLink(destination: CalendarView()) {
                    Image(systemName: "chevron.down")
                        .font(.system(size: 20))
                        .foregroundColor(.gray)
                        .padding(.top, 8)
                }
                
                Spacer()
            }
        }
        .padding(Constants.Spacing.medium)
        .gedaCardStyle()
    }
    
    // 计算属性
    private var monthString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "M月"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: currentDate)
    }
    
    private var weekDays: [CalendarDay] {
        let calendar = Calendar.current
        let today = Date()
        let weekdaySymbols = ["日", "一", "二", "三", "四", "五", "六"]

        // 获取本周的日期范围
        let weekday = calendar.component(.weekday, from: today)
        let daysFromMonday = (weekday + 5) % 7 // 转换为周一开始

        var days: [CalendarDay] = []

        for i in 0..<7 {
            let dayOffset = i - daysFromMonday
            if let date = calendar.date(byAdding: .day, value: dayOffset, to: today) {
                let dayNumber = calendar.component(.day, from: date)
                let weekdayName = weekdaySymbols[i]
                let isToday = calendar.isDate(date, inSameDayAs: today)

                days.append(CalendarDay(
                    weekday: weekdayName,
                    dayNumber: dayNumber,
                    isToday: isToday
                ))
            }
        }

        return days
    }
}

struct CalendarDay: Hashable {
    let weekday: String
    let dayNumber: Int
    let isToday: Bool
}

#Preview {
    CompactCalendar()
        .padding()
}
