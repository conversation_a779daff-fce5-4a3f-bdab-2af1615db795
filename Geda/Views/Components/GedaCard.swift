//
//  GedaCard.swift
//  Geda
//
//  Created by AI Assistant on 2025-07-14.
//

import SwiftUI

/// 咯嗒应用的通用卡片组件
/// 对应HTML原型中的custom-card CSS样式
/// 实现精确的阴影系统和视觉效果
struct GedaCard<Content: View>: View {
    let content: Content
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    var body: some View {
        content
            .padding(DesignTokens.Spacing.lg)  // 对应CSS: padding: 16px (1rem)
            .background(Color.gedaBgPage)      // 对应CSS: --bg-page: #FFFFFF
            .cornerRadius(DesignTokens.CornerRadius.card)  // 对应CSS: border-radius: 1.25rem (20px)
            .gedaCardShadow()  // 对应CSS: --card-shadow精确阴影
    }
}

/// SwiftUI预览
struct GedaCard_Previews: PreviewProvider {
    static var previews: some View {
        GedaCard {
            VStack(alignment: .leading, spacing: 8) {
                Text("标准卡片")
                    .font(.headline)
                    .fontWeight(.medium)
                
                Text("这是一个标准样式的咯嗒卡片组件。")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .previewDisplayName("GedaCard 组件预览")
    }
}