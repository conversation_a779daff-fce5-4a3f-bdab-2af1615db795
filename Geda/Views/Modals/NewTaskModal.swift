//
//  NewTaskModal.swift
//  Geda
//
//  Created by AI Assistant on 2025-07-14.
//

import SwiftUI

/// 新建任务弹窗
/// 对应HTML原型中的new-task-modal.html
struct NewTaskModal: View {
    @Binding var isPresented: Bool
    @ObservedObject var taskViewModel: TaskViewModel
    
    @State private var taskTitle = ""
    @State private var selectedPriority: Priority = .medium
    @State private var selectedPlan: Plan? = nil
    @State private var tomatoCount = 1
    @State private var startTime = Date()
    @State private var endTime = Date().addingTimeInterval(3600) // 1小时后
    @State private var isReminderEnabled = true
    @State private var subtasks: [String] = []
    @State private var newSubtaskText = ""
    
    @State private var showingPlanPicker = false
    @State private var showingDatePicker = false
    @State private var currentDatePickerField: DatePickerField = .start
    
    enum DatePickerField {
        case start, end
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 标题栏
                headerView
                
                // 任务名称
                taskNameSection
                
                // 优先级选择
                prioritySection
                
                // 计划选择
                planSection
                
                // 番茄钟数量
                pomodoroSection
                
                // 时间设置
                timeSection
                
                // 提醒设置
                reminderSection
                
                // 子任务
                subtasksSection
                
                // 操作按钮
                actionButtons
            }
            .padding(24)
        }
        .background(Color.white)
        .cornerRadius(20)
    }
    
    // MARK: - 子视图
    
    private var headerView: some View {
        HStack {
            Button("取消") {
                isPresented = false
            }
            .foregroundColor(.secondary)
            
            Spacer()
            
            Text("新建任务")
                .font(.headline)
                .fontWeight(.medium)
            
            Spacer()
            
            Button("完成") {
                createTask()
            }
            .foregroundColor(.gedaAccentStart)
            .fontWeight(.medium)
            .disabled(taskTitle.isEmpty)
        }
    }
    
    private var taskNameSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("任务名称")
                .font(.subheadline)
                .fontWeight(.medium)
            
            TextField("输入任务名称", text: $taskTitle)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .submitLabel(.done)
        }
    }
    
    private var prioritySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("优先级")
                .font(.subheadline)
                .fontWeight(.medium)
            
            PriorityButtonGroup(selectedPriority: $selectedPriority)
        }
    }
    
    private var planSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("所属计划")
                .font(.subheadline)
                .fontWeight(.medium)
            
            Button(action: { showingPlanPicker = true }) {
                HStack {
                    Text(selectedPlan?.title ?? "选择计划（可选）")
                        .foregroundColor(selectedPlan == nil ? .secondary : .primary)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.down")
                        .foregroundColor(.secondary)
                        .font(.caption)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    private var pomodoroSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("番茄钟数量")
                .font(.subheadline)
                .fontWeight(.medium)
            
            PomodoroCountControl(count: $tomatoCount)
        }
    }
    
    private var timeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("时间安排")
                .font(.subheadline)
                .fontWeight(.medium)
            
            VStack(spacing: 12) {
                // 开始时间
                HStack {
                    Text("开始时间")
                        .font(.subheadline)
                        .frame(width: 70, alignment: .leading)
                    
                    Button(action: {
                        currentDatePickerField = .start
                        showingDatePicker = true
                    }) {
                        Text(startTime.formatted(.dateTime.hour().minute()))
                            .font(.subheadline)
                            .foregroundColor(.primary)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Spacer()
                }
                
                // 结束时间
                HStack {
                    Text("结束时间")
                        .font(.subheadline)
                        .frame(width: 70, alignment: .leading)
                    
                    Button(action: {
                        currentDatePickerField = .end
                        showingDatePicker = true
                    }) {
                        Text(endTime.formatted(.dateTime.hour().minute()))
                            .font(.subheadline)
                            .foregroundColor(.primary)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Spacer()
                }
            }
        }
    }
    
    private var reminderSection: some View {
        HStack {
            Text("开启提醒")
                .font(.subheadline)
                .fontWeight(.medium)
            
            Spacer()
            
            Toggle("", isOn: $isReminderEnabled)
                .toggleStyle(SwitchToggleStyle(tint: .gedaAccentStart))
        }
    }
    
    private var subtasksSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("子任务")
                .font(.subheadline)
                .fontWeight(.medium)
            
            // 现有子任务列表
            ForEach(subtasks.indices, id: \.self) { index in
                HStack {
                    Text(subtasks[index])
                        .font(.subheadline)
                    
                    Spacer()
                    
                    Button(action: {
                        subtasks.remove(at: index)
                    }) {
                        Image(systemName: "minus.circle.fill")
                            .foregroundColor(.red)
                    }
                }
                .padding(.vertical, 4)
            }
            
            // 添加新子任务
            HStack {
                TextField("添加子任务", text: $newSubtaskText)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .onSubmit {
                        addSubtask()
                    }
                
                Button(action: addSubtask) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(.gedaAccentStart)
                }
                .disabled(newSubtaskText.isEmpty)
            }
        }
    }
    
    private var actionButtons: some View {
        HStack(spacing: 12) {
            Button("取消") {
                isPresented = false
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.primary)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(Color(.systemGray5))
            .cornerRadius(12)
            
            Button("创建任务") {
                createTask()
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(
                LinearGradient.gedaGradient
                    .opacity(taskTitle.isEmpty ? 0.5 : 1.0)
            )
            .cornerRadius(12)
            .disabled(taskTitle.isEmpty)
        }
        .padding(.top, 8)
    }
    
    // MARK: - 方法
    
    private func addSubtask() {
        guard !newSubtaskText.isEmpty else { return }
        subtasks.append(newSubtaskText)
        newSubtaskText = ""
    }
    
    private func createTask() {
        // 创建任务逻辑
        taskViewModel.createTask(
            title: taskTitle,
            startTime: startTime,
            endTime: endTime,
            priority: selectedPriority,
            tomatoCount: Int32(tomatoCount),
            isReminderEnabled: isReminderEnabled,
            plan: selectedPlan,
            subtasks: subtasks
        )
        
        isPresented = false
    }
}

/// 优先级按钮组 - 增强版，支持动画效果和精确的CSS样式复刻
struct PriorityButtonGroup: View {
    @Binding var selectedPriority: Priority
    @State private var isVisible: Bool = false
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(Array(Priority.allCases.enumerated()), id: \.element) { index, priority in
                priorityButton(for: priority, index: index)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.5)) {
                isVisible = true
            }
        }
    }

    private func priorityButton(for priority: Priority, index: Int) -> some View {
        let isSelected = selectedPriority == priority
        let textColor = isSelected ? Color.white : Color.primary

        return Button(action: {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                selectedPriority = priority
            }
        }) {
            Text(priority.displayName)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(textColor)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(
            Group {
                if isSelected {
                    LinearGradient.gedaGradient
                } else {
                    Color.clear
                }
            }
        )
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(
                    isSelected ? Color.clear : Color.gray.opacity(0.3), 
                    lineWidth: 1
                )
        )
        .cornerRadius(8)
        .scaleEffect(isSelected ? 1.02 : 1.0)
        .shadow(
            color: isSelected ? Color.gedaAccentStart.opacity(0.3) : Color.clear,
            radius: isSelected ? 4 : 0,
            x: 0,
            y: 2
        )
        .opacity(isVisible ? 1.0 : 0.0)
        .offset(y: isVisible ? 0 : 20)
        .animation(
            .spring(response: 0.6, dampingFraction: 0.8)
            .delay(Double(index) * 0.1),
            value: isVisible
        )
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

/// 番茄钟数量控制器 - 增强版，支持动画和交互反馈
struct PomodoroCountControl: View {
    @Binding var count: Int
    @State private var isVisible: Bool = false
    @State private var isAnimating: Bool = false
    
    var body: some View {
        HStack {
            // 减少按钮
            Button(action: {
                if count > 1 { 
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                        count -= 1
                        triggerCountAnimation()
                    }
                }
            }) {
                Image(systemName: "minus")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.gedaAccentStart)
            }
            .frame(width: 32, height: 32)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gedaAccentStart.opacity(count <= 1 ? 0.3 : 0.6), lineWidth: 1)
                    )
            )
            .scaleEffect(count <= 1 ? 0.9 : 1.0)
            .opacity(count <= 1 ? 0.5 : 1.0)
            .disabled(count <= 1)
            .opacity(isVisible ? 1.0 : 0.0)
            .animation(.easeInOut(duration: 0.4).delay(0.1), value: isVisible)
            .animation(.spring(response: 0.2, dampingFraction: 0.8), value: count)
            
            Spacer()
            
            // 计数显示
            VStack {
                Text("\(count)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.gedaTextPrimary)
                    .scaleEffect(isAnimating ? 1.2 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isAnimating)
                
                Text("个")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .opacity(isVisible ? 1.0 : 0.0)
            .animation(.easeInOut(duration: 0.4).delay(0.2), value: isVisible)
            
            Spacer()
            
            // 增加按钮
            Button(action: {
                if count < 10 { 
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                        count += 1
                        triggerCountAnimation()
                    }
                }
            }) {
                Image(systemName: "plus")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.gedaAccentStart)
            }
            .frame(width: 32, height: 32)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gedaAccentStart.opacity(count >= 10 ? 0.3 : 0.6), lineWidth: 1)
                    )
            )
            .scaleEffect(count >= 10 ? 0.9 : 1.0)
            .opacity(count >= 10 ? 0.5 : 1.0)
            .disabled(count >= 10)
            .opacity(isVisible ? 1.0 : 0.0)
            .animation(.easeInOut(duration: 0.4).delay(0.3), value: isVisible)
            .animation(.spring(response: 0.2, dampingFraction: 0.8), value: count)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.gedaAccentStart.opacity(0.2), lineWidth: 1)
                )
        )
        .scaleEffect(isVisible ? 1.0 : 0.8)
        .opacity(isVisible ? 1.0 : 0.0)
        .onAppear {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                isVisible = true
            }
        }
    }
    
    private func triggerCountAnimation() {
        isAnimating = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            isAnimating = false
        }
    }
}

/// SwiftUI预览
struct NewTaskModal_Previews: PreviewProvider {
    static var previews: some View {
        NewTaskModalPreview()
    }
}

private struct NewTaskModalPreview: View {
    @State private var showModal = true
    @StateObject private var taskViewModel = TaskViewModel()
    
    var body: some View {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
            .modal(isPresented: $showModal) {
                NewTaskModal(
                    isPresented: $showModal,
                    taskViewModel: taskViewModel
                )
            }
    }
}