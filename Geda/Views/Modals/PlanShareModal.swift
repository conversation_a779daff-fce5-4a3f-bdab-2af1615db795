//
//  PlanShareModal.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/19.
//

import SwiftUI

/// 计划分享Modal
/// 显示分享码并提供分享选项
struct PlanShareModal: View {
    @Binding var isPresented: Bool
    let shareCode: String
    let planTitle: String
    
    @State private var showShareSheet = false
    @State private var showCopiedAlert = false
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题栏
            headerView
            
            // 计划信息
            planInfoView
            
            // 分享码显示
            shareCodeView
            
            // 操作按钮
            actionButtons
            
            Spacer()
        }
        .padding(24)
        .background(Color(.systemBackground))
        .cornerRadius(20)
        .sheet(isPresented: $showShareSheet) {
            ActivityViewController(activityItems: [shareCode])
        }
        .alert("已复制", isPresented: $showCopiedAlert) {
            Button("确定") { }
        } message: {
            Text("分享码已复制到剪贴板")
        }
    }
    
    private var headerView: some View {
        HStack {
            Button("关闭") {
                isPresented = false
            }
            .foregroundColor(.secondary)
            
            Spacer()
            
            Text("分享计划")
                .font(.headline)
                .fontWeight(.medium)
            
            Spacer()
            
            // 占位，保持布局平衡
            Text("关闭").opacity(0)
        }
    }
    
    private var planInfoView: some View {
        VStack(spacing: 16) {
            Image(systemName: "square.and.arrow.up")
                .font(.system(size: 48))
                .foregroundColor(.gedaAccentStart)
            
            VStack(spacing: 8) {
                Text("分享 \"\(planTitle)\"")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("其他人可以使用这个分享码导入您的计划")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    private var shareCodeView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("分享码")
                .font(.subheadline)
                .fontWeight(.medium)
            
            VStack(spacing: 0) {
                ScrollView(.horizontal, showsIndicators: false) {
                    Text(shareCode)
                        .font(.system(.caption, design: .monospaced))
                        .foregroundColor(.primary)
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .background(Color(.systemGray6))
                .cornerRadius(8)
                
                Button(action: copyShareCode) {
                    HStack {
                        Image(systemName: "doc.on.doc")
                            .font(.caption)
                        
                        Text("复制分享码")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.gedaAccentStart)
                    .padding(.vertical, 8)
                }
            }
        }
    }
    
    private var actionButtons: some View {
        VStack(spacing: 12) {
            Button(action: { showShareSheet = true }) {
                HStack {
                    Image(systemName: "square.and.arrow.up")
                        .font(.subheadline)
                    
                    Text("通过系统分享")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(LinearGradient.gedaGradient)
                .cornerRadius(12)
            }
            
            Button("完成") {
                isPresented = false
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.gedaAccentStart)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
    
    private func copyShareCode() {
        UIPasteboard.general.string = shareCode
        showCopiedAlert = true
    }
}

/// 系统分享Activity View Controller
struct ActivityViewController: UIViewControllerRepresentable {
    let activityItems: [Any]
    let applicationActivities: [UIActivity]? = nil
    
    func makeUIViewController(context: UIViewControllerRepresentableContext<ActivityViewController>) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: applicationActivities)
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: UIViewControllerRepresentableContext<ActivityViewController>) {}
}

#Preview {
    PlanShareModal(
        isPresented: .constant(true),
        shareCode: "GEDA_PLAN_V1_eyJ0aXRsZSI6IuWtpuS5oUdvIOWQhOenjeivremimOebruagh+",
        planTitle: "学习Go各种课程"
    )
}