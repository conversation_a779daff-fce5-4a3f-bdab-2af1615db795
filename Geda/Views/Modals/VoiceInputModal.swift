//
//  VoiceInputModal.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/19.
//

import SwiftUI

/// 语音输入Modal
/// 提供语音转文字功能和任务快速创建
struct VoiceInputModal: View {
    @Binding var isPresented: Bool
    @ObservedObject var taskViewModel: TaskViewModel
    
    @StateObject private var voiceManager = VoiceInputManager.shared
    @State private var showTaskPreview = false
    @State private var parsedTask: VoiceTaskInfo?
    @State private var showPermissionAlert = false
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题栏
            headerView
            
            // 主要内容
            if !voiceManager.hasPermission {
                permissionView
            } else if showTaskPreview, let task = parsedTask {
                taskPreviewView(task: task)
            } else {
                voiceInputView
            }
            
            // 操作按钮
            actionButtons
        }
        .padding(24)
        .background(Color(.systemBackground))
        .cornerRadius(20)
        .onAppear {
            voiceManager.checkPermissions()
        }
        .onDisappear {
            voiceManager.stopRecording()
        }
        .alert("需要权限", isPresented: $showPermissionAlert) {
            Button("去设置") {
                openSettings()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("需要语音识别和麦克风权限才能使用此功能，请在设置中开启。")
        }
    }
    
    // MARK: - 子视图
    
    private var headerView: some View {
        HStack {
            Button("取消") {
                isPresented = false
            }
            .foregroundColor(.secondary)
            
            Spacer()
            
            Text("语音输入")
                .font(.headline)
                .fontWeight(.medium)
            
            Spacer()
            
            Button("完成") {
                if let task = parsedTask {
                    createTaskFromVoice(task)
                } else {
                    isPresented = false
                }
            }
            .foregroundColor(.gedaAccentStart)
            .fontWeight(.medium)
            .opacity(parsedTask != nil ? 1.0 : 0.0)
        }
    }
    
    private var permissionView: some View {
        VStack(spacing: 20) {
            Image(systemName: "mic.slash.fill")
                .font(.system(size: 48))
                .foregroundColor(.orange)
            
            VStack(spacing: 12) {
                Text("需要语音权限")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("请允许访问麦克风和语音识别功能，以便将您的语音转换为任务。")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
            
            Button("授权权限") {
                voiceManager.requestPermissions()
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(LinearGradient.gedaGradient)
            .cornerRadius(12)
        }
    }
    
    private var voiceInputView: some View {
        VStack(spacing: 24) {
            // 语音可视化
            voiceVisualizationView
            
            // 识别结果
            recognizedTextView
            
            // 使用说明
            instructionView
        }
    }
    
    private var voiceVisualizationView: some View {
        VStack(spacing: 16) {
            // 录音按钮
            Button(action: toggleRecording) {
                ZStack {
                    Circle()
                        .fill(voiceManager.isRecording ? 
                              LinearGradient(colors: [.red, .pink], startPoint: .topLeading, endPoint: .bottomTrailing) :
                              LinearGradient.gedaGradient)
                        .frame(width: 120, height: 120)
                        .scaleEffect(voiceManager.isRecording ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 0.6).repeatForever(autoreverses: true), value: voiceManager.isRecording)
                    
                    Image(systemName: voiceManager.isRecording ? "stop.fill" : "mic.fill")
                        .font(.system(size: 36))
                        .foregroundColor(.white)
                }
                .shadow(color: .black.opacity(0.2), radius: 10, x: 0, y: 5)
            }
            .buttonStyle(PlainButtonStyle())
            
            // 状态文本
            Text(voiceManager.isRecording ? "正在监听..." : "点击开始录音")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(voiceManager.isRecording ? .blue : .secondary)
            
            // 录音电平指示器
            if voiceManager.isRecording {
                recordingLevelView
            }
        }
    }
    
    private var recordingLevelView: some View {
        HStack(spacing: 4) {
            ForEach(0..<8, id: \.self) { index in
                RoundedRectangle(cornerRadius: 2)
                    .fill(voiceManager.recordingLevel > Float(index) * 0.125 ? Color.blue : Color.gray.opacity(0.3))
                    .frame(width: 4, height: CGFloat(8 + index * 2))
                    .animation(.easeInOut(duration: 0.1), value: voiceManager.recordingLevel)
            }
        }
    }
    
    private var recognizedTextView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("识别结果")
                .font(.subheadline)
                .fontWeight(.medium)
            
            ScrollView {
                Text(voiceManager.recognizedText.isEmpty ? "等待语音输入..." : voiceManager.recognizedText)
                    .font(.body)
                    .foregroundColor(voiceManager.recognizedText.isEmpty ? .secondary : .primary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    .frame(minHeight: 80)
            }
            .frame(maxHeight: 120)
            
            if !voiceManager.recognizedText.isEmpty {
                HStack {
                    Button("创建任务") {
                        parseTextToTask()
                    }
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(LinearGradient.gedaGradient)
                    .cornerRadius(16)
                    
                    Button("清除") {
                        voiceManager.clearRecognizedText()
                        showTaskPreview = false
                        parsedTask = nil
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(Color(.systemGray6))
                    .cornerRadius(16)
                    
                    Spacer()
                }
            }
        }
    }
    
    private var instructionView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("使用说明")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("• 说\"重要\"或\"紧急\"设置高优先级")
                Text("• 说\"3个番茄钟\"设置番茄钟数量")
                Text("• 例如：\"重要，完成项目报告，需要5个番茄钟\"")
            }
            .font(.caption)
            .foregroundColor(.secondary)
        }
    }
    
    private func taskPreviewView(task: VoiceTaskInfo) -> some View {
        VStack(spacing: 20) {
            // 预览标题
            VStack(spacing: 8) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 32))
                    .foregroundColor(.green)
                
                Text("任务预览")
                    .font(.headline)
                    .fontWeight(.medium)
            }
            
            // 任务信息卡片
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("任务标题")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                }
                
                Text(task.title)
                    .font(.title3)
                    .fontWeight(.medium)
                
                Divider()
                
                HStack {
                    Label {
                        Text(Priority(rawValue: task.priority)?.displayName ?? "中")
                            .font(.subheadline)
                    } icon: {
                        Image(systemName: "flag.fill")
                            .foregroundColor(Priority(rawValue: task.priority)?.color ?? .orange)
                    }
                    
                    Spacer()
                    
                    Label {
                        Text("\(task.tomatoCount)个")
                            .font(.subheadline)
                    } icon: {
                        Image(systemName: "timer")
                            .foregroundColor(.red)
                    }
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
            
            Text("确认创建这个任务吗？")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
    
    private var actionButtons: some View {
        HStack(spacing: 12) {
            if showTaskPreview {
                Button("重新录音") {
                    showTaskPreview = false
                    parsedTask = nil
                    voiceManager.clearRecognizedText()
                }
                .font(.subheadline)
                .foregroundColor(.primary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                Button("创建任务") {
                    if let task = parsedTask {
                        createTaskFromVoice(task)
                    }
                }
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(LinearGradient.gedaGradient)
                .cornerRadius(12)
            } else {
                Button("取消") {
                    isPresented = false
                }
                .font(.subheadline)
                .foregroundColor(.primary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
        }
    }
    
    // MARK: - 方法
    
    private func toggleRecording() {
        if voiceManager.isRecording {
            voiceManager.stopRecording()
        } else {
            if voiceManager.hasPermission {
                voiceManager.startRecording()
            } else {
                showPermissionAlert = true
            }
        }
    }
    
    private func parseTextToTask() {
        guard !voiceManager.recognizedText.isEmpty else { return }
        
        if let task = voiceManager.parseVoiceToTask(text: voiceManager.recognizedText) {
            parsedTask = task
            showTaskPreview = true
            voiceManager.stopRecording()
        }
    }
    
    private func createTaskFromVoice(_ task: VoiceTaskInfo) {
        // 设置TaskViewModel的表单数据
        taskViewModel.taskTitle = task.title
        taskViewModel.taskPriority = Priority(rawValue: task.priority) ?? .medium
        taskViewModel.taskTomatoCount = Int(task.tomatoCount)
        taskViewModel.taskStartTime = Date()
        taskViewModel.taskEndTime = Calendar.current.date(byAdding: .hour, value: 1, to: Date()) ?? Date()
        taskViewModel.taskReminderEnabled = true
        
        // 创建任务
        taskViewModel.createTask()
        
        // 关闭弹窗
        isPresented = false
        
        // 重置状态
        voiceManager.clearRecognizedText()
        showTaskPreview = false
        parsedTask = nil
    }
    
    private func openSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
}

#Preview {
    VoiceInputModal(
        isPresented: .constant(true),
        taskViewModel: TaskViewModel()
    )
}