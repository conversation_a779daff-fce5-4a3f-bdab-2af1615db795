//
//  ModalPresentationModifier.swift
//  Geda
//
//  Created by AI Assistant on 2025-07-14.
//

import SwiftUI

/// Modal弹窗展示修饰符
/// 提供统一的Modal展示逻辑和动画效果
struct ModalPresentationModifier<ModalContent: View>: ViewModifier {
    @Binding var isPresented: Bool
    let modalContent: ModalContent
    let backgroundOpacity: Double
    let enableBackgroundDismiss: Bool
    let animationDuration: Double
    
    init(
        isPresented: Binding<Bool>,
        backgroundOpacity: Double = 0.5,
        enableBackgroundDismiss: Bool = true,
        animationDuration: Double = 0.3,
        @ViewBuilder modalContent: () -> ModalContent
    ) {
        self._isPresented = isPresented
        self.modalContent = modalContent()
        self.backgroundOpacity = backgroundOpacity
        self.enableBackgroundDismiss = enableBackgroundDismiss
        self.animationDuration = animationDuration
    }
    
    func body(content: Content) -> some View {
        ZStack {
            content
            
            if isPresented {
                // 背景遮罩
                Color.black.opacity(backgroundOpacity)
                    .ignoresSafeArea()
                    .onTapGesture {
                        if enableBackgroundDismiss {
                            dismissModal()
                        }
                    }
                
                // Modal内容
                modalContent
                    .background(Color.white)
                    .cornerRadius(20)
                    .shadow(
                        color: Color.black.opacity(0.2),
                        radius: 20,
                        x: 0,
                        y: 10
                    )
                    .padding(.horizontal, 24)
                    .scaleEffect(isPresented ? 1 : 0.95)
                    .opacity(isPresented ? 1 : 0)
                    .animation(
                        .easeInOut(duration: animationDuration),
                        value: isPresented
                    )
            }
        }
        .animation(
            .easeInOut(duration: animationDuration),
            value: isPresented
        )
    }
    
    private func dismissModal() {
        withAnimation(.easeInOut(duration: animationDuration)) {
            isPresented = false
        }
    }
}

/// View扩展，提供便捷的Modal展示方法  
extension View {
    /// 展示确认弹窗 - 备用版本，委托给主要的modal系统
    func confirmationModalBackup(
        isPresented: Binding<Bool>,
        title: String,
        message: String,
        confirmText: String = "确认",
        cancelText: String = "取消",
        onConfirm: @escaping () -> Void,
        onCancel: (() -> Void)? = nil
    ) -> some View {
        // 委托给主要的modal系统 (View+Extensions.swift)
        self.modal(isPresented: isPresented) {
            ConfirmationModal(
                title: title,
                message: message,
                confirmText: confirmText,
                cancelText: cancelText,
                onConfirm: {
                    onConfirm()
                    isPresented.wrappedValue = false
                },
                onCancel: {
                    onCancel?()
                    isPresented.wrappedValue = false
                }
            )
        }
    }
}

/// 确认弹窗组件
struct ConfirmationModal: View {
    let title: String
    let message: String
    let confirmText: String
    let cancelText: String
    let onConfirm: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            VStack(spacing: 12) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                
                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
            }
            
            HStack(spacing: 12) {
                Button(action: onCancel) {
                    Text(cancelText)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color(.systemGray5))
                        .cornerRadius(12)
                }
                
                Button(action: onConfirm) {
                    Text(confirmText)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(LinearGradient.gedaGradient)
                        .cornerRadius(12)
                }
            }
        }
        .padding(24)
    }
}

/// SwiftUI预览
struct ModalPresentationModifier_Previews: PreviewProvider {
    static var previews: some View {
        ModalPreviewWrapper()
    }
}

private struct ModalPreviewWrapper: View {
    @State private var showConfirmModal = false
    @State private var showCustomModal = false
    
    var body: some View {
        VStack(spacing: 20) {
            Button("显示确认弹窗") {
                showConfirmModal = true
            }
            .buttonStyle(.borderedProminent)
            
            Button("显示自定义弹窗") {
                showCustomModal = true
            }
            .buttonStyle(.borderedProminent)
        }
        .confirmationModal(
            isPresented: $showConfirmModal,
            title: "删除任务",
            message: "确定要删除这个任务吗？此操作不可撤销。",
            confirmText: "删除",
            cancelText: "取消",
            onConfirm: {
                print("确认删除")
            }
        )
        .modal(isPresented: $showCustomModal) {
            VStack(spacing: 16) {
                Text("自定义弹窗")
                    .font(.headline)
                    .fontWeight(.medium)
                
                Text("这是一个自定义的Modal弹窗内容")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Button("关闭") {
                    showCustomModal = false
                }
                .buttonStyle(.borderedProminent)
            }
            .padding(24)
        }
    }
}