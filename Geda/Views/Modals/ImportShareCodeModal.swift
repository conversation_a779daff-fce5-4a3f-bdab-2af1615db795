//
//  ImportShareCodeModal.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/19.
//

import SwiftUI

/// 导入分享码Modal
/// 允许用户输入分享码并导入计划
struct ImportShareCodeModal: View {
    @Binding var isPresented: Bool
    @ObservedObject var planViewModel: PlanViewModel
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题栏
            headerView
            
            // 说明
            instructionView
            
            // 分享码输入
            shareCodeInputView
            
            // 操作按钮
            actionButtons
            
            Spacer()
        }
        .padding(24)
        .background(Color(.systemBackground))
        .cornerRadius(20)
    }
    
    private var headerView: some View {
        HStack {
            Button("取消") {
                isPresented = false
            }
            .foregroundColor(.secondary)
            
            Spacer()
            
            Text("导入计划")
                .font(.headline)
                .fontWeight(.medium)
            
            Spacer()
            
            Button("导入") {
                planViewModel.executeImportShareCode()
            }
            .foregroundColor(.gedaAccentStart)
            .fontWeight(.medium)
            .disabled(planViewModel.shareCodeInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
        }
    }
    
    private var instructionView: some View {
        VStack(spacing: 16) {
            Image(systemName: "square.and.arrow.down")
                .font(.system(size: 48))
                .foregroundColor(.gedaAccentStart)
            
            VStack(spacing: 8) {
                Text("导入共享计划")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("输入别人分享给您的计划分享码，即可将计划导入到您的账户中")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    private var shareCodeInputView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("分享码")
                .font(.subheadline)
                .fontWeight(.medium)
            
            TextField("请输入分享码", text: $planViewModel.shareCodeInput, axis: .vertical)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .lineLimit(3...8)
                .font(.system(.caption, design: .monospaced))
        }
    }
    
    private var actionButtons: some View {
        VStack(spacing: 12) {
            Button("导入计划") {
                planViewModel.executeImportShareCode()
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(
                LinearGradient.gedaGradient
                    .opacity(planViewModel.shareCodeInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? 0.5 : 1.0)
            )
            .cornerRadius(12)
            .disabled(planViewModel.shareCodeInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            
            Button("取消") {
                isPresented = false
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.primary)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
}

#Preview {
    ImportShareCodeModal(
        isPresented: .constant(true),
        planViewModel: PlanViewModel()
    )
}