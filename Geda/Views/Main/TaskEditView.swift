//
//  TaskEditView.swift
//  Geda
//
//  Created by 杨瑞光 on 2025/7/11.
//

import SwiftUI

/// 任务编辑/创建视图
struct TaskEditView: View {
    @ObservedObject var viewModel: TaskViewModel
    @Environment(\.dismiss) private var dismiss
    
    let isEditing: Bool
    
    init(viewModel: TaskViewModel, isEditing: Bool = false) {
        self.viewModel = viewModel
        self.isEditing = isEditing
    }
    
    var body: some View {
        NavigationView {
            Form {
                // 基本信息
                basicInfoSection
                
                // 时间设置
                timeSettingsSection
                
                // 任务设置
                taskSettingsSection
                
                // 计划选择
                planSelectionSection
                
                // 提醒设置
                reminderSection
            }
            .navigationTitle(isEditing ? "编辑任务" : "新建任务")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(isEditing ? "保存" : "创建") {
                        if isEditing {
                            viewModel.updateTask()
                        } else {
                            viewModel.createTask()
                        }
                        dismiss()
                    }
                    .disabled(!viewModel.hasValidTaskForm)
                }
            }
        }
    }
    
    // MARK: - 视图组件
    
    private var basicInfoSection: some View {
        Section("基本信息") {
            TextField("任务标题", text: $viewModel.taskTitle)
                .textFieldStyle(.roundedBorder)
        }
    }
    
    private var timeSettingsSection: some View {
        Section("时间设置") {
            DatePicker(
                "开始时间",
                selection: $viewModel.taskStartTime,
                displayedComponents: [.date, .hourAndMinute]
            )
            
            DatePicker(
                "结束时间",
                selection: $viewModel.taskEndTime,
                displayedComponents: [.date, .hourAndMinute]
            )
            
            // 时间验证提示
            if viewModel.taskStartTime >= viewModel.taskEndTime {
                HStack {
                    Image(systemName: "exclamationmark.triangle")
                        .foregroundColor(.gedaError)
                    Text("结束时间必须晚于开始时间")
                        .font(.gedaCaption)
                        .foregroundColor(.gedaError)
                }
            }
        }
    }
    
    private var taskSettingsSection: some View {
        Section("任务设置") {
            // 优先级选择
            VStack(alignment: .leading, spacing: DesignTokens.Spacing.small) {
                Text("优先级")
                    .font(.gedaBody)
                    .foregroundColor(.gedaTextPrimary)
                
                HStack(spacing: DesignTokens.Spacing.medium) {
                    ForEach(Priority.allCases, id: \.self) { priority in
                        priorityButton(priority)
                    }
                }
            }
            .padding(.vertical, DesignTokens.Spacing.small)
            
            // 番茄钟数量
            HStack {
                Text("番茄钟数量")
                    .font(.gedaBody)
                    .foregroundColor(.gedaTextPrimary)
                
                Spacer()
                
                HStack {
                    Button(action: {
                        if viewModel.taskTomatoCount > 1 {
                            viewModel.taskTomatoCount -= 1
                        }
                    }) {
                        Image(systemName: "minus.circle")
                            .foregroundColor(.gedaAccentStart)
                    }
                    .disabled(viewModel.taskTomatoCount <= 1)
                    
                    Text("\(viewModel.taskTomatoCount)")
                        .font(.gedaBody)
                        .foregroundColor(.gedaTextPrimary)
                        .frame(minWidth: 30)
                    
                    Button(action: {
                        if viewModel.taskTomatoCount < 10 {
                            viewModel.taskTomatoCount += 1
                        }
                    }) {
                        Image(systemName: "plus.circle")
                            .foregroundColor(.gedaAccentStart)
                    }
                    .disabled(viewModel.taskTomatoCount >= 10)
                }
            }
        }
    }
    
    private func priorityButton(_ priority: Priority) -> some View {
        Button(action: {
            viewModel.taskPriority = priority
        }) {
            VStack(spacing: DesignTokens.Spacing.xsmall) {
                Circle()
                    .fill(priority.color)
                    .frame(width: 20, height: 20)
                    .overlay(
                        Circle()
                            .stroke(
                                viewModel.taskPriority == priority ? Color.gedaTextPrimary : .clear,
                                lineWidth: 2
                            )
                    )
                
                Text(priority.displayName)
                    .font(.gedaCaption)
                    .foregroundColor(
                        viewModel.taskPriority == priority ? .gedaTextPrimary : .gedaTextSecondary
                    )
            }
        }
        .buttonStyle(.plain)
    }
    
    private var planSelectionSection: some View {
        Section("计划") {
            HStack {
                Text("所属计划")
                    .font(.gedaBody)
                    .foregroundColor(.gedaTextPrimary)
                
                Spacer()
                
                if let selectedPlan = viewModel.selectedPlan {
                    Text(selectedPlan.title ?? "未命名计划")
                        .font(.gedaBody)
                        .foregroundColor(.gedaAccentStart)
                } else {
                    Text("无")
                        .font(.gedaBody)
                        .foregroundColor(.gedaTextSecondary)
                }
                
                Image(systemName: "chevron.right")
                    .font(.gedaCaption)
                    .foregroundColor(.gedaTextSecondary)
            }
            .contentShape(Rectangle())
            .onTapGesture {
                // TODO: 显示计划选择器
            }
        }
    }
    
    private var reminderSection: some View {
        Section("提醒") {
            Toggle("开启提醒", isOn: $viewModel.taskReminderEnabled)
                .tint(.gedaAccentStart)
        }
    }
}

// MARK: - 预览

#Preview("创建任务") {
    TaskEditView(viewModel: TaskViewModel(), isEditing: false)
}

#Preview("编辑任务") {
    let viewModel = TaskViewModel()
    viewModel.taskTitle = "示例任务"
    viewModel.taskPriority = .high
    viewModel.taskTomatoCount = 2
    viewModel.taskReminderEnabled = true
    
    return TaskEditView(viewModel: viewModel, isEditing: true)
}
