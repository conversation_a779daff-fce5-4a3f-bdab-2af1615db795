//
//  CalendarView.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//  Updated by AI Assistant on 2025/7/14
//

import SwiftUI
import Foundation

/// 日历视图 - 对应HTML原型中的calendar.html
struct CalendarView: View {
    @StateObject private var calendarViewModel = CalendarViewModel()
    @State private var selectedDate = Date()
    @State private var currentDate = Date()
    @State private var showingDateInfo = false
    @State private var showingNewTaskModal = false
    @State private var showingNewReminderModal = false
    @Environment(\.presentationMode) var presentationMode
    
    private let calendar = Calendar.current
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部导航栏
            headerView
            
            // 主要内容区域
            ScrollView {
                VStack(spacing: 16) {
                    // 月份导航
                    monthNavigationView
                    
                    // 日历主体
                    calendarGridView
                    
                    // 选中日期信息
                    if showingDateInfo {
                        selectedDateInfoView
                    }
                    
                    // 快速操作按钮
                    quickActionButtons
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 80)
            }
        }
        .navigationBarHidden(true)
        .background(Color.gedaBackgroundPrimary)  // 使用设计令牌背景色
        .onAppear {
            selectedDate = Date()
            showingDateInfo = true
            calendarViewModel.loadTasks(for: selectedDate)
        }
        .modal(isPresented: $showingNewTaskModal) {
            NewTaskModalContent(isPresented: $showingNewTaskModal)
        }
        .modal(isPresented: $showingNewReminderModal) {
            NewReminderModalContent(isPresented: $showingNewReminderModal)
        }
    }
    
    // MARK: - 顶部导航栏
    @ViewBuilder
    private var headerView: some View {
        HStack {
            // 返回按钮
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.primary)
            }
            .padding(.leading, 16)
            
            Spacer()
            
            // 页面标题
            Text("日历")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Spacer()
            
            // 占位空间，保持标题居中
            Color.clear
                .frame(width: 44, height: 44)
        }
        .padding(.vertical, 16)
        .background(Color.white)
        .overlay(
            Rectangle()
                .fill(Color.gray.opacity(0.1))
                .frame(height: 1),
            alignment: .bottom
        )
    }
    
    // MARK: - 月份导航
    @ViewBuilder
    private var monthNavigationView: some View {
        HStack {
            // 上一月按钮
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    currentDate = calendar.date(byAdding: .month, value: -1, to: currentDate) ?? currentDate
                }
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
                    .frame(width: 32, height: 32)
                    .background(Color(.secondarySystemFill))
                    .clipShape(Circle())
            }
            
            Spacer()
            
            // 月份年份显示
            Text(monthYearText)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Spacer()
            
            // 下一月按钮
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    currentDate = calendar.date(byAdding: .month, value: 1, to: currentDate) ?? currentDate
                }
            }) {
                Image(systemName: "chevron.right")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
                    .frame(width: 32, height: 32)
                    .background(Color(.secondarySystemFill))
                    .clipShape(Circle())
            }
        }
        .padding(.horizontal, 4)
    }
    
    // MARK: - 日历网格
    @ViewBuilder
    private var calendarGridView: some View {
        VStack {
            VStack(spacing: 8) {
                // 星期标题
                weekdayHeaderView
                
                // 日历网格
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 4) {
                    ForEach(calendarDays, id: \.self) { date in
                        CalendarDayCell(
                            date: date,
                            currentDate: currentDate,
                            selectedDate: $selectedDate,
                            hasEvents: calendarViewModel.hasEvents(for: date),
                            onDateSelected: { newDate in
                                selectedDate = newDate
                                showingDateInfo = true
                                calendarViewModel.loadTasks(for: newDate)
                            }
                        )
                    }
                }
            }
            .padding(DesignTokens.Spacing.lg)
        }
        .background(Color.gedaBgPage)
        .cornerRadius(DesignTokens.CornerRadius.card)
        .gedaCardShadow()  // 使用设计令牌阴影
    }
    
    // MARK: - 星期标题
    @ViewBuilder
    private var weekdayHeaderView: some View {
        HStack {
            ForEach(["日", "一", "二", "三", "四", "五", "六"], id: \.self) { weekday in
                Text(weekday)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity)
            }
        }
    }
    
    // MARK: - 选中日期信息
    @ViewBuilder
    private var selectedDateInfoView: some View {
        VStack {
            VStack(alignment: .leading, spacing: 16) {
                // 日期标题
                Text(selectedDateTitle)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                // 任务列表
                VStack(alignment: .leading, spacing: 12) {
                    Text("任务")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                    
                    if calendarViewModel.tasksForSelectedDate.isEmpty {
                        Text("当日无任务")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 8)
                            .background(Color(.secondarySystemFill))
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                    } else {
                        VStack(spacing: 8) {
                            ForEach(calendarViewModel.tasksForSelectedDate, id: \.id) { task in
                                TaskItemRow(task: task)
                            }
                        }
                    }
                }
                
                // 提醒列表
                VStack(alignment: .leading, spacing: 12) {
                    Text("提醒")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                    
                    if calendarViewModel.remindersForSelectedDate.isEmpty {
                        Text("当日无提醒")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 8)
                            .background(Color(.secondarySystemFill))
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                    } else {
                        VStack(spacing: 8) {
                            ForEach(calendarViewModel.remindersForSelectedDate, id: \.id) { reminder in
                                ReminderItemRow(reminder: reminder)
                            }
                        }
                    }
                }
            }
            .padding(16)
        }
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 4)
    }
    
    // MARK: - 快速操作按钮
    @ViewBuilder
    private var quickActionButtons: some View {
        HStack(spacing: 16) {
            // 新建任务按钮
            Button(action: {
                showingNewTaskModal = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "plus")
                        .font(.system(size: 16, weight: .medium))
                    Text("新建任务")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.blue)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color.blue.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
            
            // 新建提醒按钮
            Button(action: {
                showingNewReminderModal = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "bell")
                        .font(.system(size: 16, weight: .medium))
                    Text("新建提醒")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.orange)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color.orange.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
        }
    }
    
    // MARK: - 计算属性
    private var monthYearText: String {
        dateFormatter.dateFormat = "yyyy年M月"
        return dateFormatter.string(from: currentDate)
    }
    
    private var selectedDateTitle: String {
        let today = Date()
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: today) ?? today
        
        dateFormatter.dateFormat = "M月d日"
        var title = dateFormatter.string(from: selectedDate)
        
        if calendar.isDate(selectedDate, inSameDayAs: today) {
            title += " (今天)"
        } else if calendar.isDate(selectedDate, inSameDayAs: tomorrow) {
            title += " (明天)"
        }
        
        return title
    }
    
    private var calendarDays: [Date] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: currentDate) else {
            return []
        }
        
        let monthFirstWeekday = calendar.component(.weekday, from: monthInterval.start)
        let daysToSubtract = (monthFirstWeekday - 1) % 7
        
        guard let startDate = calendar.date(byAdding: .day, value: -daysToSubtract, to: monthInterval.start) else {
            return []
        }
        
        var days: [Date] = []
        for i in 0..<42 { // 6周 x 7天
            if let date = calendar.date(byAdding: .day, value: i, to: startDate) {
                days.append(date)
            }
        }
        
        return days
    }
}

// MARK: - 辅助组件

/// 日历日期单元格
struct CalendarDayCell: View {
    let date: Date
    let currentDate: Date
    @Binding var selectedDate: Date
    let hasEvents: Bool
    let onDateSelected: (Date) -> Void
    
    private let calendar = Calendar.current
    
    var body: some View {
        Button(action: {
            onDateSelected(date)
        }) {
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 8)
                    .fill(backgroundColor)
                
                VStack(spacing: 2) {
                    // 日期数字
                    Text("\(calendar.component(.day, from: date))")
                        .font(.subheadline)
                        .fontWeight(isSelected ? .bold : .medium)
                        .foregroundColor(textColor)
                    
                    // 事件指示器
                    if hasEvents {
                        Circle()
                            .fill(Color.gedaWarning)  // 使用应用主题色
                            .frame(width: 4, height: 4)
                    } else {
                        Spacer()
                            .frame(height: 4)
                    }
                }
            }
        }
        .frame(height: 40)
        .buttonStyle(PlainButtonStyle())
    }
    
    private var backgroundColor: Color {
        if isSelected {
            return Color.gedaAccentStart  // 使用应用主题色
        } else if isToday {
            return Color.gedaAccentStart.opacity(0.2)  // 使用主题色的透明版本
        } else {
            return Color.clear
        }
    }
    
    private var textColor: Color {
        if isSelected {
            return .white
        } else if isCurrentMonth {
            return .primary
        } else {
            return .secondary
        }
    }
    
    private var isSelected: Bool {
        calendar.isDate(date, inSameDayAs: selectedDate)
    }
    
    private var isToday: Bool {
        calendar.isDate(date, inSameDayAs: Date())
    }
    
    private var isCurrentMonth: Bool {
        calendar.isDate(date, equalTo: currentDate, toGranularity: .month)
    }
}

/// 任务项行组件
struct TaskItemRow: View {
    let task: CalendarTask
    
    var body: some View {
        HStack {
            Text(task.title)
                .font(.caption)
                .foregroundColor(.primary)
            
            Spacer()
            
            Text(task.timeRange)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.secondarySystemFill))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
}

/// 提醒项行组件
struct ReminderItemRow: View {
    let reminder: CalendarReminder
    
    var body: some View {
        HStack {
            Text(reminder.title)
                .font(.caption)
                .foregroundColor(.primary)
            
            Spacer()
            
            Text(reminder.time)
                .font(.caption2)
                .foregroundColor(.orange)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color.orange.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
}

/// 新建任务Modal内容
struct NewTaskModalContent: View {
    @Binding var isPresented: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            Text("新建任务")
                .font(.headline)
                .fontWeight(.medium)
            
            Text("任务创建功能将在后续版本中实现")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("关闭") {
                isPresented = false
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(LinearGradient(colors: [.blue, .purple], startPoint: .topLeading, endPoint: .bottomTrailing))
            .cornerRadius(12)
        }
        .padding(24)
    }
}

/// 新建提醒Modal内容
struct NewReminderModalContent: View {
    @Binding var isPresented: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            Text("新建提醒")
                .font(.headline)
                .fontWeight(.medium)
            
            Text("提醒创建功能将在后续版本中实现")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("关闭") {
                isPresented = false
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(LinearGradient(colors: [.blue, .purple], startPoint: .topLeading, endPoint: .bottomTrailing))
            .cornerRadius(12)
        }
        .padding(24)
    }
}

#Preview {
    NavigationView {
        CalendarView()
    }
}
