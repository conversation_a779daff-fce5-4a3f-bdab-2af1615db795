//
//  PlanView.swift
//  Geda
//
//  Created by AI Assistant on 2025-07-14.
//

import SwiftUI

/// 计划管理页面
/// 对应HTML原型中的plan.html
struct PlanView: View {
    @ObservedObject var planViewModel: PlanViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var showNewPlanModal = false
    @State private var showImportModal = false
    @State private var selectedPlan: Plan?
    @State private var showDeleteConfirmation = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 顶部操作区域
                    topActionSection
                    
                    // 进行中的计划
                    activePlansSection
                    
                    // 已搁置的计划
                    pausedPlansSection
                    
                    // 已完成的计划
                    completedPlansSection
                }
                .padding(.horizontal, 16)
                .padding(.top, 8)
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("计划")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        dismiss()
                    }
                    .foregroundColor(Color.gedaAccentStart)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("新建") {
                        showNewPlanModal = true
                    }
                    .foregroundColor(Color.gedaAccentStart)
                    .fontWeight(.medium)
                }
            }
        }
        .onAppear {
            planViewModel.loadPlans()
        }
        .modal(isPresented: $showNewPlanModal) {
            NewPlanModal(
                isPresented: $showNewPlanModal,
                planViewModel: planViewModel
            )
        }
        .modal(isPresented: $planViewModel.showPlanShareModal) {
            PlanShareModal(
                isPresented: $planViewModel.showPlanShareModal,
                shareCode: planViewModel.currentShareCode,
                planTitle: planViewModel.currentSharePlanTitle
            )
        }
        .modal(isPresented: $planViewModel.showImportModal) {
            ImportShareCodeModal(
                isPresented: $planViewModel.showImportModal,
                planViewModel: planViewModel
            )
        }
        .confirmationModal(
            isPresented: $showDeleteConfirmation,
            title: "删除计划",
            message: "确定要删除这个计划吗？计划下的所有任务也会被删除。",
            confirmText: "删除",
            cancelText: "取消",
            onConfirm: {
                if let plan = selectedPlan {
                    planViewModel.deletePlan(plan)
                }
            }
        )
    }
    
    // MARK: - 子视图
    
    private var topActionSection: some View {
        HStack(spacing: 12) {
            // 导入分享码按钮
            Button(action: { planViewModel.showImportShareCodeModal() }) {
                HStack(spacing: 8) {
                    Image(systemName: "square.and.arrow.down")
                        .font(.subheadline)
                    
                    Text("导入分享码")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .foregroundColor(Color.gedaAccentStart)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            }
            
            Spacer()
            
            // 探索模板按钮
            Button(action: { /* 探索模板逻辑 */ }) {
                HStack(spacing: 8) {
                    Image(systemName: "safari")
                        .font(.subheadline)
                    
                    Text("探索模板")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(LinearGradient.gedaGradient)
                .cornerRadius(12)
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            }
        }
    }
    
    private var activePlansSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(
                title: "进行中",
                count: planViewModel.activePlans.count,
                icon: "play.circle.fill",
                color: .green
            )
            
            if planViewModel.activePlans.isEmpty {
                EmptyStateView(
                    icon: "plus.circle",
                    title: "还没有进行中的计划",
                    subtitle: "创建一个新计划开始管理您的任务",
                    actionText: "创建计划",
                    action: { showNewPlanModal = true }
                )
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(planViewModel.activePlans) { plan in
                        PlanCard(
                            plan: plan,
                            onTap: { /* 跳转到计划详情 */ },
                            onShare: { sharePlan(plan) },
                            onPause: { planViewModel.pausePlan(plan) },
                            onResume: { planViewModel.resumePlan(plan) },
                            onRestart: { planViewModel.restartPlan(plan) },
                            onDelete: {
                                selectedPlan = plan
                                showDeleteConfirmation = true
                            }
                        )
                    }
                }
            }
        }
    }
    
    private var pausedPlansSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            if !planViewModel.pausedPlans.isEmpty {
                SectionHeader(
                    title: "已搁置",
                    count: planViewModel.pausedPlans.count,
                    icon: "pause.circle.fill",
                    color: .orange
                )
                
                LazyVStack(spacing: 12) {
                    ForEach(planViewModel.pausedPlans) { plan in
                        PlanCard(
                            plan: plan,
                            onTap: { /* 跳转到计划详情 */ },
                            onShare: { sharePlan(plan) },
                            onPause: { planViewModel.pausePlan(plan) },
                            onResume: { planViewModel.resumePlan(plan) },
                            onRestart: { planViewModel.restartPlan(plan) },
                            onDelete: {
                                selectedPlan = plan
                                showDeleteConfirmation = true
                            }
                        )
                    }
                }
            }
        }
    }
    
    private var completedPlansSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            if !planViewModel.completedPlans.isEmpty {
                SectionHeader(
                    title: "已完成",
                    count: planViewModel.completedPlans.count,
                    icon: "checkmark.circle.fill",
                    color: Color.gedaAccentStart
                )
                
                LazyVStack(spacing: 12) {
                    ForEach(planViewModel.completedPlans) { plan in
                        PlanCard(
                            plan: plan,
                            onTap: { /* 跳转到计划详情 */ },
                            onShare: { sharePlan(plan) },
                            onPause: { planViewModel.pausePlan(plan) },
                            onResume: { planViewModel.resumePlan(plan) },
                            onRestart: { planViewModel.restartPlan(plan) },
                            onDelete: {
                                selectedPlan = plan
                                showDeleteConfirmation = true
                            }
                        )
                    }
                }
            }
        }
    }
    
    // MARK: - 方法
    
    private func sharePlan(_ plan: Plan) {
        // 分享计划逻辑
        planViewModel.sharePlan(plan)
    }
}

/// 区域标题组件
struct SectionHeader: View {
    let title: String
    let count: Int
    let icon: String
    let color: Color
    
    var body: some View {
        HStack {
            Label {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
            } icon: {
                Image(systemName: icon)
                    .foregroundColor(color)
            }
            
            Text("(\(count))")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
        }
    }
}

/// 计划卡片组件
struct PlanCard: View {
    let plan: Plan
    let onTap: () -> Void
    let onShare: (() -> Void)?
    let onPause: (() -> Void)?
    let onResume: (() -> Void)?
    let onRestart: (() -> Void)?
    let onDelete: () -> Void
    
    @State private var showActionSheet = false
    
    var body: some View {
        GedaCard {
            VStack(alignment: .leading, spacing: 12) {
                // 计划标题和进度
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text(plan.title ?? "未命名计划")
                            .font(.headline)
                            .fontWeight(.medium)
                            .lineLimit(2)
                        
                        Spacer()
                        
                        Button(action: { showActionSheet = true }) {
                            Image(systemName: "ellipsis")
                                .foregroundColor(.secondary)
                                .frame(width: 24, height: 24)
                        }
                    }
                    
                    if let description = plan.planDescription, !description.isEmpty {
                        Text(description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                }
                
                // 进度条
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("进度")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Text("\(Int(plan.progress * 100))%")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(Color.gedaAccentStart)
                    }
                    
                    ProgressView(value: plan.progress)
                        .progressViewStyle(LinearProgressViewStyle(tint: Color.gedaAccentStart))
                        .frame(height: 6)
                }
                
                // 统计信息
                HStack {
                    StatItem(
                        icon: "list.bullet",
                        value: "\(plan.tasks?.count ?? 0)",
                        label: "任务"
                    )
                    
                    StatItem(
                        icon: "timer",
                        value: "\(plan.totalTomatoes)",
                        label: "番茄钟"
                    )
                    
                    if let startDate = plan.startDate {
                        StatItem(
                            icon: "calendar",
                            value: startDate.formatted(.dateTime.month().day()),
                            label: "开始"
                        )
                    }
                    
                    Spacer()
                    
                    // 状态标签
                    StatusBadge(status: PlanStatus(rawValue: plan.status ?? "") ?? .active)
                }
            }
        }
        .onTapGesture {
            onTap()
        }
        .actionSheet(isPresented: $showActionSheet) {
            ActionSheet(
                title: Text(plan.title ?? "计划操作"),
                buttons: actionButtons
            )
        }
    }
    
    private var actionButtons: [ActionSheet.Button] {
        var buttons: [ActionSheet.Button] = []
        
        // 根据计划状态显示不同操作
        switch PlanStatus(rawValue: plan.status ?? "") ?? .active {
        case .active:
            if let onPause = onPause {
                buttons.append(.default(Text("搁置计划"), action: onPause))
            }
        case .paused:
            if let onResume = onResume {
                buttons.append(.default(Text("恢复计划"), action: onResume))
            }
        case .completed:
            if let onRestart = onRestart {
                buttons.append(.default(Text("重新开始"), action: onRestart))
            }
        }
        
        if let onShare = onShare {
            buttons.append(.default(Text("分享计划"), action: onShare))
        }
        
        buttons.append(.destructive(Text("删除计划"), action: onDelete))
        buttons.append(.cancel())
        
        return buttons
    }
}

/// 统计项组件
struct StatItem: View {
    let icon: String
    let value: String
    let label: String
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
            
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

/// 状态标签组件
struct StatusBadge: View {
    let status: PlanStatus
    
    var body: some View {
        Text(status.displayName)
            .font(.caption)
            .fontWeight(.medium)
            .foregroundColor(status.textColor)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(status.backgroundColor)
            .cornerRadius(6)
    }
}

/// 空状态视图
struct EmptyStateView: View {
    let icon: String
    let title: String
    let subtitle: String
    let actionText: String
    let action: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: icon)
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.medium)
                
                Text(subtitle)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: action) {
                Text(actionText)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(LinearGradient.gedaGradient)
                    .cornerRadius(20)
            }
        }
        .padding(40)
        .frame(maxWidth: .infinity)
        .background(Color.white)
        .cornerRadius(16)
    }
}

/// 新建计划弹窗
/// 对应HTML原型中的new-plan-modal.html
struct NewPlanModal: View {
    @Binding var isPresented: Bool
    @ObservedObject var planViewModel: PlanViewModel
    
    @State private var planTitle = ""
    @State private var planDescription = ""
    @State private var startDate = Date()
    @State private var endDate = Date().addingTimeInterval(7 * 24 * 3600) // 一周后
    @State private var totalTomatoes = 20
    
    @State private var showingDatePicker = false
    @State private var currentDatePickerField: DatePickerField = .start
    
    enum DatePickerField {
        case start, end
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 标题栏
                headerView
                
                // 计划名称
                planNameSection
                
                // 计划描述
                planDescriptionSection
                
                // 时间设置
                timeSection
                
                // 番茄钟目标
                tomatoTargetSection
                
                // 操作按钮
                actionButtons
            }
            .padding(24)
        }
        .background(Color.white)
        .cornerRadius(20)
        .sheet(isPresented: $showingDatePicker) {
            datePickerSheet
        }
    }
    
    // MARK: - 子视图
    
    private var headerView: some View {
        HStack {
            Button("取消") {
                isPresented = false
            }
            .foregroundColor(.secondary)
            
            Spacer()
            
            Text("新建计划")
                .font(.headline)
                .fontWeight(.medium)
            
            Spacer()
            
            Button("完成") {
                createPlan()
            }
            .foregroundColor(.gedaAccentStart)
            .fontWeight(.medium)
            .disabled(planTitle.isEmpty)
        }
    }
    
    private var planNameSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("计划名称")
                .font(.subheadline)
                .fontWeight(.medium)
            
            TextField("输入计划名称", text: $planTitle)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .submitLabel(.done)
        }
    }
    
    private var planDescriptionSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("计划描述")
                .font(.subheadline)
                .fontWeight(.medium)
            
            TextField("描述你的学习计划（可选）", text: $planDescription, axis: .vertical)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .lineLimit(3...6)
        }
    }
    
    private var timeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("计划时间")
                .font(.subheadline)
                .fontWeight(.medium)
            
            VStack(spacing: 12) {
                // 开始日期
                HStack {
                    Text("开始日期")
                        .font(.subheadline)
                        .frame(width: 70, alignment: .leading)
                    
                    Button(action: {
                        currentDatePickerField = .start
                        showingDatePicker = true
                    }) {
                        Text(startDate.formatted(.dateTime.year().month().day()))
                            .font(.subheadline)
                            .foregroundColor(.primary)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Spacer()
                }
                
                // 结束日期
                HStack {
                    Text("结束日期")
                        .font(.subheadline)
                        .frame(width: 70, alignment: .leading)
                    
                    Button(action: {
                        currentDatePickerField = .end
                        showingDatePicker = true
                    }) {
                        Text(endDate.formatted(.dateTime.year().month().day()))
                            .font(.subheadline)
                            .foregroundColor(.primary)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Spacer()
                }
            }
        }
    }
    
    private var tomatoTargetSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("番茄钟目标")
                .font(.subheadline)
                .fontWeight(.medium)
            
            TomatoTargetControl(count: $totalTomatoes)
        }
    }
    
    private var actionButtons: some View {
        HStack(spacing: 12) {
            Button("取消") {
                isPresented = false
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.primary)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(Color(.systemGray5))
            .cornerRadius(12)
            
            Button("创建计划") {
                createPlan()
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(
                LinearGradient.gedaGradient
                    .opacity(planTitle.isEmpty ? 0.5 : 1.0)
            )
            .cornerRadius(12)
            .disabled(planTitle.isEmpty)
        }
        .padding(.top, 8)
    }
    
    private var datePickerSheet: some View {
        NavigationView {
            DatePicker(
                currentDatePickerField == .start ? "选择开始日期" : "选择结束日期",
                selection: currentDatePickerField == .start ? $startDate : $endDate,
                displayedComponents: .date
            )
            .datePickerStyle(.wheel)
            .navigationTitle(currentDatePickerField == .start ? "开始日期" : "结束日期")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("取消") {
                    showingDatePicker = false
                },
                trailing: Button("完成") {
                    showingDatePicker = false
                }
            )
        }
    }
    
    // MARK: - 方法
    
    private func createPlan() {
        // 设置PlanViewModel的表单数据
        planViewModel.planTitle = planTitle
        planViewModel.planDescription = planDescription
        planViewModel.planStartDate = startDate
        planViewModel.planEndDate = endDate
        
        // 调用PlanViewModel的创建方法
        planViewModel.createPlan()
        
        isPresented = false
    }
}

/// 番茄钟目标控制器
struct TomatoTargetControl: View {
    @Binding var count: Int
    
    var body: some View {
        HStack {
            Button(action: {
                if count > 5 { count -= 5 }
            }) {
                Image(systemName: "minus")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.gedaAccentStart)
            }
            .frame(width: 32, height: 32)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
            .disabled(count <= 5)
            
            Spacer()
            
            VStack {
                Text("\(count)")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("个番茄钟")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button(action: {
                if count < 100 { count += 5 }
            }) {
                Image(systemName: "plus")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.gedaAccentStart)
            }
            .frame(width: 32, height: 32)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
            .disabled(count >= 100)
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
}


#Preview {
    PlanView(
        planViewModel: PlanViewModel()
    )
}