//
//  ProfileView.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//  Updated by AI Assistant on 2025/7/14
//

import SwiftUI

/// 个人资料视图 - 对应HTML原型中的profile.html
struct ProfileView: View {
    @StateObject private var profileViewModel = ProfileViewModel()
    @ObservedObject private var studyModeManager = StudyModeManager.shared
    @StateObject private var authService = AuthenticationService.shared  // 添加认证服务
    @State private var showingStudyModeModal = false
    @State private var showingSettingsView = false
    @State private var showingAboutView = false
    @State private var showingTrashView = false
    @State private var showingLogoutAlert = false
    @State private var showingProfileEditModal = false
    @State private var showingTestModeAlert = false  // 测试模式确认弹窗
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部导航栏
            headerView
            
            // 主要内容区域
            ScrollView {
                VStack(spacing: 24) {
                    // 用户资料卡片
                    userProfileCard
                    
                    // 快速操作区域
                    quickActionsGrid
                    
                    // 设置菜单
                    settingsMenu
                    
                    // 退出登录按钮
                    logoutButton
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 80)
            }
        }
        .navigationBarHidden(true)
        .background(Color.gedaBackgroundPrimary)
        .onAppear {
            profileViewModel.loadUserProfile()
        }
        .modal(isPresented: $showingStudyModeModal) {
            StudyModeModalContent(isPresented: $showingStudyModeModal)
        }
        .modal(isPresented: $showingProfileEditModal) {
            ProfileEditModal(
                isPresented: $showingProfileEditModal,
                profileViewModel: profileViewModel
            )
        }
        .alert("测试模式控制", isPresented: $showingTestModeAlert) {
            Button("禁用测试模式", role: .destructive) {
                authService.disableTestMode()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("测试模式允许您在不登录Apple ID的情况下使用应用。\n\n注意：测试模式下的数据不会同步到iCloud。")
        }
        .confirmationModal(
            isPresented: $showingLogoutAlert,
            title: "退出登录",
            message: "确定要退出当前账户吗？",
            confirmText: "退出",
            cancelText: "取消",
            onConfirm: {
                profileViewModel.logout()
            }
        )
    }
    
    // MARK: - 顶部导航栏
    @ViewBuilder
    private var headerView: some View {
        HStack {
            // 返回按钮
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.primary)
            }
            .padding(.leading, 16)
            
            Spacer()
        }
        .padding(.vertical, 16)
        .background(Color(.systemBackground))
    }
    
    // MARK: - 用户资料卡片
    @ViewBuilder
    private var userProfileCard: some View {
        VStack {
            HStack(alignment: .center, spacing: 16) {
                // 用户头像
                AsyncImage(url: URL(string: profileViewModel.userProfile.avatarURL)) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.gray)
                }
                .frame(width: 64, height: 64)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(Color.gedaBgPage, lineWidth: 2)  // 使用应用背景色作为边框
                )
                
                // 用户信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(profileViewModel.userProfile.username)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("ID: \(profileViewModel.userProfile.userID)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    HStack(spacing: 4) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                        
                        Text("已坚持打卡 \(profileViewModel.userProfile.streakDays) 天")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 编辑按钮
                Button(action: {
                    showingProfileEditModal = true
                }) {
                    Image(systemName: "pencil")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .frame(width: 32, height: 32)
                        .background(Color(.tertiarySystemFill))
                        .clipShape(Circle())
                }
            }
            .padding(DesignTokens.Spacing.lg)
        }
        .background(Color.gedaBgPage)
        .cornerRadius(DesignTokens.CornerRadius.card)
        .gedaCardShadow()
    }
    
    // MARK: - 快速操作网格
    @ViewBuilder
    private var quickActionsGrid: some View {
        HStack(spacing: 16) {
            // 学霸模式
            Button(action: {
                if studyModeManager.isStudyModeActive {
                    // 如果正在学霸模式中，显示状态而不是开启新会话
                    showingStudyModeModal = false
                } else {
                    showingStudyModeModal = true
                }
            }) {
                VStack(spacing: 12) {
                    ZStack {
                        Circle()
                            .fill(studyModeManager.isStudyModeActive ? 
                                LinearGradient(colors: [.green, .blue], startPoint: .topLeading, endPoint: .bottomTrailing) :
                                LinearGradient(colors: [.blue, .purple], startPoint: .topLeading, endPoint: .bottomTrailing))
                            .frame(width: 48, height: 48)
                        
                        if studyModeManager.isStudyModeActive {
                            Image(systemName: "checkmark.shield.fill")
                                .font(.title3)
                                .foregroundColor(.white)
                        } else {
                            Image(systemName: "lock.fill")
                                .font(.title3)
                                .foregroundColor(.white)
                        }
                    }
                    
                    VStack(spacing: 2) {
                        Text(studyModeManager.isStudyModeActive ? "进行中" : "学霸模式")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        if studyModeManager.isStudyModeActive {
                            Text(studyModeManager.getRemainingTimeString())
                                .font(.caption2)
                                .foregroundColor(.green)
                                .fontWeight(.medium)
                        } else {
                            Text("专注时屏蔽打扰")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(studyModeManager.isStudyModeActive ? 
                    Color.green.opacity(0.1) : Color(.systemBackground))
                .cornerRadius(12)
                .shadow(radius: 4)
                .overlay(
                    // 活跃状态指示器
                    studyModeManager.isStudyModeActive ?
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.green, lineWidth: 2) : nil
                )
            }
            .buttonStyle(PlainButtonStyle())
            
            // 官方模板
            Button(action: {
                // TODO: 实现官方模板功能
            }) {
                VStack(spacing: 12) {
                    ZStack {
                        Circle()
                            .fill(LinearGradient(colors: [.blue, .purple], startPoint: .topLeading, endPoint: .bottomTrailing))
                            .frame(width: 48, height: 48)
                        
                        Image(systemName: "doc.text.fill")
                            .font(.title3)
                            .foregroundColor(.white)
                    }
                    
                    Text("官方模板")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(radius: 4)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
    
    // MARK: - 设置菜单
    @ViewBuilder
    private var settingsMenu: some View {
        VStack(spacing: 0) {
            // 通用设置
            SettingsRowView(
                icon: "gearshape.fill",
                title: "通用设置",
                action: {
                    showingSettingsView = true
                }
            )
            
            Divider()
                .padding(.leading, 56)
            
            // 回收站
            SettingsRowView(
                icon: "trash.fill",
                title: "回收站",
                action: {
                    showingTrashView = true
                }
            )
            
            Divider()
                .padding(.leading, 56)
            
            // 测试模式控制（仅在测试模式启用时显示）
            if authService.testModeEnabled {
                Button(action: {
                    showingTestModeAlert = true
                }) {
                    HStack(spacing: 16) {
                        Image(systemName: "wrench.fill")
                            .font(.title3)
                            .foregroundColor(.orange)
                            .frame(width: 24, height: 24)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text("测试模式")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)
                            
                            Text("开发者功能已启用")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 16)
                    .background(Color(.systemBackground))
                }
                .buttonStyle(PlainButtonStyle())
                
                Divider()
                    .padding(.leading, 56)
            }
            
            // 关于我们
            SettingsRowView(
                icon: "info.circle.fill",
                title: "关于我们",
                action: {
                    showingAboutView = true
                }
            )
        }
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 4)
    }
    
    // MARK: - 退出登录按钮
    @ViewBuilder
    private var logoutButton: some View {
        Button(action: {
            showingLogoutAlert = true
        }) {
            Text("退出登录")
                .font(.subheadline)
                .fontWeight(.bold)
                .foregroundColor(.red)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(Color.red.opacity(0.1))
                .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 辅助组件

/// 设置行视图组件
struct SettingsRowView: View {
    let icon: String
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .frame(width: 24, height: 24)
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 16)
            .background(Color(.systemBackground))
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// 学霸模式Modal内容
struct StudyModeModalContent: View {
    @Binding var isPresented: Bool
    @StateObject private var profileViewModel = ProfileViewModel()
    @State private var selectedDuration: StudyModeDuration = .minutes25
    @State private var enableNotifications = true
    @State private var enableSound = true
    @State private var showSuccess = false
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题区域
            VStack(spacing: 12) {
                Image(systemName: "lock.shield.fill")
                    .font(.system(size: 48))
                    .foregroundColor(.blue)
                
                Text("学霸模式")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("开启学霸模式后，将屏蔽娱乐应用通知，帮助您专注学习。")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
            
            // 设置选项
            VStack(spacing: 16) {
                // 专注时长选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("专注时长")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Picker("专注时长", selection: $selectedDuration) {
                        ForEach(StudyModeDuration.allCases, id: \.self) { duration in
                            Text(duration.displayName).tag(duration)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
                
                // 通知设置
                VStack(spacing: 12) {
                    Toggle(isOn: $enableNotifications) {
                        HStack {
                            Image(systemName: "bell.fill")
                                .foregroundColor(.orange)
                                .frame(width: 20)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("推送通知")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                
                                Text("开启后在专注结束时推送通知")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .toggleStyle(SwitchToggleStyle(tint: .blue))
                    
                    Toggle(isOn: $enableSound) {
                        HStack {
                            Image(systemName: "speaker.wave.2.fill")
                                .foregroundColor(.green)
                                .frame(width: 20)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("提醒声音")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                
                                Text("开启后在专注结束时播放提醒音")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .toggleStyle(SwitchToggleStyle(tint: .blue))
                }
            }
            .padding(.vertical, 8)
            
            // 操作按钮
            VStack(spacing: 12) {
                Button(action: startStudyMode) {
                    HStack {
                        Image(systemName: "play.fill")
                            .font(.subheadline)
                        
                        Text("开启学霸模式")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(LinearGradient(colors: [.blue, .purple], startPoint: .topLeading, endPoint: .bottomTrailing))
                    .cornerRadius(12)
                }
                
                Button("取消") {
                    isPresented = false
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
        }
        .padding(24)
        .background(Color(.systemBackground))
        .cornerRadius(20)
        .alert("学霸模式已开启", isPresented: $showSuccess) {
            Button("确定") {
                isPresented = false
            }
        } message: {
            Text("已为您开启 \(selectedDuration.displayName) 的专注时间，开始专注学习吧！")
        }
        .onAppear {
            profileViewModel.loadUserProfile()
        }
    }
    
    private func startStudyMode() {
        // 开启学霸模式
        profileViewModel.toggleStudyMode()
        
        // 启动专注计时器
        StudyModeManager.shared.startStudySession(
            duration: selectedDuration.timeInterval,
            enableNotifications: enableNotifications,
            enableSound: enableSound
        )
        
        // 显示成功提示
        showSuccess = true
    }
}

/// 学霸模式时长枚举
enum StudyModeDuration: CaseIterable {
    case minutes15
    case minutes25
    case minutes45
    case hour1
    
    var displayName: String {
        switch self {
        case .minutes15: return "15分钟"
        case .minutes25: return "25分钟"
        case .minutes45: return "45分钟"
        case .hour1: return "1小时"
        }
    }
    
    var timeInterval: TimeInterval {
        switch self {
        case .minutes15: return 15 * 60
        case .minutes25: return 25 * 60
        case .minutes45: return 45 * 60
        case .hour1: return 60 * 60
        }
    }
}

/// Profile编辑Modal内容
struct ProfileEditModal: View {
    @Binding var isPresented: Bool
    @ObservedObject var profileViewModel: ProfileViewModel
    
    @State private var editedUsername: String = ""
    @State private var editedEmail: String = ""
    @State private var editedAvatarURL: String = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 头像编辑区域
                    avatarSection
                    
                    // 基本信息编辑
                    basicInfoSection
                    
                    // 偏好设置
                    preferencesSection
                    
                    // 通知设置
                    notificationSection
                }
                .padding(24)
            }
            .navigationTitle("编辑资料")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("取消") {
                    isPresented = false
                },
                trailing: Button("保存") {
                    saveChanges()
                }
                .fontWeight(.medium)
                .foregroundColor(.gedaAccentStart)
            )
        }
        .onAppear {
            loadCurrentData()
        }
    }
    
    // MARK: - 子视图
    
    private var avatarSection: some View {
        VStack(spacing: 16) {
            Text("头像")
                .font(.headline)
                .fontWeight(.medium)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                // 当前头像显示
                AsyncImage(url: URL(string: editedAvatarURL)) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.gray)
                }
                .frame(width: 80, height: 80)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
                
                // 头像URL输入
                VStack(alignment: .leading, spacing: 8) {
                    Text("头像链接")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    TextField("输入头像图片链接", text: $editedAvatarURL)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.URL)
                        .autocapitalization(.none)
                }
            }
        }
    }
    
    private var basicInfoSection: some View {
        VStack(spacing: 16) {
            Text("基本信息")
                .font(.headline)
                .fontWeight(.medium)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 16) {
                // 用户名编辑
                VStack(alignment: .leading, spacing: 8) {
                    Text("用户名")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    TextField("输入用户名", text: $editedUsername)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .submitLabel(.done)
                }
                
                // 邮箱编辑
                VStack(alignment: .leading, spacing: 8) {
                    Text("邮箱")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    TextField("输入邮箱地址", text: $editedEmail)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                        .submitLabel(.done)
                }
            }
        }
    }
    
    private var preferencesSection: some View {
        VStack(spacing: 16) {
            Text("偏好设置")
                .font(.headline)
                .fontWeight(.medium)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                // 主题设置
                HStack {
                    Text("主题设置")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Picker("主题", selection: .constant(profileViewModel.userProfile.preferences.preferredTheme)) {
                        ForEach(AppTheme.allCases, id: \.self) { theme in
                            Text(theme.displayName).tag(theme)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                // 声音开关
                HStack {
                    Text("启用声音")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Toggle("", isOn: .constant(profileViewModel.userProfile.preferences.soundEnabled))
                        .toggleStyle(SwitchToggleStyle(tint: .gedaAccentStart))
                }
                
                // 振动开关
                HStack {
                    Text("启用振动")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Toggle("", isOn: .constant(profileViewModel.userProfile.preferences.vibrationEnabled))
                        .toggleStyle(SwitchToggleStyle(tint: .gedaAccentStart))
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    private var notificationSection: some View {
        VStack(spacing: 16) {
            Text("通知设置")
                .font(.headline)
                .fontWeight(.medium)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                // 任务提醒
                HStack {
                    Text("任务提醒")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Toggle("", isOn: .constant(profileViewModel.userProfile.notificationSettings.isTaskReminderEnabled))
                        .toggleStyle(SwitchToggleStyle(tint: .gedaAccentStart))
                }
                
                // 番茄钟通知
                HStack {
                    Text("番茄钟通知")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Toggle("", isOn: .constant(profileViewModel.userProfile.notificationSettings.isPomodoroNotificationEnabled))
                        .toggleStyle(SwitchToggleStyle(tint: .gedaAccentStart))
                }
                
                // 复盘提醒
                HStack {
                    Text("复盘提醒")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    Toggle("", isOn: .constant(profileViewModel.userProfile.notificationSettings.isReviewReminderEnabled))
                        .toggleStyle(SwitchToggleStyle(tint: .gedaAccentStart))
                }
            }
            .padding(.vertical, 8)
        }
    }
    
    // MARK: - 方法
    
    private func loadCurrentData() {
        editedUsername = profileViewModel.userProfile.username
        editedEmail = profileViewModel.userProfile.email
        editedAvatarURL = profileViewModel.userProfile.avatarURL
    }
    
    private func saveChanges() {
        // 更新用户名
        if editedUsername != profileViewModel.userProfile.username {
            profileViewModel.updateUsername(editedUsername)
        }
        
        // 更新头像
        if editedAvatarURL != profileViewModel.userProfile.avatarURL {
            profileViewModel.updateAvatar(imageURL: editedAvatarURL)
        }
        
        // 更新邮箱
        if editedEmail != profileViewModel.userProfile.email {
            profileViewModel.updateEmail(editedEmail)
        }
        
        isPresented = false
    }
}

#Preview {
    NavigationView {
        ProfileView()
    }
}
