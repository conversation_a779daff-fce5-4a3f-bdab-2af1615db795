//
//  TaskDetailView.swift
//  Geda
//
//  Created by 杨瑞光 on 2025/7/11.
//

import SwiftUI

/// 任务详情视图
struct TaskDetailView: View {
    @ObservedObject var viewModel: TaskViewModel
    @Environment(\.dismiss) private var dismiss
    
    let task: Task
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 任务标题和状态
                    taskHeaderSection
                    
                    // 时间信息
                    timeInfoSection
                    
                    // 优先级和番茄钟
                    priorityAndTomatoSection
                    
                    // 计划信息
                    if let plan = task.plan {
                        planInfoSection(plan)
                    }
                    
                    // 提醒设置
                    reminderSection
                    
                    // 操作按钮
                    actionButtonsSection
                }
                .padding(DesignTokens.Spacing.medium)
            }
            .navigationTitle("任务详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("编辑") {
                        viewModel.showTaskEditor(task)
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - 视图组件
    
    private var taskHeaderSection: some View {
        VStack(alignment: .leading, spacing: DesignTokens.Spacing.small) {
            HStack {
                Text(task.title ?? "未命名任务")
                    .font(.gedaTitle2)
                    .foregroundColor(.gedaTextPrimary)
                
                Spacer()
                
                // 完成状态指示器
                Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(task.isCompleted ? .gedaSuccess : .gedaTextSecondary)
                    .font(.title2)
            }
            
            // 任务状态标签
            HStack {
                Label(
                    task.isCompleted ? "已完成" : "进行中",
                    systemImage: task.isCompleted ? "checkmark.circle" : "clock"
                )
                .font(.gedaCaption)
                .foregroundColor(task.isCompleted ? .gedaSuccess : .gedaWarning)
                .padding(.horizontal, DesignTokens.Spacing.small)
                .padding(.vertical, DesignTokens.Spacing.xsmall)
                .background(
                    RoundedRectangle(cornerRadius: DesignTokens.CornerRadius.small)
                        .fill(task.isCompleted ? Color.gedaSuccess.opacity(0.1) : Color.gedaWarning.opacity(0.1))
                )
                
                Spacer()
            }
        }
        .padding(DesignTokens.Spacing.medium)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gedaBackgroundSecondary)
        )
    }
    
    private var timeInfoSection: some View {
        VStack(alignment: .leading, spacing: DesignTokens.Spacing.medium) {
            Text("时间安排")
                .font(.gedaHeadline)
                .foregroundColor(.gedaTextPrimary)
            
            VStack(spacing: DesignTokens.Spacing.small) {
                timeInfoRow(
                    title: "开始时间",
                    time: task.startTime ?? Date(),
                    icon: "play.circle"
                )
                
                timeInfoRow(
                    title: "结束时间",
                    time: task.endTime ?? Date(),
                    icon: "stop.circle"
                )
                
                // 持续时间
                if let startTime = task.startTime,
                   let endTime = task.endTime {
                    let duration = endTime.timeIntervalSince(startTime)
                    let hours = Int(duration) / 3600
                    let minutes = Int(duration) % 3600 / 60
                    
                    HStack {
                        Image(systemName: "timer")
                            .foregroundColor(.gedaAccentStart)
                            .frame(width: 20)
                        
                        Text("持续时间")
                            .font(.gedaBody)
                            .foregroundColor(.gedaTextSecondary)
                        
                        Spacer()
                        
                        Text("\(hours)小时\(minutes)分钟")
                            .font(.gedaBody)
                            .foregroundColor(.gedaTextPrimary)
                    }
                }
            }
        }
        .padding(DesignTokens.Spacing.medium)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gedaBackgroundSecondary)
        )
    }
    
    private func timeInfoRow(title: String, time: Date, icon: String) -> some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.gedaAccentStart)
                .frame(width: 20)
            
            Text(title)
                .font(.gedaBody)
                .foregroundColor(.gedaTextSecondary)
            
            Spacer()
            
            Text(time, style: .time)
                .font(.gedaBody)
                .foregroundColor(.gedaTextPrimary)
        }
    }
    
    private var priorityAndTomatoSection: some View {
        VStack(alignment: .leading, spacing: DesignTokens.Spacing.medium) {
            Text("任务设置")
                .font(.gedaHeadline)
                .foregroundColor(.gedaTextPrimary)
            
            HStack(spacing: 20) {
                // 优先级
                VStack(alignment: .leading, spacing: DesignTokens.Spacing.small) {
                    Text("优先级")
                        .font(.gedaCaption)
                        .foregroundColor(.gedaTextSecondary)
                    
                    let priority = Priority(rawValue: task.priority ?? "medium") ?? .medium
                    HStack {
                        Circle()
                            .fill(priority.color)
                            .frame(width: 12, height: 12)
                        
                        Text(priority.displayName)
                            .font(.gedaBody)
                            .foregroundColor(.gedaTextPrimary)
                    }
                }
                
                Spacer()
                
                // 番茄钟数量
                VStack(alignment: .trailing, spacing: DesignTokens.Spacing.small) {
                    Text("番茄钟")
                        .font(.gedaCaption)
                        .foregroundColor(.gedaTextSecondary)
                    
                    HStack {
                        Image(systemName: "timer")
                            .foregroundColor(.gedaAccentStart)
                        
                        Text("\(task.tomatoCount)")
                            .font(.gedaBody)
                            .foregroundColor(.gedaTextPrimary)
                    }
                }
            }
        }
        .padding(DesignTokens.Spacing.medium)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gedaBackgroundSecondary)
        )
    }
    
    private func planInfoSection(_ plan: Plan) -> some View {
        VStack(alignment: .leading, spacing: DesignTokens.Spacing.medium) {
            Text("所属计划")
                .font(.gedaHeadline)
                .foregroundColor(.gedaTextPrimary)
            
            HStack {
                Image(systemName: "folder")
                    .foregroundColor(.gedaAccentStart)
                
                Text(plan.title ?? "未命名计划")
                    .font(.gedaBody)
                    .foregroundColor(.gedaTextPrimary)
                
                Spacer()
            }
        }
        .padding(DesignTokens.Spacing.medium)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gedaBackgroundSecondary)
        )
    }
    
    private var reminderSection: some View {
        VStack(alignment: .leading, spacing: DesignTokens.Spacing.medium) {
            Text("提醒设置")
                .font(.gedaHeadline)
                .foregroundColor(.gedaTextPrimary)
            
            HStack {
                Image(systemName: task.isReminderEnabled ? "bell.fill" : "bell.slash")
                    .foregroundColor(task.isReminderEnabled ? .gedaAccentStart : .gedaTextSecondary)
                
                Text(task.isReminderEnabled ? "已开启提醒" : "未开启提醒")
                    .font(.gedaBody)
                    .foregroundColor(.gedaTextPrimary)
                
                Spacer()
            }
        }
        .padding(DesignTokens.Spacing.medium)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gedaBackgroundSecondary)
        )
    }
    
    private var actionButtonsSection: some View {
        VStack(spacing: DesignTokens.Spacing.medium) {
            // 完成/取消完成按钮
            Button(action: {
                viewModel.toggleTaskCompletion(task)
            }) {
                HStack {
                    Image(systemName: task.isCompleted ? "arrow.counterclockwise" : "checkmark")
                    Text(task.isCompleted ? "标记为未完成" : "标记为完成")
                }
                .frame(maxWidth: .infinity)
                .padding(DesignTokens.Spacing.medium)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(task.isCompleted ? Color.gedaWarning : Color.gedaSuccess)
                )
                .foregroundColor(.white)
                .font(.gedaBody.weight(.medium))
            }
            
            // 删除按钮
            Button(action: {
                viewModel.confirmDeleteTask(task)
                dismiss()
            }) {
                HStack {
                    Image(systemName: "trash")
                    Text("删除任务")
                }
                .frame(maxWidth: .infinity)
                .padding(DesignTokens.Spacing.medium)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.gedaError, lineWidth: 1)
                )
                .foregroundColor(.gedaError)
                .font(.gedaBody.weight(.medium))
            }
        }
    }
}

#Preview {
    let context = CoreDataManager.shared.viewContext
    let sampleTask = Task(context: context)
    sampleTask.id = UUID().uuidString
    sampleTask.title = "示例任务"
    sampleTask.startTime = Date()
    sampleTask.endTime = Date().addingTimeInterval(3600)
    sampleTask.priority = Priority.high.rawValue
    sampleTask.tomatoCount = 2
    sampleTask.isReminderEnabled = true
    sampleTask.isCompleted = false
    
    return TaskDetailView(
        viewModel: TaskViewModel(),
        task: sampleTask
    )
}
