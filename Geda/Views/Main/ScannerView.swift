//
//  ScannerView.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//  Updated by AI Assistant on 2025/7/14
//

import SwiftUI
import AVFoundation

/// 扫描视图 - 对应HTML原型中的scan.html
struct ScannerView: View {
    @StateObject private var scannerViewModel = ScannerViewModel()
    @State private var showingResult = false
    @State private var scannedResult = ""
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        ZStack {
            // 黑色背景
            Color.black
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 顶部导航栏
                headerView
                
                Spacer()
                
                // 扫描区域
                scanningArea
                
                Spacer()
                
                // 底部说明
                instructionText
            }
            .padding()
        }
        .navigationBarHidden(true)
        .onAppear {
            scannerViewModel.startScanning()
        }
        .onDisappear {
            scannerViewModel.stopScanning()
        }
        .alert("扫描结果", isPresented: $showingResult) {
            Button("确定") {
                showingResult = false
            }
            Button("继续扫描") {
                showingResult = false
                scannerViewModel.resumeScanning()
            }
        } message: {
            Text(scannedResult)
        }
        .onReceive(scannerViewModel.$scannedCode) { newValue in
            if !newValue.isEmpty {
                scannedResult = newValue
                showingResult = true
            }
        }
    }
    
    // MARK: - 顶部导航栏
    @ViewBuilder
    private var headerView: some View {
        HStack {
            Text("扫描二维码")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Spacer()
            
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .foregroundColor(.gray)
            }
        }
        .padding(.top, 20)
    }
    
    // MARK: - 扫描区域
    @ViewBuilder
    private var scanningArea: some View {
        ZStack {
            // 相机预览层
            if scannerViewModel.isCameraAuthorized {
                CameraPreviewView(scannerViewModel: scannerViewModel)
                    .frame(width: 256, height: 256)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
            } else {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 256, height: 256)
                    .overlay(
                        VStack(spacing: 12) {
                            Image(systemName: "camera.fill")
                                .font(.largeTitle)
                                .foregroundColor(.gray)
                            
                            Text("需要相机权限")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    )
            }
            
            // 扫描框传感器
            ScannerOverlayView()
                .frame(width: 256, height: 256)
        }
    }
    
    // MARK: - 底部说明
    @ViewBuilder
    private var instructionText: some View {
        Text("将二维码/条形码放入框内，即可自动扫描")
            .font(.subheadline)
            .foregroundColor(.gray)
            .multilineTextAlignment(.center)
            .padding(.bottom, 40)
    }
}

// MARK: - 辅助组件

/// 扫描覆盖层视图 - 精确复刻HTML原型scan.html中的扫描线动画
struct ScannerOverlayView: View {
    @State private var animationOffset: CGFloat = -128  // 对应CSS translateY(-100%)
    @State private var animationOpacity: Double = 0     // 对应CSS opacity: 0
    
    var body: some View {
        ZStack {
            // 四个角框 - 对应HTML中的Corner frames
            VStack {
                HStack {
                    CornerFrameView(corners: [.topLeft])
                    Spacer()
                    CornerFrameView(corners: [.topRight])
                }
                Spacer()
                HStack {
                    CornerFrameView(corners: [.bottomLeft])
                    Spacer()
                    CornerFrameView(corners: [.bottomRight])
                }
            }
            
            // 精确复刻CSS扫描线动画
            Rectangle()
                .fill(Color(hex: "#818cf8").opacity(0.5))  // 对应CSS border-indigo-400/50
                .frame(height: 1)  // 对应CSS h-px
                .opacity(animationOpacity)
                .offset(y: animationOffset)
                .onAppear {
                    startPreciseScanAnimation()
                }
        }
    }
    
    /// 启动精确的CSS映射扫描动画
    private func startPreciseScanAnimation() {
        // 创建复合动画，精确对应CSS keyframes
        withAnimation(
            .easeInOut(duration: 2.0)  // 对应CSS 2s ease-in-out
            .repeatForever(autoreverses: false)
        ) {
            // 动画序列：0% -> 50% -> 100%
            // 0%: translateY(-100%), opacity: 0
            // 50%: opacity: 1
            // 100%: translateY(100%), opacity: 0
            
            // 位置动画：从-100%到+100%
            animationOffset = 128  // 对应CSS translateY(100%)
        }
        
        // 透明度动画序列
        Timer.scheduledTimer(withTimeInterval: 0.0, repeats: true) { timer in
            // 第一阶段：0% -> 50% (0-1秒) opacity: 0 -> 1
            withAnimation(.easeInOut(duration: 1.0)) {
                animationOpacity = 1.0
            }
            
            // 第二阶段：50% -> 100% (1-2秒) opacity: 1 -> 0
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                withAnimation(.easeInOut(duration: 1.0)) {
                    animationOpacity = 0.0
                }
            }
            
            // 重置周期 (2秒后重新开始)
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                animationOffset = -128
                animationOpacity = 0.0
            }
        }
    }
}

/// 角框视图 - 对应HTML中的border-indigo-400样式
struct CornerFrameView: View {
    let corners: UIRectCorner
    
    var body: some View {
        RoundedRectangle(cornerRadius: 8)
            .stroke(Color(hex: "#818cf8"), lineWidth: 4)  // 对应CSS border-indigo-400
            .frame(width: 32, height: 32)
            .clipShape(
                CornerMask(corners: corners)
            )
    }
}

/// 角框遮罩
struct CornerMask: Shape {
    let corners: UIRectCorner
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        if corners.contains(.topLeft) {
            path.move(to: CGPoint(x: rect.minX, y: rect.minY + 16))
            path.addLine(to: CGPoint(x: rect.minX, y: rect.minY))
            path.addLine(to: CGPoint(x: rect.minX + 16, y: rect.minY))
        }
        
        if corners.contains(.topRight) {
            path.move(to: CGPoint(x: rect.maxX - 16, y: rect.minY))
            path.addLine(to: CGPoint(x: rect.maxX, y: rect.minY))
            path.addLine(to: CGPoint(x: rect.maxX, y: rect.minY + 16))
        }
        
        if corners.contains(.bottomLeft) {
            path.move(to: CGPoint(x: rect.minX, y: rect.maxY - 16))
            path.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
            path.addLine(to: CGPoint(x: rect.minX + 16, y: rect.maxY))
        }
        
        if corners.contains(.bottomRight) {
            path.move(to: CGPoint(x: rect.maxX - 16, y: rect.maxY))
            path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
            path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY - 16))
        }
        
        return path
    }
}

/// 相机预览视图
struct CameraPreviewView: UIViewRepresentable {
    let scannerViewModel: ScannerViewModel
    
    func makeUIView(context: Context) -> CameraPreviewUIView {
        let previewView = CameraPreviewUIView()
        previewView.scannerViewModel = scannerViewModel
        return previewView
    }
    
    func updateUIView(_ uiView: CameraPreviewUIView, context: Context) {
        // 更新逻辑
    }
}

/// 相机UIView封装
class CameraPreviewUIView: UIView {
    var scannerViewModel: ScannerViewModel? {
        didSet {
            setupPreviewLayer()
        }
    }
    
    private var previewLayer: AVCaptureVideoPreviewLayer?
    
    private func setupPreviewLayer() {
        guard let scannerViewModel = scannerViewModel,
              let session = scannerViewModel.captureSession else { return }
        
        previewLayer?.removeFromSuperlayer()
        
        let previewLayer = AVCaptureVideoPreviewLayer(session: session)
        previewLayer.frame = bounds
        previewLayer.videoGravity = .resizeAspectFill
        layer.addSublayer(previewLayer)
        
        self.previewLayer = previewLayer
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        previewLayer?.frame = bounds
    }
}

#Preview {
    NavigationView {
        ScannerView()
    }
}
