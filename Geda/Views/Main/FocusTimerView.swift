//
//  FocusTimerView.swift
//  Geda
//
//  Created by AI Assistant on 2025-07-14.
//

import SwiftUI

/// 番茄钟专注页面
/// 对应HTML原型中的focus-timer.html
struct FocusTimerView: View {
    @ObservedObject var focusViewModel: FocusTimerViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var showSettings = false
    @State private var showConfirmExit = false
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    // 导航栏
                    navigationHeader
                    
                    // 主要内容区域
                    ScrollView {
                        VStack(spacing: 40) {
                            Spacer(minLength: 20)
                            
                            // 任务信息
                            if let currentTask = focusViewModel.currentTask {
                                taskInfoSection(currentTask)
                            }
                            
                            // 计时器区域
                            timerSection
                            
                            // 控制按钮组
                            controlButtonsSection
                            
                            Spacer(minLength: 40)
                        }
                        .frame(minHeight: geometry.size.height - 60)
                    }
                    
                    // 底部设置区域
                    bottomSettingsSection
                }
            }
            .background(
                LinearGradient(
                    colors: [
                        Color(.systemBackground),
                        Color(.systemGroupedBackground)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .navigationBarHidden(true)
        }
        .onAppear {
            focusViewModel.prepareForSession()
        }
        .confirmationModal(
            isPresented: $showConfirmExit,
            title: "退出专注",
            message: "确定要退出当前的专注会话吗？进度将会丢失。",
            confirmText: "退出",
            cancelText: "继续",
            onConfirm: {
                focusViewModel.stopSession()
                dismiss()
            }
        )
    }
    
    // MARK: - 子视图
    
    private var navigationHeader: some View {
        HStack {
            Button(action: {
                if focusViewModel.isRunning {
                    showConfirmExit = true
                } else {
                    dismiss()
                }
            }) {
                Image(systemName: "chevron.left")
                    .font(.title2)
                    .foregroundColor(.gedaAccentStart)
            }
            
            Spacer()
            
            Text("专注中")
                .font(.headline)
                .fontWeight(.medium)
            
            Spacer()
            
            Button(action: { showSettings = true }) {
                Image(systemName: "gearshape")
                    .font(.title2)
                    .foregroundColor(.gedaAccentStart)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
    }
    
    private func taskInfoSection(_ task: Task) -> some View {
        VStack(spacing: 8) {
            Text(task.title ?? "专注任务")
                .font(.headline)
                .fontWeight(.medium)
                .multilineTextAlignment(.center)
                .lineLimit(2)
            
            if let plan = task.plan {
                Text(plan.title ?? "")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // 会话信息
            HStack(spacing: 20) {
                SessionInfoItem(
                    icon: "timer",
                    value: "\(focusViewModel.currentSessionIndex + 1)/\(focusViewModel.totalSessions)",
                    label: "会话"
                )
                
                SessionInfoItem(
                    icon: "checkmark.circle",
                    value: "\(focusViewModel.completedSessionsCount)",
                    label: "已完成"
                )
                
                SessionInfoItem(
                    icon: "target",
                    value: "\(task.tomatoCount)",
                    label: "目标"
                )
            }
            .padding(.top, 8)
        }
        .padding(.horizontal, 20)
    }
    
    private var timerSection: some View {
        VStack(spacing: 24) {
            // 环形进度条
            ZStack {
                CircularProgressView(
                    progress: focusViewModel.progress,
                    style: .large
                )
                .frame(width: 250, height: 250)
                
                VStack(spacing: 8) {
                    // 时间显示
                    Text(focusViewModel.timeString)
                        .font(.system(size: 48, weight: .light, design: .monospaced))
                        .foregroundColor(.primary)
                    
                    // 会话状态
                    Text(focusViewModel.sessionTypeText)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .fontWeight(.medium)
                }
            }
            
            // 会话进度指示器
            SessionProgressIndicator(
                currentSession: focusViewModel.currentSessionIndex,
                totalSessions: focusViewModel.totalSessions,
                completedSessions: focusViewModel.completedSessionsCount
            )
        }
    }
    
    private var controlButtonsSection: some View {
        HStack(spacing: 24) {
            // 重置按钮
            ControlButton(
                icon: "arrow.clockwise",
                text: "重置",
                style: .secondary,
                action: {
                    focusViewModel.resetSession()
                }
            )
            .disabled(focusViewModel.isRunning && focusViewModel.remainingTime > 0)
            
            // 主要控制按钮
            ControlButton(
                icon: focusViewModel.isRunning ? "pause.fill" : "play.fill",
                text: focusViewModel.isRunning ? "暂停" : "开始",
                style: .primary,
                action: {
                    focusViewModel.toggleTimer()
                }
            )
            
            // 跳过按钮
            ControlButton(
                icon: "forward.end",
                text: "跳过",
                style: .secondary,
                action: {
                    focusViewModel.skipSession()
                }
            )
            .disabled(!focusViewModel.isRunning)
        }
        .padding(.horizontal, 20)
    }
    
    private var bottomSettingsSection: some View {
        VStack(spacing: 16) {
            Divider()
            
            HStack(spacing: 32) {
                // 工作时间设置
                TimerSettingItem(
                    title: "工作",
                    value: focusViewModel.workDurationMinutes,
                    unit: "分钟",
                    onDecrease: { focusViewModel.decreaseWorkTime() },
                    onIncrease: { focusViewModel.increaseWorkTime() },
                    isEnabled: !focusViewModel.isRunning
                )
                
                // 休息时间设置
                TimerSettingItem(
                    title: "休息",
                    value: focusViewModel.breakDurationMinutes,
                    unit: "分钟",
                    onDecrease: { focusViewModel.decreaseBreakTime() },
                    onIncrease: { focusViewModel.increaseBreakTime() },
                    isEnabled: !focusViewModel.isRunning
                )
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
        .background(Color(.systemBackground))
    }
}

/// 会话信息项
struct SessionInfoItem: View {
    let icon: String
    let value: String
    let label: String
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(.gedaAccentStart)
            
            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

/// 会话进度指示器
struct SessionProgressIndicator: View {
    let currentSession: Int
    let totalSessions: Int
    let completedSessions: Int
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(0..<totalSessions, id: \.self) { index in
                Circle()
                    .fill(indicatorColor(for: index))
                    .frame(width: 12, height: 12)
                    .scaleEffect(index == currentSession ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 0.3), value: currentSession)
            }
        }
        .padding(.horizontal, 20)
    }
    
    private func indicatorColor(for index: Int) -> Color {
        if index < completedSessions {
            return .gedaAccentStart
        } else if index == currentSession {
            return .gedaAccentStart.opacity(0.7)
        } else {
            return Color(.systemGray4)
        }
    }
}

/// 控制按钮
struct ControlButton: View {
    let icon: String
    let text: String
    let style: ControlButtonStyle
    let action: () -> Void
    
    enum ControlButtonStyle {
        case primary, secondary
    }
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .fontWeight(.medium)
                
                Text(text)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(style == .primary ? .white : .gedaAccentStart)
            .frame(width: 80, height: 80)
            .background(
                style == .primary 
                    ? AnyView(LinearGradient.gedaGradient)
                    : AnyView(Color.white)
            )
            .cornerRadius(40)
            .shadow(
                color: .black.opacity(0.1),
                radius: 8,
                x: 0,
                y: 4
            )
        }
        .scaleEffect(1.0)
        .animation(.easeInOut(duration: 0.1), value: false)
    }
}

/// 计时器设置项
struct TimerSettingItem: View {
    let title: String
    let value: Int
    let unit: String
    let onDecrease: () -> Void
    let onIncrease: () -> Void
    let isEnabled: Bool
    
    var body: some View {
        VStack(spacing: 12) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            HStack(spacing: 16) {
                Button(action: onDecrease) {
                    Image(systemName: "minus")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(isEnabled ? .gedaAccentStart : .gray)
                }
                .disabled(!isEnabled)
                
                VStack(spacing: 2) {
                    Text("\(value)")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text(unit)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(minWidth: 50)
                
                Button(action: onIncrease) {
                    Image(systemName: "plus")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(isEnabled ? .gedaAccentStart : .gray)
                }
                .disabled(!isEnabled)
            }
        }
    }
}

/// CircularProgressView扩展，支持大尺寸样式
extension CircularProgressView {
    enum Style {
        case standard, large
        
        var lineWidth: CGFloat {
            switch self {
            case .standard: return 8
            case .large: return 12
            }
        }
    }
    
    init(progress: Double, style: Style) {
        self.init(
            progress: progress,
            lineWidth: style.lineWidth,
            colors: [Color.gedaAccentStart, Color.gedaAccentEnd]
        )
    }
}

/// SwiftUI预览
struct FocusTimerView_Previews: PreviewProvider {
    static var previews: some View {
        FocusTimerView(
            focusViewModel: FocusTimerViewModel()
        )
    }
}