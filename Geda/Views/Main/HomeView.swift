//
//  HomeView.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//  Updated by AI Assistant on 2025/7/14
//

import SwiftUI
import CoreData

/// 主页视图 - 应用入口页面
/// 对应HTML原型中的home.html
struct HomeView: View {
    @Environment(\.managedObjectContext) private var viewContext
    
    // ViewModels (通过环境对象注入)
    @EnvironmentObject var homeViewModel: HomeViewModel
    @EnvironmentObject var planViewModel: PlanViewModel
    @EnvironmentObject var taskViewModel: TaskViewModel
    @EnvironmentObject var focusTimerViewModel: FocusTimerViewModel
    @EnvironmentObject var reviewViewModel: ReviewViewModel

    @State private var selectedTab: HomeTab = .schedule
    @State private var showAddMenu = false
    @State private var showNewTaskModal = false
    @State private var showNewPlanModal = false
    @State private var showVoiceInputModal = false
    @State private var showOnboardingOverlay = false

    var body: some View {
        VStack(spacing: 0) {
            // 主内容区域 - 使用LazyVStack优化渲染性能
            ScrollView {
                LazyVStack(spacing: DesignTokens.Spacing.lg, pinnedViews: [.sectionHeaders]) {
                    // 顶部导航切换 (日程/计划) - 固定头部
                    Section {
                        // 根据选中的Tab显示不同内容
                        if selectedTab == .schedule {
                            scheduleContent
                        } else {
                            planContent
                        }
                    } header: {
                        TopNavigationSwitch(selectedTab: $selectedTab)
                            .padding(.top, DesignTokens.Spacing.lg)
                            .background(Color(.systemBackground))
                            .onTapGesture {
                                // 点击计划时跳转到计划页面
                                if selectedTab == .plan {
                                    // 导航到计划页面的逻辑在顶部导航组件中处理
                                }
                            }
                    }
                }
                .padding(.horizontal, DesignTokens.Spacing.lg)
            }

            // 底部操作栏
            BottomActionBar(
                showAddMenu: $showAddMenu,
                onNewTask: {
                    showNewTaskModal = true
                },
                onNewPlan: {
                    showNewPlanModal = true
                },
                onVoiceInput: {
                    showVoiceInputModal = true
                }
            )
            .padding(.bottom, DesignTokens.Spacing.lg)
        }
        .navigationBarHidden(true) // 隐藏默认导航栏
        .background(Color.gedaBackgroundPrimary)
        .onAppear {
            homeViewModel.loadTodayTasks()
            taskViewModel.loadTasks()
            
            // 检查是否首次启动
            let hasSeenOnboarding = UserDefaults.standard.bool(forKey: "hasSeenOnboarding")
            if !hasSeenOnboarding {
                // 延迟500ms显示引导遮罩 - 对应HTML中的延迟显示
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    showOnboardingOverlay = true
                }
            }
        }
        .overlay(
            // 首次启动引导遮罩
            OnboardingOverlay(isPresented: $showOnboardingOverlay)
                .opacity(showOnboardingOverlay ? 1 : 0)
                .allowsHitTesting(showOnboardingOverlay)
        )
        .overlay(
            // 点击外部区域关闭悬浮菜单 - 对应HTML的window.addEventListener('click')
            Color.clear
                .contentShape(Rectangle())
                .onTapGesture {
                    if showAddMenu {
                        withAnimation(.easeOut(duration: 0.15)) {
                            showAddMenu = false
                        }
                    }
                }
                .allowsHitTesting(showAddMenu)
        )
        .performanceMonitored(viewName: "HomeView")
        .modal(isPresented: $showNewTaskModal) {
            NewTaskModal(
                isPresented: $showNewTaskModal,
                taskViewModel: taskViewModel
            )
        }
        .modal(isPresented: $showNewPlanModal) {
            NewPlanModal(
                isPresented: $showNewPlanModal,
                planViewModel: planViewModel
            )
        }
        .modal(isPresented: $showVoiceInputModal) {
            VoiceInputModal(
                isPresented: $showVoiceInputModal,
                taskViewModel: taskViewModel
            )
        }
        .sheet(isPresented: $taskViewModel.showTaskDetail) {
            if let selectedTask = taskViewModel.selectedTask {
                TaskDetailView(viewModel: TaskViewModel(), task: selectedTask)
            }
        }
        .alert("删除任务", isPresented: $taskViewModel.showDeleteConfirmation) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                if let task = taskViewModel.selectedTask {
                    taskViewModel.deleteTask(task)
                }
            }
        } message: {
            Text("确定要删除这个任务吗？此操作无法撤销。")
        }
    }
    
    // 日程内容
    @ViewBuilder
    private var scheduleContent: some View {
        // 紧凑日历组件
        CompactCalendar()

        // 今日提醒卡片
        TodayReminderCard()

        // 今日任务区域
        VStack(alignment: .leading, spacing: DesignTokens.Spacing.sm) {
            // 任务标题和操作
            TaskSectionHeader(
                title: "今日任务",
                taskCount: homeViewModel.todayTasks.count,
                onAddTask: {
                    showNewTaskModal = true
                },
                onSortChanged: { sortOption, ascending in
                    taskViewModel.setSortOption(sortOption, ascending: ascending)
                },
                onFilterChanged: { priority, completion in
                    taskViewModel.setFilterPriority(priority)
                    taskViewModel.setFilterCompleted(completion)
                }
            )

            if homeViewModel.isLoading {
                // 加载状态
                HStack {
                    Spacer()
                    ProgressView()
                        .scaleEffect(1.2)
                    Spacer()
                }
                .padding(.vertical, DesignTokens.Spacing.xl)
            } else if homeViewModel.todayTasks.isEmpty {
                // 空状态
                VStack(spacing: DesignTokens.Spacing.lg) {
                    Image(systemName: "checkmark.circle")
                        .font(.system(size: 48))
                        .foregroundColor(.gedaTextSecondary)

                    Text("暂无任务")
                        .font(.gedaBody)
                        .foregroundColor(.gedaTextSecondary)

                    Button("创建第一个任务") {
                        showNewTaskModal = true
                    }
                    .foregroundColor(.gedaAccentStart)
                    .font(.gedaBody)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, DesignTokens.Spacing.xxxl)
            } else {
                // 堆叠任务卡片
                StackedTaskCards(
                    tasks: taskViewModel.filteredTasks,
                    onTaskTap: { task in
                        taskViewModel.selectedTask = task
                        taskViewModel.showTaskDetail = true
                    },
                    onTaskComplete: { task in
                        homeViewModel.toggleTaskCompletion(task)
                    }
                )
                
                // 快速操作区域
                quickActionsSection
            }
        }
        
        // 错误提示
        if let errorMessage = homeViewModel.errorMessage {
            Text(errorMessage)
                .foregroundColor(.red)
                .font(.caption)
                .padding(.top, DesignTokens.Spacing.sm)
        }
    }
    
    // 计划内容 - 跳转到计划页面
    @ViewBuilder
    private var planContent: some View {
        VStack(spacing: DesignTokens.Spacing.lg) {
            Image(systemName: "folder.badge.plus")
                .font(.system(size: 64))
                .foregroundColor(.gedaAccentStart)

            VStack(spacing: DesignTokens.Spacing.sm) {
                Text("计划管理")
                    .font(.gedaHeadline)
                    .foregroundColor(.gedaTextPrimary)

                Text("创建和管理您的学习计划")
                    .font(.gedaBody)
                    .foregroundColor(.gedaTextSecondary)
                    .multilineTextAlignment(.center)
            }
            
            NavigationLink(destination: PlanView(planViewModel: planViewModel)) {
                Text("查看所有计划")
                    .font(.gedaBody)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, DesignTokens.Spacing.xl)
                    .padding(.vertical, DesignTokens.Spacing.md)
                    .background(LinearGradient.gedaGradient)
                    .cornerRadius(12)
            }
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .padding(.vertical, DesignTokens.Spacing.xl)
    }
    
    // 快速操作区域
    @ViewBuilder
    private var quickActionsSection: some View {
        VStack(spacing: DesignTokens.Spacing.md) {
            HStack {
                Text("快速操作")
                    .font(.gedaSubheadline)
                    .foregroundColor(.gedaTextPrimary)
                Spacer()
            }
            
            HStack(spacing: DesignTokens.Spacing.md) {
                // 开始专注
                NavigationLink(destination: FocusTimerView(focusViewModel: focusTimerViewModel)) {
                    QuickActionCard(
                        icon: "timer",
                        title: "开始专注",
                        subtitle: "番茄钟计时",
                        color: .gedaAccentStart
                    )
                }
                
                // 查看日历
                NavigationLink(destination: CalendarView()) {
                    QuickActionCard(
                        icon: "calendar",
                        title: "查看日历",
                        subtitle: "任务安排",
                        color: .blue
                    )
                }
            }
            
            HStack(spacing: DesignTokens.Spacing.md) {
                // 数据复盘
                NavigationLink(destination: ReviewView()) {
                    QuickActionCard(
                        icon: "chart.bar",
                        title: "数据复盘",
                        subtitle: "学习统计",
                        color: .green
                    )
                }
                
                // 个人中心
                NavigationLink(destination: ProfileView()) {
                    QuickActionCard(
                        icon: "person.circle",
                        title: "个人中心",
                        subtitle: "设置资料",
                        color: .orange
                    )
                }
            }
        }
        .padding(.top, DesignTokens.Spacing.lg)
    }
}

/// 快速操作卡片
struct QuickActionCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    
    var body: some View {
        VStack(spacing: DesignTokens.Spacing.xs) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(title)
                .font(.gedaCaptionMedium)
                .foregroundColor(.gedaTextPrimary)
            
            Text(subtitle)
                .font(.gedaCaptionSmall)
                .foregroundColor(.gedaTextSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, DesignTokens.Spacing.sm)
        .background(Color.white)
        .cornerRadius(DesignTokens.CornerRadius.small)
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
    }
}

#Preview {
    let coreDataManager = CoreDataManager.shared
    
    NavigationView {
        HomeView()
            .environmentObject(HomeViewModel())
            .environmentObject(PlanViewModel())
            .environmentObject(TaskViewModel())
            .environmentObject(FocusTimerViewModel())
            .environmentObject(ReviewViewModel())
            .environment(\.managedObjectContext, coreDataManager.viewContext)
    }
}
