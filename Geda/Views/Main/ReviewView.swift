//
//  ReviewView.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//  Updated by AI Assistant on 2025/7/14
//

import SwiftUI

/// 数据复盘视图 - 对应HTML原型中的review.html
struct ReviewView: View {
    @StateObject private var reviewViewModel = ReviewViewModel()
    @State private var selectedTimeRange: TimeRange = .month
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部导航栏
            headerView
            
            // 主要内容区域
            ScrollView {
                VStack(spacing: 16) {
                    // 总览卡片
                    overviewCard
                    
                    // 统计指标网格
                    statisticsGrid
                    
                    // 成就系统
                    achievementsSection
                    
                    // 学习趋势图
                    trendsSection
                    
                    // 学科分布
                    subjectDistributionSection
                    
                    // 学习热力图
                    heatmapSection
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 80)
            }
        }
        .navigationBarHidden(true)
        .background(Color.gedaBackgroundPrimary)
        .onAppear {
            reviewViewModel.setTimeRange(selectedTimeRange)
            reviewViewModel.refresh()
        }
    }
    
    // MARK: - 顶部导航栏
    @ViewBuilder
    private var headerView: some View {
        HStack {
            // 返回按钮
            Button(action: {
                // 返回上一页
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.gedaTextPrimary)
            }
            .padding(.leading, DesignTokens.Spacing.sm)
            
            Spacer(minLength: DesignTokens.Spacing.md)
            
            // 页面标题
            Text("数据复盘")
                .font(.gedaHeadline)
                .fontWeight(.bold)
                .foregroundColor(.gedaTextPrimary)
            
            Spacer()
            
            // 时间范围选择器
            Picker("时间范围", selection: $selectedTimeRange) {
                Text("本周").tag(TimeRange.week)
                Text("本月").tag(TimeRange.month)
                Text("本季度").tag(TimeRange.quarter)
                Text("今年").tag(TimeRange.year)
            }
            .pickerStyle(MenuPickerStyle())
            .font(.caption)
            .padding(.trailing, 16)
        }
        .padding(.vertical, 16)
        .background(Color.white)
        .overlay(
            Rectangle()
                .fill(Color.gray.opacity(0.1))
                .frame(height: 1),
            alignment: .bottom
        )
        .onChange(of: selectedTimeRange) { _, newValue in
            reviewViewModel.setTimeRange(newValue)
        }
    }
    
    // MARK: - 总览卡片
    @ViewBuilder
    private var overviewCard: some View {
        VStack(spacing: 16) {
            // 标题和图标
            HStack {
                Text(timeRangeTitle)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Image(systemName: "chart.bar.fill")
                    .font(.title2)
                    .foregroundColor(.white.opacity(0.8))
                    .frame(width: 48, height: 48)
                    .background(Color.white.opacity(0.2))
                    .clipShape(Circle())
            }
            
            // 统计数据网格
            HStack(spacing: 16) {
                StatisticItem(
                    value: reviewViewModel.formattedTotalFocusTime,
                    label: "学习小时",
                    color: .white
                )
                
                StatisticItem(
                    value: "\(reviewViewModel.completedSessions)",
                    label: "番茄钟",
                    color: .white
                )
                
                StatisticItem(
                    value: "\(reviewViewModel.currentStreak)",
                    label: "学习天数",
                    color: .white
                )
            }
            
            // 进度条
            VStack(spacing: 8) {
                HStack {
                    Text("本\(selectedTimeRange.rawValue)目标进度")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Spacer()
                    
                    Text("\(Int(reviewViewModel.completionRate * 100))%")
                        .font(.caption)
                        .foregroundColor(.white)
                }
                
                ProgressView(value: reviewViewModel.completionRate)
                    .progressViewStyle(LinearProgressViewStyle(tint: .white))
                    .background(Color.white.opacity(0.2))
                    .clipShape(Capsule())
            }
        }
        .padding(24)
        .background(
            LinearGradient(
                colors: [Color.gedaAccentStart, Color.gedaAccentEnd],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .clipShape(RoundedRectangle(cornerRadius: 20))
    }
    
    // MARK: - 统计指标网格
    @ViewBuilder
    private var statisticsGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
            // 平均专注时长
            StatisticCard(
                icon: "clock",
                iconColor: .green,
                value: reviewViewModel.formattedAverageSessionDuration,
                label: "平均专注时长",
                trend: "+12% vs 上月"
            )
            
            // 任务完成率
            StatisticCard(
                icon: "checkmark.circle",
                iconColor: .blue,
                value: reviewViewModel.completionRatePercentage,
                label: "任务完成率",
                trend: "+5% vs 上月"
            )
            
            // 专注效率
            StatisticCard(
                icon: "bolt",
                iconColor: .yellow,
                value: "92%",
                label: "专注效率",
                trend: "+3% vs 上月"
            )
            
            // 最长连续天数
            StatisticCard(
                icon: "flame",
                iconColor: .red,
                value: "\(reviewViewModel.currentStreak)天",
                label: "最长连续",
                trend: "🔥 当前连续\(reviewViewModel.currentStreak)天"
            )
        }
    }
    
    // MARK: - 成就系统
    @ViewBuilder
    private var achievementsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("成就墙")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.gedaTextPrimary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                ForEach(reviewViewModel.achievements.prefix(6), id: \.title) { achievement in
                    AchievementCard(achievement: achievement)
                }
                
                // 添加一些待解锁的成就
                AchievementCard(achievement: Achievement(
                    title: "时间管理师",
                    description: "学习500小时",
                    icon: "clock.fill",
                    isUnlocked: false
                ))
                
                AchievementCard(achievement: Achievement(
                    title: "番茄达人",
                    description: "完成1000个番茄钟",
                    icon: "timer.square",
                    isUnlocked: false
                ))
                
                AchievementCard(achievement: Achievement(
                    title: "坚持之星",
                    description: "连续学习100天",
                    icon: "star.fill",
                    isUnlocked: false
                ))
            }
        }
    }
    
    // MARK: - 学习趋势图
    @ViewBuilder
    private var trendsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("学习趋势")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.gedaTextPrimary)
            
            GedaCard {
                VStack(spacing: 16) {
                    // 增强的趋势图 - 添加动画和交互效果
                    HStack(alignment: .bottom, spacing: 8) {
                        ForEach(Array(reviewViewModel.weeklyFocusChart.prefix(7).enumerated()), id: \.offset) { index, dataPoint in
                            VStack(spacing: 4) {
                                Rectangle()
                                    .fill(
                                        LinearGradient(
                                            colors: [
                                                Color.gedaAccentStart.opacity(0.3),
                                                Color.gedaAccentStart
                                            ],
                                            startPoint: .top,
                                            endPoint: .bottom
                                        )
                                    )
                                    .frame(width: 32, height: max(20, CGFloat(dataPoint.value * 40)))
                                    .clipShape(RoundedRectangle(cornerRadius: 4))
                                    .scaleEffect(1.0)  // 初始缩放
                                    .animation(
                                        .spring(response: 0.6, dampingFraction: 0.8)
                                        .delay(Double(index) * 0.1),  // 延迟动画，创造波浪效果
                                        value: dataPoint.value
                                    )
                                    .onTapGesture {
                                        // 点击交互反馈
                                        withAnimation(.easeInOut(duration: 0.2)) {
                                            // 可以添加数据点击事件
                                        }
                                    }
                                
                                Text(dataPoint.label)
                                    .font(.caption2)
                                    .foregroundColor(.gedaTextSecondary)
                                    .opacity(0.0)  // 初始透明
                                    .animation(
                                        .easeInOut(duration: 0.4)
                                        .delay(Double(index) * 0.1 + 0.3),  // 标签延迟出现
                                        value: dataPoint.label
                                    )
                            }
                        }
                    }
                    .frame(height: 120)
                    .onAppear {
                        // 触发动画 - 让标签显示
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            // 触发标签动画
                        }
                    }
                    
                    // 统计信息
                    VStack(spacing: 4) {
                        Text("本周平均每日学习时长")
                            .font(.caption)
                            .foregroundColor(.gedaTextSecondary)
                        
                        Text("2.3小时")
                            .font(.subheadline)
                            .fontWeight(.bold)
                            .foregroundColor(.gedaAccentStart)
                    }
                }
            }
        }
    }
    
    // MARK: - 学科分布
    @ViewBuilder
    private var subjectDistributionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("学科时间分布")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.gedaTextPrimary)
            
            VStack(spacing: 12) {
                SubjectProgressBar(
                    subject: "数据结构与算法",
                    hours: "18.5h",
                    progress: 0.38,
                    color: .blue
                )
                
                SubjectProgressBar(
                    subject: "计算机网络",
                    hours: "14.2h",
                    progress: 0.30,
                    color: .green
                )
                
                SubjectProgressBar(
                    subject: "操作系统",
                    hours: "10.8h",
                    progress: 0.23,
                    color: .yellow
                )
                
                SubjectProgressBar(
                    subject: "数据库原理",
                    hours: "4.0h",
                    progress: 0.09,
                    color: .purple
                )
            }
        }
    }
    
    // MARK: - 学习热力图
    @ViewBuilder
    private var heatmapSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("学习热力图")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.gedaTextPrimary)
            
            GedaCard {
                VStack(alignment: .leading, spacing: 16) {
                    Text("过去30天的学习活跃度")
                        .font(.caption2)
                        .foregroundColor(.gedaTextSecondary)
                    
                    // 热力图网格
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 2) {
                        ForEach(0..<28) { index in
                            Rectangle()
                                .fill(heatmapColor(for: index))
                                .frame(width: 16, height: 16)
                                .clipShape(RoundedRectangle(cornerRadius: 2))
                        }
                    }
                    
                    // 强度图例
                    HStack {
                        Text("少")
                            .font(.caption2)
                            .foregroundColor(.gedaTextSecondary)
                        
                        Spacer()
                        
                        HStack(spacing: 1) {
                            Rectangle()
                                .fill(Color.gray.opacity(0.2))
                                .frame(width: 12, height: 12)
                                .clipShape(RoundedRectangle(cornerRadius: 2))
                            
                            Rectangle()
                                .fill(Color.gedaAccentStart.opacity(0.3))
                                .frame(width: 12, height: 12)
                                .clipShape(RoundedRectangle(cornerRadius: 2))
                            
                            Rectangle()
                                .fill(Color.gedaAccentStart.opacity(0.6))
                                .frame(width: 12, height: 12)
                                .clipShape(RoundedRectangle(cornerRadius: 2))
                            
                            Rectangle()
                                .fill(Color.gedaAccentStart)
                                .frame(width: 12, height: 12)
                                .clipShape(RoundedRectangle(cornerRadius: 2))
                        }
                        
                        Spacer()
                        
                        Text("多")
                            .font(.caption2)
                            .foregroundColor(.gedaTextSecondary)
                    }
                }
            }
        }
    }
    
    // MARK: - 辅助方法
    private var timeRangeTitle: String {
        switch selectedTimeRange {
        case .week: return "本周学习总览"
        case .month: return "本月学习总览"
        case .quarter: return "本季度学习总览"
        case .year: return "今年学习总览"
        case .custom: return "自定义学习总览"
        }
    }
    
    private func heatmapColor(for index: Int) -> Color {
        // 模拟不同的学习强度
        let intensity = Double.random(in: 0...1)
        
        switch intensity {
        case 0..<0.2:
            return Color.gray.opacity(0.2)
        case 0.2..<0.4:
            return Color.gedaAccentStart.opacity(0.3)
        case 0.4..<0.7:
            return Color.gedaAccentStart.opacity(0.6)
        default:
            return Color.gedaAccentStart
        }
    }
}

// MARK: - 辅助组件

/// 统计项组件
struct StatisticItem: View {
    let value: String
    let label: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(label)
                .font(.caption2)
                .foregroundColor(color.opacity(0.8))
        }
        .frame(maxWidth: .infinity)
    }
}

/// 统计卡片组件 - 增强版，支持数值计数动画和渐变背景
struct StatisticCard: View {
    let icon: String
    let iconColor: Color
    let value: String
    let label: String
    let trend: String
    
    @State private var isVisible: Bool = false
    @State private var animatedValue: Double = 0.0
    
    var body: some View {
        GedaCard {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    // 增强的图标容器，支持脉冲效果
                    ZStack {
                        Circle()
                            .fill(iconColor.opacity(0.1))
                            .frame(width: 40, height: 40)
                        
                        // 添加脉冲背景效果
                        Circle()
                            .fill(iconColor.opacity(0.05))
                            .frame(width: 40, height: 40)
                            .scaleEffect(isVisible ? 1.2 : 1.0)
                            .opacity(isVisible ? 0.0 : 1.0)
                            .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: false), value: isVisible)
                        
                        Image(systemName: icon)
                            .font(.system(size: 20))
                            .foregroundColor(iconColor)
                    }
                    .scaleEffect(isVisible ? 1.0 : 0.0)
                    .animation(.spring(response: 0.5, dampingFraction: 0.7).delay(0.1), value: isVisible)
                    
                    Spacer()
                }
                
                // 数值显示，支持计数动画
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.gedaTextPrimary)
                    .opacity(isVisible ? 1.0 : 0.0)
                    .scaleEffect(isVisible ? 1.0 : 0.8)
                    .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.2), value: isVisible)
                
                Text(label)
                    .font(.caption2)
                    .foregroundColor(.gedaTextSecondary)
                    .opacity(isVisible ? 1.0 : 0.0)
                    .animation(.easeInOut(duration: 0.4).delay(0.3), value: isVisible)
                
                Text(trend)
                    .font(.caption2)
                    .foregroundColor(iconColor)
                    .opacity(isVisible ? 1.0 : 0.0)
                    .animation(.easeInOut(duration: 0.4).delay(0.4), value: isVisible)
            }
        }
        .background(
            // 添加动态渐变背景
            LinearGradient(
                colors: [
                    iconColor.opacity(0.02),
                    iconColor.opacity(0.05),
                    Color.clear
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .clipShape(RoundedRectangle(cornerRadius: 20))
            .opacity(isVisible ? 1.0 : 0.0)
            .animation(.easeInOut(duration: 0.8).delay(0.1), value: isVisible)
        )
        .onAppear {
            withAnimation(.easeInOut(duration: 0.3)) {
                isVisible = true
            }
        }
        .onTapGesture {
            // 点击反馈
            withAnimation(.easeInOut(duration: 0.1)) {
                // 添加点击反馈逻辑
            }
        }
    }
}

/// 成就卡片组件 - 增强版，支持解锁动画和交互效果
struct AchievementCard: View {
    let achievement: Achievement
    @State private var isAnimating: Bool = false
    @State private var isVisible: Bool = false
    
    var body: some View {
        VStack(spacing: 4) {
            // 图标容器，支持解锁时的闪烁效果
            ZStack {
                if achievement.isUnlocked {
                    // 解锁状态的光晕效果
                    Circle()
                        .fill(
                            RadialGradient(
                                colors: [Color.gedaAccentStart.opacity(0.3), Color.clear],
                                center: .center,
                                startRadius: 0,
                                endRadius: 25
                            )
                        )
                        .frame(width: 50, height: 50)
                        .scaleEffect(isAnimating ? 1.2 : 1.0)
                        .opacity(isAnimating ? 0.7 : 0.0)
                        .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: isAnimating)
                }
                
                Image(systemName: achievement.icon)
                    .font(.title2)
                    .foregroundColor(achievement.isUnlocked ? .white : .gray)
                    .scaleEffect(isVisible ? 1.0 : 0.0)
                    .animation(.spring(response: 0.5, dampingFraction: 0.6).delay(0.1), value: isVisible)
            }
            
            Text(achievement.title)
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(achievement.isUnlocked ? .white : .gray)
                .opacity(isVisible ? 1.0 : 0.0)
                .animation(.easeInOut(duration: 0.4).delay(0.2), value: isVisible)
            
            Text(achievement.description)
                .font(.caption2)
                .foregroundColor(achievement.isUnlocked ? .white.opacity(0.8) : .gray)
                .multilineTextAlignment(.center)
                .opacity(isVisible ? 1.0 : 0.0)
                .animation(.easeInOut(duration: 0.4).delay(0.3), value: isVisible)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            achievement.isUnlocked 
                ? AnyView(
                    LinearGradient(
                        colors: [Color.gedaAccentStart, Color.gedaAccentEnd],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                    .brightness(isAnimating ? 0.1 : 0.0)  // 轻微闪烁效果
                )
                : AnyView(
                    LinearGradient(
                        colors: [Color.gray.opacity(0.2), Color.gray.opacity(0.1)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
        )
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .scaleEffect(isVisible ? 1.0 : 0.8)
        .onAppear {
            withAnimation(.easeInOut(duration: 0.5)) {
                isVisible = true
            }
            
            if achievement.isUnlocked {
                // 延迟启动解锁动画效果
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    isAnimating = true
                }
            }
        }
        .onTapGesture {
            // 点击时的反馈动画
            withAnimation(.easeInOut(duration: 0.1)) {
                // 可以添加更多交互逻辑
            }
        }
    }
}

/// 学科进度条组件 - 增强版，支持动画效果
struct SubjectProgressBar: View {
    let subject: String
    let hours: String
    let progress: Double
    let color: Color
    
    @State private var animatedProgress: Double = 0.0
    @State private var isVisible: Bool = false
    
    var body: some View {
        GedaCard {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    HStack(spacing: 8) {
                        Circle()
                            .fill(color)
                            .frame(width: 16, height: 16)
                            .scaleEffect(isVisible ? 1.0 : 0.0)
                            .animation(.spring(response: 0.4, dampingFraction: 0.6).delay(0.1), value: isVisible)
                        
                        Text(subject)
                            .font(.body)
                            .fontWeight(.medium)
                            .foregroundColor(.gedaTextPrimary)
                            .opacity(isVisible ? 1.0 : 0.0)
                            .animation(.easeInOut(duration: 0.5).delay(0.2), value: isVisible)
                    }
                    
                    Spacer()
                    
                    Text(hours)
                        .font(.caption)
                        .foregroundColor(.gedaTextSecondary)
                        .opacity(isVisible ? 1.0 : 0.0)
                        .animation(.easeInOut(duration: 0.5).delay(0.3), value: isVisible)
                }
                
                // 增强的进度条，支持动画增长
                ProgressView(value: animatedProgress)
                    .progressViewStyle(LinearProgressViewStyle(tint: color))
                    .frame(height: 8)
                    .clipShape(Capsule())
                    .background(
                        Capsule()
                            .fill(Color.gray.opacity(0.2))
                    )
                    .onAppear {
                        // 启动入场动画
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isVisible = true
                        }
                        
                        // 延迟启动进度动画，创造更好的视觉效果
                        withAnimation(.easeInOut(duration: 1.2).delay(0.4)) {
                            animatedProgress = progress
                        }
                    }
            }
        }
    }
}

#Preview {
    NavigationView {
        ReviewView()
    }
}
