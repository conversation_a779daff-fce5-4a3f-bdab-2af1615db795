//
//  ContentView.swift
//  Geda
//
//  Created by 杨瑞光 on 2025/7/10.
//  Updated by AI Assistant on 2025/7/14
//

import SwiftUI

/// 主容器视图
/// 基于NavigationView的页面跳转导航系统（对应HTML原型设计）
/// 支持Apple ID认证和CloudKit同步
struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject private var authService: AuthenticationService
    @EnvironmentObject private var coreDataManager: CoreDataManager
    @EnvironmentObject private var cloudKitManager: CloudKitManager
    
    // ViewModels (依赖注入)
    @StateObject private var homeViewModel = HomeViewModel()
    @StateObject private var planViewModel = PlanViewModel()
    @StateObject private var taskViewModel = TaskViewModel()
    @StateObject private var focusTimerViewModel = FocusTimerViewModel()
    @StateObject private var reviewViewModel = ReviewViewModel()

    var body: some View {
        Group {
            if authService.isAuthenticated {
                // 已认证用户 - 显示主应用界面
                authenticatedView
            } else {
                // 未认证用户 - 显示登录界面
                SignInView()
            }
        }
        .overlay(
            syncStatusOverlay,
            alignment: .top
        )
    }
    
    // MARK: - 子视图
    
    /// 已认证用户的主界面
    private var authenticatedView: some View {
        NavigationView {
            // HomeView作为主入口页面
            HomeView()
                .environmentObject(homeViewModel)
                .environmentObject(planViewModel)
                .environmentObject(taskViewModel)
                .environmentObject(focusTimerViewModel)
                .environmentObject(reviewViewModel)
                .environment(\.managedObjectContext, viewContext)
        }
        .navigationViewStyle(StackNavigationViewStyle()) // 强制单栈导航样式
        .accentColor(.gedaAccentStart)
    }
    
    /// 同步状态覆盖层
    @ViewBuilder
    private var syncStatusOverlay: some View {
        if coreDataManager.syncInProgress {
            HStack(spacing: 8) {
                ProgressView()
                    .scaleEffect(0.7)
                
                Text("正在同步...")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(.regularMaterial, in: Capsule())
            .padding(.top, 8)
        }
    }
}

/// 认证状态指示器
struct AuthStatusIndicator: View {
    @EnvironmentObject private var authService: AuthenticationService
    @EnvironmentObject private var cloudKitManager: CloudKitManager
    
    var body: some View {
        HStack(spacing: 4) {
            // 认证状态图标
            Image(systemName: authService.isAuthenticated ? "person.fill.checkmark" : "person.fill.questionmark")
                .foregroundColor(authService.isAuthenticated ? .green : .orange)
                .font(.caption)
            
            // 同步状态图标
            if authService.isAuthenticated {
                Image(systemName: cloudKitManager.isAccountAvailable ? "icloud.fill" : "icloud.slash.fill")
                    .foregroundColor(cloudKitManager.isAccountAvailable ? .blue : .gray)
                    .font(.caption)
            }
        }
    }
}

#Preview("已认证状态") {
    ContentView()
        .environment(\.managedObjectContext, CoreDataManager.shared.viewContext)
        .environmentObject({
            let authService = AuthenticationService.shared
            authService.isAuthenticated = true
            return authService
        }())
        .environmentObject(CoreDataManager.shared)
        .environmentObject(CloudKitManager.shared)
}

#Preview("未认证状态") {
    ContentView()
        .environment(\.managedObjectContext, CoreDataManager.shared.viewContext)
        .environmentObject({
            let authService = AuthenticationService.shared
            authService.isAuthenticated = false
            return authService
        }())
        .environmentObject(CoreDataManager.shared)
        .environmentObject(CloudKitManager.shared)
}
