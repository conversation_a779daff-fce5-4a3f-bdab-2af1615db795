//
//  GedaApp.swift
//  Geda
//
//  Created by 杨瑞光 on 2025/7/10.
//

import SwiftUI
import UserNotifications
import _Concurrency
import CoreData

@main
struct GedaApp: App {
    // 性能监控管理器
    private let performanceManager = PerformanceManager.shared
    
    // Core Data管理器
    @StateObject private var coreDataManager = CoreDataManager.shared
    
    // 通知管理器
    private let notificationManager = NotificationManager.shared
    
    // 认证服务
    @StateObject private var authService = AuthenticationService.shared
    
    // CloudKit管理器
    @StateObject private var cloudKitManager = CloudKitManager.shared
    
    init() {
        // 记录应用启动开始时间
        performanceManager.recordAppLaunchStart()
        
        // 启动时优化
        optimizeAppStartup()
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, coreDataManager.viewContext)
                .environmentObject(coreDataManager)
                .environmentObject(notificationManager)
                .environmentObject(authService)
                .environmentObject(cloudKitManager)
                .onAppear {
                    setupApplication()
                    // 记录应用启动完成
                    performanceManager.recordAppLaunchComplete()
                }
                .onReceive(authService.$isAuthenticated) { isAuthenticated in
                    handleAuthenticationChange(isAuthenticated)
                }
        }
    }
    
    // MARK: - 私有方法
    
    /// 应用启动优化
    private func optimizeAppStartup() {
        // 设置性能监控
        performanceManager.recordCustomMetric(name: "startup_optimization", value: 1.0)
    }
    
    /// 应用初始化设置
    private func setupApplication() {
        // 检查认证状态
        authService.checkAuthenticationState()
        
        // 异步初始化非关键组件，避免阻塞主线程
        _Concurrency.Task {
            await setupNotifications()
            await preloadCriticalData()
        }
    }
    
    /// 处理认证状态变化
    private func handleAuthenticationChange(_ isAuthenticated: Bool) {
        if isAuthenticated {
            // 用户已登录，启用CloudKit同步
            coreDataManager.enableCloudKitSync()
            cloudKitManager.startSync()
            
            // 创建或获取用户数据
            createOrFetchUser()
        } else {
            // 用户未登录，禁用CloudKit同步
            coreDataManager.disableCloudKitSync()
            cloudKitManager.stopSync()
        }
    }
    
    /// 创建或获取用户数据
    private func createOrFetchUser() {
        guard let currentAppleUser = authService.currentUser else { return }
        
        let context = coreDataManager.viewContext
        let request: NSFetchRequest<User> = User.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", currentAppleUser.appleUserID)
        
        do {
            let existingUsers = try context.fetch(request)
            
            if existingUsers.isEmpty {
                // 创建新用户
                let newUser = User(context: context)
                newUser.id = currentAppleUser.appleUserID
                newUser.name = currentAppleUser.displayName
                newUser.createdAt = Date()
                newUser.updatedAt = Date()
                newUser.checkInDays = 0
                
                try context.save()
                print("已创建新用户: \(newUser.name ?? "")")
            } else {
                print("用户已存在: \(existingUsers.first?.name ?? "")")
            }
        } catch {
            print("用户数据处理失败: \(error.localizedDescription)")
        }
    }
    
    /// 设置通知系统
    private func setupNotifications() async {
        // 设置通知代理
        UNUserNotificationCenter.current().delegate = notificationManager
        
        // 请求通知权限
        _ = await notificationManager.requestPermission()
        
        // 清除应用图标角标
        notificationManager.clearBadge()
        
        performanceManager.recordCustomMetric(name: "notifications_setup", value: 1.0)
    }
    
    /// 预加载关键数据
    private func preloadCriticalData() async {
        performanceManager.startDataLoad(for: "critical_data_preload")
        
        // 预加载用户偏好设置
        let userDefaults = UserDefaults.standard
        _ = userDefaults.bool(forKey: "first_launch")
        
        // 预热Core Data栈
        _ = coreDataManager.viewContext
        
        performanceManager.endDataLoad(for: "critical_data_preload")
    }
}
