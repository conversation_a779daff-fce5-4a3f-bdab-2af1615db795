//
//  ScannerViewModel.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/14.
//

import SwiftUI
import AVFoundation
import Combine

/// 扫描视图模型
/// 管理二维码扫描、相机访问和扫描结果处理
class ScannerViewModel: NSObject, ObservableObject {
    @Published var scannedCode: String = ""
    @Published var isCameraAuthorized = false
    @Published var isScanning = false
    @Published var errorMessage: String?
    
    var captureSession: AVCaptureSession?
    private var captureDevice: AVCaptureDevice?
    private var videoOutput: AVCaptureVideoDataOutput?
    private var metadataOutput: AVCaptureMetadataOutput?
    
    private var cancellables = Set<AnyCancellable>()
    
    override init() {
        super.init()
        checkCameraPermission()
    }
    
    // MARK: - Public Methods
    
    /// 开始扫描
    func startScanning() {
        guard isCameraAuthorized else {
            requestCameraPermission()
            return
        }
        
        setupCaptureSession()
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            self?.captureSession?.startRunning()
            DispatchQueue.main.async {
                self?.isScanning = true
            }
        }
    }
    
    /// 停止扫描
    func stopScanning() {
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            self?.captureSession?.stopRunning()
            DispatchQueue.main.async {
                self?.isScanning = false
            }
        }
    }
    
    /// 恢复扫描
    func resumeScanning() {
        scannedCode = ""
        if !isScanning {
            startScanning()
        }
    }
    
    /// 切换闪光灯
    func toggleTorch() {
        guard let device = captureDevice, device.hasTorch else { return }
        
        do {
            try device.lockForConfiguration()
            device.torchMode = device.torchMode == .on ? .off : .on
            device.unlockForConfiguration()
        } catch {
            errorMessage = "无法控制闪光灯: \\(error.localizedDescription)"
        }
    }
    
    // MARK: - Private Methods
    
    private func checkCameraPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            isCameraAuthorized = true
        case .notDetermined:
            requestCameraPermission()
        case .denied, .restricted:
            isCameraAuthorized = false
            errorMessage = "相机权限被拒绝，请在设置中允许相机访问"
        @unknown default:
            isCameraAuthorized = false
        }
    }
    
    private func requestCameraPermission() {
        AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
            DispatchQueue.main.async {
                self?.isCameraAuthorized = granted
                if granted {
                    self?.setupCaptureSession()
                } else {
                    self?.errorMessage = "需要相机权限才能扫描二维码"
                }
            }
        }
    }
    
    private func setupCaptureSession() {
        guard isCameraAuthorized else { return }
        
        // 创建捕获会话
        let captureSession = AVCaptureSession()
        
        // 设置相机设备
        guard let captureDevice = AVCaptureDevice.default(for: .video) else {
            errorMessage = "无法访问相机设备"
            return
        }
        
        self.captureDevice = captureDevice
        
        do {
            // 创建输入源
            let deviceInput = try AVCaptureDeviceInput(device: captureDevice)
            
            if captureSession.canAddInput(deviceInput) {
                captureSession.addInput(deviceInput)
            }
            
            // 设置元数据输出
            let metadataOutput = AVCaptureMetadataOutput()
            
            if captureSession.canAddOutput(metadataOutput) {
                captureSession.addOutput(metadataOutput)
                
                // 设置代理和队列
                metadataOutput.setMetadataObjectsDelegate(self, queue: DispatchQueue.main)
                
                // 设置支持的条码类型
                metadataOutput.metadataObjectTypes = [
                    .qr,
                    .ean8,
                    .ean13,
                    .pdf417,
                    .code128,
                    .code39,
                    .code93,
                    .upce,
                    .aztec,
                    .dataMatrix
                ]
            }
            
            self.metadataOutput = metadataOutput
            self.captureSession = captureSession
            
        } catch {
            errorMessage = "设置相机失败: \\(error.localizedDescription)"
        }
    }
    
    private func processScannedCode(_ code: String) {
        // 防止重复扫描
        guard scannedCode.isEmpty else { return }
        
        scannedCode = code
        stopScanning()
        
        // 可以在这里添加震动反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // 可以在这里添加声音反馈
        AudioServicesPlaySystemSound(SystemSoundID(kSystemSoundID_Vibrate))
    }
}

// MARK: - AVCaptureMetadataOutputObjectsDelegate

extension ScannerViewModel: AVCaptureMetadataOutputObjectsDelegate {
    func metadataOutput(
        _ output: AVCaptureMetadataOutput,
        didOutput metadataObjects: [AVMetadataObject],
        from connection: AVCaptureConnection
    ) {
        // 查找第一个有效的条码
        if let metadataObject = metadataObjects.first as? AVMetadataMachineReadableCodeObject,
           let stringValue = metadataObject.stringValue {
            processScannedCode(stringValue)
        }
    }
}

// MARK: - 扫描结果处理

extension ScannerViewModel {
    /// 验证二维码格式
    func validateQRCode(_ code: String) -> QRCodeType {
        // URL格式
        if code.hasPrefix("http://") || code.hasPrefix("https://") {
            return .url
        }
        
        // WiFi格式
        if code.hasPrefix("WIFI:") {
            return .wifi
        }
        
        // 联系人格式
        if code.hasPrefix("BEGIN:VCARD") {
            return .contact
        }
        
        // 邮件格式
        if code.hasPrefix("mailto:") {
            return .email
        }
        
        // 电话格式
        if code.hasPrefix("tel:") {
            return .phone
        }
        
        // GPS格式
        if code.hasPrefix("geo:") {
            return .location
        }
        
        // 普通文本
        return .text
    }
    
    /// 处理不同类型的二维码
    func handleScannedCode(_ code: String) {
        let qrType = validateQRCode(code)
        
        switch qrType {
        case .url:
            openURL(code)
        case .wifi:
            connectToWiFi(code)
        case .contact:
            addContact(code)
        case .email:
            composeEmail(code)
        case .phone:
            makePhoneCall(code)
        case .location:
            showLocation(code)
        case .text:
            copyToClipboard(code)
        }
    }
    
    private func openURL(_ urlString: String) {
        guard let url = URL(string: urlString) else { return }
        UIApplication.shared.open(url)
    }
    
    private func connectToWiFi(_ wifiString: String) {
        // WiFi连接逻辑 - iOS限制较多，主要是提示用户手动连接
        // WIFI:T:WPA;S:NetworkName;P:Password;;
        print("WiFi连接: \\(wifiString)")
    }
    
    private func addContact(_ vcardString: String) {
        // 添加联系人逻辑
        print("添加联系人: \\(vcardString)")
    }
    
    private func composeEmail(_ emailString: String) {
        guard let url = URL(string: emailString) else { return }
        UIApplication.shared.open(url)
    }
    
    private func makePhoneCall(_ phoneString: String) {
        guard let url = URL(string: phoneString) else { return }
        UIApplication.shared.open(url)
    }
    
    private func showLocation(_ locationString: String) {
        // geo:37.786971,-122.399677
        guard let url = URL(string: "maps://?q=\\(locationString)") else { return }
        UIApplication.shared.open(url)
    }
    
    private func copyToClipboard(_ text: String) {
        UIPasteboard.general.string = text
    }
}

// MARK: - 数据模型

/// 二维码类型枚举
enum QRCodeType: Codable {
    case url
    case wifi
    case contact
    case email
    case phone
    case location
    case text
    
    var displayName: String {
        switch self {
        case .url: return "网址"
        case .wifi: return "WiFi"
        case .contact: return "联系人"
        case .email: return "邮件"
        case .phone: return "电话"
        case .location: return "位置"
        case .text: return "文本"
        }
    }
}

/// 扫描历史记录
struct ScanHistory: Identifiable, Codable {
    let id = UUID()
    let content: String
    let type: QRCodeType
    let timestamp: Date
    
    init(content: String, type: QRCodeType) {
        self.content = content
        self.type = type
        self.timestamp = Date()
    }
}