//
//  ProfileViewModel.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/14.
//

import SwiftUI
import Foundation
import Combine

/// 个人资料视图模型
/// 管理用户个人资料数据和相关操作
class ProfileViewModel: ObservableObject {
    @Published var userProfile: UserProfile = UserProfile.defaultProfile
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupObservers()
    }
    
    // MARK: - Public Methods
    
    /// 加载用户个人资料
    func loadUserProfile() {
        isLoading = true
        errorMessage = nil
        
        // 模拟网络请求延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            guard let self = self else { return }
            
            // 在实际应用中，这里应该从Repository或API获取数据
            self.userProfile = UserProfile(
                userID: "PLAN78935",
                username: "孙博为",
                email: "<EMAIL>",
                avatarURL: "https://images.unsplash.com/photo-1527980965255-d3b416303d12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=880&q=80",
                streakDays: 128,
                totalStudyHours: 256,
                completedTasks: 1420,
                totalPlans: 28,
                joinDate: Calendar.current.date(byAdding: .day, value: -128, to: Date()) ?? Date(),
                isStudyModeEnabled: false,
                notificationSettings: NotificationSettings(),
                preferences: UserPreferences()
            )
            
            self.isLoading = false
        }
    }
    
    /// 更新用户头像
    func updateAvatar(imageURL: String) {
        userProfile.avatarURL = imageURL
        saveUserProfile()
    }
    
    /// 更新用户名
    func updateUsername(_ newUsername: String) {
        userProfile.username = newUsername
        saveUserProfile()
    }
    
    /// 更新邮箱
    func updateEmail(_ newEmail: String) {
        userProfile.email = newEmail
        saveUserProfile()
    }
    
    /// 切换学霸模式
    func toggleStudyMode() {
        userProfile.isStudyModeEnabled.toggle()
        saveUserProfile()
    }
    
    /// 退出登录
    func logout() {
        // 在实际应用中，这里应该清除用户数据和令牌
        // 重置到登录页面等
        userProfile = UserProfile.defaultProfile
        print("用户已退出登录")
    }
    
    /// 获取用户统计信息
    func getUserStatistics() -> UserStatistics {
        return UserStatistics(
            totalStudyHours: userProfile.totalStudyHours,
            completedTasks: userProfile.completedTasks,
            streakDays: userProfile.streakDays,
            totalPlans: userProfile.totalPlans,
            averageDailyStudyTime: calculateAverageDailyStudyTime(),
            mostProductiveDay: getMostProductiveDay()
        )
    }
    
    // MARK: - Private Methods
    
    private func setupObservers() {
        // 可以在这里设置用户数据变化的监听
        // 例如监听Core Data的变化通知
    }
    
    private func saveUserProfile() {
        // 在实际应用中，这里应该调用Repository保存用户数据
        print("保存用户资料: \(userProfile.username)")
    }
    
    private func calculateAverageDailyStudyTime() -> Double {
        guard userProfile.streakDays > 0 else { return 0.0 }
        return Double(userProfile.totalStudyHours) / Double(userProfile.streakDays)
    }
    
    private func getMostProductiveDay() -> String {
        // 模拟数据 - 在实际应用中应该从数据库计算
        let weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        return weekdays.randomElement() ?? "周一"
    }
}

// MARK: - 数据模型

/// 用户个人资料模型
struct UserProfile {
    var userID: String
    var username: String
    var email: String
    var avatarURL: String
    var streakDays: Int
    var totalStudyHours: Int
    var completedTasks: Int
    var totalPlans: Int
    var joinDate: Date
    var isStudyModeEnabled: Bool
    var notificationSettings: NotificationSettings
    var preferences: UserPreferences
    
    static let defaultProfile = UserProfile(
        userID: "GUEST0001",
        username: "游客用户",
        email: "<EMAIL>",
        avatarURL: "",
        streakDays: 0,
        totalStudyHours: 0,
        completedTasks: 0,
        totalPlans: 0,
        joinDate: Date(),
        isStudyModeEnabled: false,
        notificationSettings: NotificationSettings(),
        preferences: UserPreferences()
    )
}

/// 通知设置模型
struct NotificationSettings {
    var isTaskReminderEnabled: Bool = true
    var isPomodoroNotificationEnabled: Bool = true
    var isReviewReminderEnabled: Bool = true
    var quietHoursStart: Date = Calendar.current.date(bySettingHour: 22, minute: 0, second: 0, of: Date()) ?? Date()
    var quietHoursEnd: Date = Calendar.current.date(bySettingHour: 8, minute: 0, second: 0, of: Date()) ?? Date()
}

/// 用户偏好设置模型
struct UserPreferences {
    var preferredTheme: AppTheme = .system
    var defaultPomodoroLength: Int = 25 // 分钟
    var defaultShortBreak: Int = 5 // 分钟
    var defaultLongBreak: Int = 15 // 分钟
    var autoStartBreaks: Bool = false
    var autoStartPomodoros: Bool = false
    var soundEnabled: Bool = true
    var vibrationEnabled: Bool = true
}

/// 应用主题枚举
enum AppTheme: String, CaseIterable {
    case light = "light"
    case dark = "dark"
    case system = "system"
    
    var displayName: String {
        switch self {
        case .light: return "浅色"
        case .dark: return "深色"
        case .system: return "跟随系统"
        }
    }
}

/// 用户统计信息模型
struct UserStatistics {
    let totalStudyHours: Int
    let completedTasks: Int
    let streakDays: Int
    let totalPlans: Int
    let averageDailyStudyTime: Double
    let mostProductiveDay: String
}