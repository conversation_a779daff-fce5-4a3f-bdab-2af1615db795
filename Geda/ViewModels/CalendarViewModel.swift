//
//  CalendarViewModel.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/14.
//

import SwiftUI
import Foundation
import Combine

/// 日历视图模型
/// 管理日历页面的数据和业务逻辑
class CalendarViewModel: ObservableObject {
    @Published var tasksForSelectedDate: [CalendarTask] = []
    @Published var remindersForSelectedDate: [CalendarReminder] = []
    @Published var isLoading = false
    
    private var cancellables = Set<AnyCancellable>()
    private let calendar = Calendar.current
    
    // 示例数据 - 在实际应用中这些数据应该来自Repository
    private let sampleTasks: [CalendarTask] = [
        CalendarTask(
            id: UUID(),
            title: "学习考研英语",
            timeRange: "09:00-11:00",
            date: Date(),
            isCompleted: false
        ),
        CalendarTask(
            id: UUID(),
            title: "复习长难句结构分析",
            timeRange: "14:00-15:00",
            date: Date(),
            isCompleted: true
        ),
        CalendarTask(
            id: UUID(),
            title: "背诵新概念英语第四册",
            timeRange: "15:30-16:30",
            date: Date(),
            isCompleted: false
        )
    ]
    
    private let sampleReminders: [CalendarReminder] = [
        CalendarReminder(
            id: UUID(),
            title: "CPA经济法报名",
            time: "20:00",
            date: Date(),
            isCompleted: false
        )
    ]
    
    init() {
        setupObservers()
    }
    
    // MARK: - Public Methods
    
    /// 加载指定日期的任务和提醒
    func loadTasks(for date: Date) {
        isLoading = true
        
        // 模拟网络请求延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            guard let self = self else { return }
            
            // 筛选当日的任务和提醒
            self.tasksForSelectedDate = self.sampleTasks.filter { task in
                self.calendar.isDate(task.date, inSameDayAs: date)
            }
            
            self.remindersForSelectedDate = self.sampleReminders.filter { reminder in
                self.calendar.isDate(reminder.date, inSameDayAs: date)
            }
            
            self.isLoading = false
        }
    }
    
    /// 检查指定日期是否有事件（任务或提醒）
    func hasEvents(for date: Date) -> Bool {
        let hasTasks = sampleTasks.contains { task in
            calendar.isDate(task.date, inSameDayAs: date)
        }
        
        let hasReminders = sampleReminders.contains { reminder in
            calendar.isDate(reminder.date, inSameDayAs: date)
        }
        
        return hasTasks || hasReminders
    }
    
    /// 切换任务完成状态
    func toggleTaskCompletion(_ task: CalendarTask) {
        // 在实际应用中，这里应该调用Repository更新数据
        if let index = tasksForSelectedDate.firstIndex(where: { $0.id == task.id }) {
            tasksForSelectedDate[index].isCompleted.toggle()
        }
    }
    
    /// 切换提醒完成状态
    func toggleReminderCompletion(_ reminder: CalendarReminder) {
        // 在实际应用中，这里应该调用Repository更新数据
        if let index = remindersForSelectedDate.firstIndex(where: { $0.id == reminder.id }) {
            remindersForSelectedDate[index].isCompleted.toggle()
        }
    }
    
    /// 添加新任务
    func addTask(_ task: CalendarTask) {
        // 在实际应用中，这里应该调用Repository保存数据
        tasksForSelectedDate.append(task)
    }
    
    /// 添加新提醒
    func addReminder(_ reminder: CalendarReminder) {
        // 在实际应用中，这里应该调用Repository保存数据
        remindersForSelectedDate.append(reminder)
    }
    
    /// 删除任务
    func deleteTask(_ task: CalendarTask) {
        // 在实际应用中，这里应该调用Repository删除数据
        tasksForSelectedDate.removeAll { $0.id == task.id }
    }
    
    /// 删除提醒
    func deleteReminder(_ reminder: CalendarReminder) {
        // 在实际应用中，这里应该调用Repository删除数据
        remindersForSelectedDate.removeAll { $0.id == reminder.id }
    }
    
    // MARK: - Private Methods
    
    private func setupObservers() {
        // 可以在这里设置数据变化的监听
        // 例如监听Core Data的变化通知
    }
}

// MARK: - 数据模型

/// 日历任务模型
struct CalendarTask: Identifiable, Equatable {
    let id: UUID
    var title: String
    var timeRange: String
    var date: Date
    var isCompleted: Bool
    
    init(id: UUID = UUID(), title: String, timeRange: String, date: Date, isCompleted: Bool = false) {
        self.id = id
        self.title = title
        self.timeRange = timeRange
        self.date = date
        self.isCompleted = isCompleted
    }
}

/// 日历提醒模型
struct CalendarReminder: Identifiable, Equatable {
    let id: UUID
    var title: String
    var time: String
    var date: Date
    var isCompleted: Bool
    
    init(id: UUID = UUID(), title: String, time: String, date: Date, isCompleted: Bool = false) {
        self.id = id
        self.title = title
        self.time = time
        self.date = date
        self.isCompleted = isCompleted
    }
}