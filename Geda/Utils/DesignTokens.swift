//
//  DesignTokens.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI

/// 设计令牌 - 统一管理设计系统的所有设计决策
/// 基于HTML原型的CSS变量系统
struct DesignTokens {
    
    // MARK: - 间距系统
    struct Spacing {
        static let xxs: CGFloat = 2     // 超小间距
        static let xsmall: CGFloat = 2   // 超小间距
        static let small: CGFloat = 6    // 小间距
        static let xs: CGFloat = 4      // 0.25rem
        static let sm: CGFloat = 8      // 0.5rem
        static let md: CGFloat = 12     // 0.75rem
        static let medium: CGFloat = 12  // 别名
        static let lg: CGFloat = 16     // 1rem
        static let xl: CGFloat = 20     // 1.25rem
        static let xxl: CGFloat = 24    // 1.5rem
        static let xxxl: CGFloat = 32   // 2rem
        static let huge: CGFloat = 48   // 3rem
        
        // 语义化间距
        static let cardPadding: CGFloat = lg
        static let sectionSpacing: CGFloat = xxl
        static let elementSpacing: CGFloat = md
        static let buttonPadding: CGFloat = lg
    }
    
    // MARK: - 圆角系统
    struct CornerRadius {
        static let xs: CGFloat = 4      // 小圆角
        static let small: CGFloat = 6   // 小圆角别名
        static let sm: CGFloat = 8      // 标准圆角
        static let md: CGFloat = 12     // 中等圆角
        static let lg: CGFloat = 16     // 大圆角
        static let xl: CGFloat = 20     // 卡片圆角 (对应CSS 1.25rem)
        static let xxl: CGFloat = 24    // 超大圆角
        static let full: CGFloat = 999  // 完全圆角
        
        // 语义化圆角
        static let card: CGFloat = xl           // 对应custom-card
        static let button: CGFloat = md         // 按钮圆角
        static let input: CGFloat = sm          // 输入框圆角
        static let badge: CGFloat = full        // 徽章圆角
    }
    
    // MARK: - 阴影系统
    struct Shadow {
        // 对应CSS --card-shadow: 0 10px 35px -5px rgba(180, 190, 220, 0.2)
        static let card = (
            color: Color.gedaCardShadow,
            radius: CGFloat(10),
            x: CGFloat(0),
            y: CGFloat(5)
        )
        
        static let small = (
            color: Color.black.opacity(0.1),
            radius: CGFloat(2),
            x: CGFloat(0),
            y: CGFloat(1)
        )
        
        static let medium = (
            color: Color.black.opacity(0.15),
            radius: CGFloat(5),
            x: CGFloat(0),
            y: CGFloat(2)
        )
        
        static let large = (
            color: Color.black.opacity(0.2),
            radius: CGFloat(15),
            x: CGFloat(0),
            y: CGFloat(8)
        )
        
        static let button = (
            color: Color.gedaAccentStart.opacity(0.3),
            radius: CGFloat(8),
            x: CGFloat(0),
            y: CGFloat(4)
        )
    }
    
    // MARK: - 动画系统
    struct Animation {
        // 持续时间
        static let fast: Double = 0.15
        static let normal: Double = 0.3
        static let slow: Double = 0.5
        static let verySlow: Double = 0.8
        
        // 缓动函数
        static let easeInOut = SwiftUI.Animation.easeInOut(duration: normal)
        static let easeOut = SwiftUI.Animation.easeOut(duration: normal)
        static let spring = SwiftUI.Animation.spring(response: 0.4, dampingFraction: 0.8)
        static let bouncy = SwiftUI.Animation.spring(response: 0.3, dampingFraction: 0.6)
        
        // 对应CSS transition: all 0.3s ease
        static let defaultTransition = easeInOut
        
        // 对应CSS transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1)
        static let taskCardTransition = SwiftUI.Animation.timingCurve(0.4, 0, 0.2, 1, duration: 0.4)
    }
    
    // MARK: - 尺寸系统
    struct Size {
        // 图标尺寸
        static let iconXS: CGFloat = 12
        static let iconSM: CGFloat = 16
        static let iconMD: CGFloat = 20
        static let iconLG: CGFloat = 24
        static let iconXL: CGFloat = 32
        
        // 按钮尺寸
        static let buttonHeight: CGFloat = 44
        static let buttonHeightSmall: CGFloat = 36
        static let buttonHeightLarge: CGFloat = 52
        
        // 输入框尺寸
        static let inputHeight: CGFloat = 44
        static let inputHeightSmall: CGFloat = 36
        
        // 卡片尺寸
        static let cardMinHeight: CGFloat = 80
        static let taskCardHeight: CGFloat = 128  // 对应CSS height: 128px
        
        // 头像尺寸
        static let avatarSmall: CGFloat = 24
        static let avatarMedium: CGFloat = 32
        static let avatarLarge: CGFloat = 48
        static let avatarXLarge: CGFloat = 64
    }
    
    // MARK: - 透明度系统
    struct Opacity {
        static let disabled: Double = 0.4
        static let secondary: Double = 0.6
        static let overlay: Double = 0.8
        static let modal: Double = 0.5      // 对应CSS rgba(30, 41, 59, 0.5)
        
        // 任务卡片透明度 (对应CSS)
        static let taskCard2: Double = 0.8  // task-card-2
        static let taskCard3: Double = 0.6  // task-card-3
    }
    
    // MARK: - Z-Index系统
    struct ZIndex {
        static let base: Double = 1
        static let dropdown: Double = 10
        static let modal: Double = 50
        static let overlay: Double = 100
        static let tooltip: Double = 200
        
        // 任务卡片层级 (对应CSS)
        static let taskCard1: Double = 30
        static let taskCard2: Double = 20
        static let taskCard3: Double = 10
    }
    
    // MARK: - 断点系统
    struct Breakpoint {
        static let mobile: CGFloat = 420     // 对应CSS max-width: 420px
        static let tablet: CGFloat = 768
        static let desktop: CGFloat = 1024
    }
    
    // MARK: - 应用尺寸 (对应CSS .app-shell)
    struct AppSize {
        static let maxWidth: CGFloat = 420   // max-width: 420px
        static let maxHeight: CGFloat = 880  // max-height: 880px
        static let shellShadow = (
            color: Color.black.opacity(0.15),
            radius: CGFloat(25),
            x: CGFloat(0),
            y: CGFloat(20)
        )
    }
}

// MARK: - 设计令牌应用扩展
extension View {
    // 应用卡片阴影
    func gedaCardShadow() -> some View {
        self.shadow(
            color: DesignTokens.Shadow.card.color,
            radius: DesignTokens.Shadow.card.radius,
            x: DesignTokens.Shadow.card.x,
            y: DesignTokens.Shadow.card.y
        )
    }
    
    // 应用按钮阴影
    func gedaButtonShadow() -> some View {
        self.shadow(
            color: DesignTokens.Shadow.button.color,
            radius: DesignTokens.Shadow.button.radius,
            x: DesignTokens.Shadow.button.x,
            y: DesignTokens.Shadow.button.y
        )
    }
    
    // 应用标准动画
    func gedaDefaultAnimation() -> some View {
        self.animation(DesignTokens.Animation.defaultTransition, value: UUID())
    }
    
    // 应用任务卡片动画
    func gedaTaskCardAnimation() -> some View {
        self.animation(DesignTokens.Animation.taskCardTransition, value: UUID())
    }
}
