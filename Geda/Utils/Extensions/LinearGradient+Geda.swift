//
//  LinearGradient+Geda.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI

extension LinearGradient {
    // MARK: - 主要渐变

    // 对应CSS的gradient-button类
    // background-image: linear-gradient(to right, var(--accent-start), var(--accent-end))
    static let gedaGradient = LinearGradient(
        colors: [.gedaAccentStart, .gedaAccentEnd],
        startPoint: .leading,
        endPoint: .trailing
    )

    // MARK: - 功能性渐变

    // 学习总览卡片渐变 (紫色到蓝色)
    static let reviewCardGradient = LinearGradient(
        colors: [Color(hex: "#8b5cf6"), Color(hex: "#3b82f6")],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    // 成就徽章渐变
    static let achievementGradient = LinearGradient(
        colors: [Color(hex: "#10b981"), Color(hex: "#3b82f6")],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    // 优先级渐变
    static let priorityHighGradient = LinearGradient(
        colors: [.gedaPriorityHigh, .gedaPriorityHigh.opacity(0.8)],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    static let priorityMediumGradient = LinearGradient(
        colors: [.gedaPriorityMedium, .gedaPriorityMedium.opacity(0.8)],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    static let priorityLowGradient = LinearGradient(
        colors: [.gedaPriorityLow, .gedaPriorityLow.opacity(0.8)],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    // MARK: - 背景渐变

    // 页面背景渐变
    static let pageBackgroundGradient = LinearGradient(
        colors: [.gedaBgShell, .gedaBgPage],
        startPoint: .top,
        endPoint: .bottom
    )

    // 卡片背景渐变
    static let cardBackgroundGradient = LinearGradient(
        colors: [.white, .gedaGray50],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    // MARK: - 状态渐变

    // 成功状态渐变
    static let successGradient = LinearGradient(
        colors: [.gedaSuccess, Color(hex: "#059669")],
        startPoint: .leading,
        endPoint: .trailing
    )

    // 警告状态渐变
    static let warningGradient = LinearGradient(
        colors: [.gedaWarning, Color(hex: "#d97706")],
        startPoint: .leading,
        endPoint: .trailing
    )

    // 错误状态渐变
    static let errorGradient = LinearGradient(
        colors: [.gedaError, Color(hex: "#dc2626")],
        startPoint: .leading,
        endPoint: .trailing
    )

    // MARK: - 特殊效果渐变

    // 玻璃态效果渐变
    static let glassGradient = LinearGradient(
        colors: [
            .white.opacity(0.25),
            .white.opacity(0.1)
        ],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    // 阴影渐变
    static let shadowGradient = LinearGradient(
        colors: [
            .black.opacity(0.1),
            .clear
        ],
        startPoint: .top,
        endPoint: .bottom
    )

    // MARK: - 工具方法

    // 根据优先级获取对应的渐变
    static func priorityGradient(for priority: String) -> LinearGradient {
        switch priority.lowercased() {
        case "high":
            return .priorityHighGradient
        case "medium":
            return .priorityMediumGradient
        case "low":
            return .priorityLowGradient
        default:
            return .priorityMediumGradient
        }
    }
}
