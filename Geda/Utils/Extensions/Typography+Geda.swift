//
//  Typography+Geda.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI

// MARK: - 字体扩展
extension Font {
    // MARK: - 基础字体系统 (基于CSS: Noto Sans SC + Inter)
    
    // 标题字体
    static let gedaTitle1 = Font.system(size: 28, weight: .bold, design: .default)
    static let gedaTitle2 = Font.system(size: 24, weight: .bold, design: .default)
    static let gedaTitle3 = Font.system(size: 20, weight: .semibold, design: .default)
    static let gedaTitle4 = Font.system(size: 18, weight: .semibold, design: .default)
    static let gedaHeadline = Font.system(size: 18, weight: .semibold, design: .default)
    
    // 正文字体
    static let gedaBody = Font.system(size: 16, weight: .regular, design: .default)
    static let gedaBodyMedium = Font.system(size: 16, weight: .medium, design: .default)
    static let gedaBodySemibold = Font.system(size: 16, weight: .semibold, design: .default)
    static let gedaSubheadline = Font.system(size: 15, weight: .medium, design: .default)
    
    // 小字体
    static let gedaCaption = Font.system(size: 14, weight: .regular, design: .default)
    static let gedaCaptionMedium = Font.system(size: 14, weight: .medium, design: .default)
    static let gedaCaptionSemibold = Font.system(size: 14, weight: .semibold, design: .default)
    static let gedaCaptionSmall = Font.system(size: 12, weight: .regular, design: .default)
    
    // 极小字体
    static let gedaFootnote = Font.system(size: 12, weight: .regular, design: .default)
    static let gedaFootnoteMedium = Font.system(size: 12, weight: .medium, design: .default)
    
    // 按钮字体
    static let gedaButton = Font.system(size: 16, weight: .semibold, design: .default)
    static let gedaButtonSmall = Font.system(size: 14, weight: .semibold, design: .default)
    
    // 数字字体 (等宽)
    static let gedaNumber = Font.system(size: 16, weight: .medium, design: .monospaced)
    static let gedaNumberLarge = Font.system(size: 24, weight: .bold, design: .monospaced)
    
    // 标签字体
    static let gedaLabel = Font.system(size: 12, weight: .medium, design: .default)
    static let gedaLabelSmall = Font.system(size: 10, weight: .medium, design: .default)
}

// MARK: - 文本样式扩展
extension Text {
    // MARK: - 标题样式
    
    func gedaTitle1Style() -> some View {
        self
            .font(.gedaTitle1)
            .foregroundColor(.gedaTextPrimary)
    }
    
    func gedaTitle2Style() -> some View {
        self
            .font(.gedaTitle2)
            .foregroundColor(.gedaTextPrimary)
    }
    
    func gedaTitle3Style() -> some View {
        self
            .font(.gedaTitle3)
            .foregroundColor(.gedaTextPrimary)
    }
    
    func gedaTitle4Style() -> some View {
        self
            .font(.gedaTitle4)
            .foregroundColor(.gedaTextPrimary)
    }
    
    // MARK: - 正文样式
    
    func gedaBodyStyle() -> some View {
        self
            .font(.gedaBody)
            .foregroundColor(.gedaTextPrimary)
    }
    
    func gedaBodySecondaryStyle() -> some View {
        self
            .font(.gedaBody)
            .foregroundColor(.gedaTextSecondary)
    }
    
    func gedaBodyMediumStyle() -> some View {
        self
            .font(.gedaBodyMedium)
            .foregroundColor(.gedaTextPrimary)
    }
    
    // MARK: - 小字体样式
    
    func gedaCaptionStyle() -> some View {
        self
            .font(.gedaCaption)
            .foregroundColor(.gedaTextSecondary)
    }
    
    func gedaCaptionPrimaryStyle() -> some View {
        self
            .font(.gedaCaption)
            .foregroundColor(.gedaTextPrimary)
    }
    
    func gedaFootnoteStyle() -> some View {
        self
            .font(.gedaFootnote)
            .foregroundColor(.gedaTextSecondary)
    }
    
    // MARK: - 按钮样式
    
    func gedaButtonStyle() -> some View {
        self
            .font(.gedaButton)
            .foregroundColor(.white)
    }
    
    func gedaButtonSecondaryStyle() -> some View {
        self
            .font(.gedaButton)
            .foregroundColor(.gedaAccentStart)
    }
    
    // MARK: - 特殊样式
    
    func gedaNumberStyle() -> some View {
        self
            .font(.gedaNumber)
            .foregroundColor(.gedaTextPrimary)
    }
    
    func gedaLabelStyle() -> some View {
        self
            .font(.gedaLabel)
            .foregroundColor(.gedaTextSecondary)
            .textCase(.uppercase)
    }
    
    // 渐变文本样式 (对应CSS的gradient-text类)
    func gedaGradientTextStyle() -> some View {
        self
            .font(.gedaTitle3)
            .foregroundStyle(LinearGradient.gedaGradient)
    }
    
    // 优先级文本样式
    func gedaPriorityTextStyle(priority: String) -> some View {
        self
            .font(.gedaCaptionMedium)
            .foregroundColor(Color.priorityColor(for: priority))
    }
    
    // 删除线样式 (对应CSS的text-strikethrough类)
    func gedaStrikethroughStyle() -> some View {
        self
            .font(.gedaBody)
            .foregroundColor(.gedaTextSecondary)
            .strikethrough(true, color: .gedaTextSecondary)
    }
}

// MARK: - 行高和间距
extension View {
    // 标准行高
    func gedaLineSpacing() -> some View {
        self.lineSpacing(4)
    }
    
    // 紧凑行高
    func gedaCompactLineSpacing() -> some View {
        self.lineSpacing(2)
    }
    
    // 宽松行高
    func gedaRelaxedLineSpacing() -> some View {
        self.lineSpacing(6)
    }
}

// MARK: - 字体权重工具
struct GedaTypography {
    // 字体大小常量
    static let titleLarge: CGFloat = 28
    static let titleMedium: CGFloat = 24
    static let titleSmall: CGFloat = 20
    static let bodyLarge: CGFloat = 18
    static let bodyMedium: CGFloat = 16
    static let bodySmall: CGFloat = 14
    static let captionLarge: CGFloat = 14
    static let captionMedium: CGFloat = 12
    static let captionSmall: CGFloat = 10
    
    // 行高常量
    static let lineHeightTight: CGFloat = 1.2
    static let lineHeightNormal: CGFloat = 1.4
    static let lineHeightRelaxed: CGFloat = 1.6
    
    // 字间距常量
    static let letterSpacingTight: CGFloat = -0.5
    static let letterSpacingNormal: CGFloat = 0
    static let letterSpacingWide: CGFloat = 0.5
}
