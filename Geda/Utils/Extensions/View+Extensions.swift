//
//  View+Extensions.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI

extension View {
    // MARK: - 卡片样式

    // 对应custom-card CSS类的卡片样式
    func gedaCardStyle() -> some View {
        self
            .background(Color.gedaBgPage)
            .cornerRadius(20) // 对应border-radius: 1.25rem
            .shadow(
                color: Color.gedaCardShadow,
                radius: 10,
                x: 0,
                y: 5
            ) // 对应--card-shadow CSS变量
    }

    // 紧凑卡片样式
    func gedaCompactCardStyle() -> some View {
        self
            .background(Color.gedaBgPage)
            .cornerRadius(12)
            .shadow(
                color: Color.gedaCardShadow,
                radius: 5,
                x: 0,
                y: 2
            )
    }

    // 玻璃态卡片样式
    func gedaGlassCardStyle() -> some View {
        self
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(LinearGradient.glassGradient)
                    .background(.ultraThinMaterial)
            )
            .cornerRadius(20)
    }

    // MARK: - 按钮样式

    // 对应gradient-button CSS类的渐变按钮样式
    func gedaGradientButton() -> some View {
        self
            .background(LinearGradient.gedaGradient)
            .foregroundColor(.white)
            .cornerRadius(12)
    }

    // 次要按钮样式
    func gedaSecondaryButton() -> some View {
        self
            .background(Color.gedaGray100)
            .foregroundColor(.gedaTextPrimary)
            .cornerRadius(12)
    }

    // 轮廓按钮样式
    func gedaOutlineButton() -> some View {
        self
            .background(Color.clear)
            .foregroundColor(.gedaAccentStart)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.gedaAccentStart, lineWidth: 1.5)
            )
    }

    // 危险按钮样式
    func gedaDangerButton() -> some View {
        self
            .background(Color.gedaError)
            .foregroundColor(.white)
            .cornerRadius(12)
    }
    
    // MARK: - 状态样式

    // 选中状态样式
    func gedaSelectedStyle() -> some View {
        self
            .background(Color.gedaSelected)
            .cornerRadius(8)
    }

    // 悬停状态样式
    func gedaHoverStyle() -> some View {
        self
            .background(Color.gedaHover)
            .cornerRadius(8)
    }

    // 禁用状态样式
    func gedaDisabledStyle() -> some View {
        self
            .foregroundColor(.gedaDisabled)
            .background(Color.gedaGray100)
            .cornerRadius(8)
    }

    // MARK: - 优先级样式

    // 优先级指示器样式
    func gedaPriorityIndicator(priority: String) -> some View {
        self
            .background(Color.priorityColor(for: priority))
            .cornerRadius(4)
    }

    // 优先级边框样式
    func gedaPriorityBorder(priority: String) -> some View {
        self
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.priorityColor(for: priority), lineWidth: 2)
            )
    }

    // MARK: - 动画效果

    // 弹跳动画
    func gedaBounceAnimation() -> some View {
        self
            .scaleEffect(1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: UUID())
    }

    // 淡入动画
    func gedaFadeInAnimation() -> some View {
        self
            .opacity(1.0)
            .animation(.easeInOut(duration: 0.3), value: UUID())
    }

    // 滑入动画
    func gedaSlideInAnimation() -> some View {
        self
            .offset(x: 0, y: 0)
            .animation(.easeOut(duration: 0.4), value: UUID())
    }
    
    // MARK: - 精确页面切换动画 (对应CSS页面过渡效果)
    
    /// 页面切换过渡动画 - 增强的NavigationLink过渡
    func gedaPageTransition() -> some View {
        self
            .transition(.asymmetric(
                insertion: .move(edge: .trailing).combined(with: .opacity),
                removal: .move(edge: .leading).combined(with: .opacity)
            ))
            .animation(.timingCurve(0.4, 0, 0.2, 1, duration: 0.35), value: UUID())
    }
    
    /// Hero动画过渡 - 用于相关视图间的无缝切换  
    func gedaHeroTransition(namespace: Namespace.ID, id: String) -> some View {
        self
            // .matchedGeometryEffect(id: id, in: namespace)  // 暂时注释以确保编译
            .animation(.spring(response: 0.5, dampingFraction: 0.8), value: UUID())
    }
    
    /// Modal出现动画 - 对应CSS modal展示效果
    func gedaModalTransition() -> some View {
        self
            .transition(.asymmetric(
                insertion: .scale(scale: 0.95).combined(with: .opacity),
                removal: .scale(scale: 1.05).combined(with: .opacity)
            ))
            .animation(.easeOut(duration: 0.3), value: UUID())
    }
    
    /// 卡片展开动画 - 对应CSS card hover/active states
    func gedaCardExpandTransition() -> some View {
        self
            .transition(.scale.combined(with: .opacity))
            .animation(.spring(response: 0.4, dampingFraction: 0.7), value: UUID())
    }
    
    /// 列表项动画 - 对应CSS list item transitions
    func gedaListItemTransition() -> some View {
        self
            .transition(.move(edge: .top).combined(with: .opacity))
            .animation(.easeInOut(duration: 0.25), value: UUID())
    }

    // MARK: - 工具修饰符

    // 条件修饰符
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }

    // 圆角指定角
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }

    // 安全区域内边距
    func gedaSafeAreaPadding() -> some View {
        self
            .padding(.horizontal, 20)
            .padding(.top, 10)
    }

    // 标准内边距
    func gedaStandardPadding() -> some View {
        self.padding(16)
    }

    // 紧凑内边距
    func gedaCompactPadding() -> some View {
        self.padding(12)
    }

    // 宽松内边距
    func gedaRelaxedPadding() -> some View {
        self.padding(24)
    }
    
    // MARK: - Modal相关
    
    // Modal展示修饰符 - 增强版本，支持精确的CSS backdrop-blur效果
    func modal<Content: View>(
        isPresented: Binding<Bool>,
        @ViewBuilder content: @escaping () -> Content
    ) -> some View {
        ZStack {
            self
            
            if isPresented.wrappedValue {
                // 精确复刻CSS backdrop-blur-sm效果 (backdrop-blur-sm = 4px blur)
                ZStack {
                    // 主要模糊层 - 对应CSS backdrop-blur-sm
                    Rectangle()
                        .fill(.regularMaterial)  // SwiftUI的.regularMaterial最接近CSS backdrop-blur-sm
                        .background(Color.black.opacity(0.4))  // 对应CSS bg-black/40
                        .ignoresSafeArea()
                    
                    // 增强模糊深度层 - 提升模糊质量
                    Rectangle()
                        .fill(.thinMaterial)  // 额外的轻度材质层
                        .background(Color.black.opacity(0.1))  // 轻微加深
                        .ignoresSafeArea()
                        .opacity(0.6)  // 减少强度以避免过度模糊
                }
                .onTapGesture {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isPresented.wrappedValue = false
                    }
                }
                .transition(.opacity)

                content()
                    .background(Color.white)
                    .cornerRadius(20)
                    .shadow(color: Color.black.opacity(0.15), radius: 25, x: 0, y: 15)  // 增强阴影效果
                    .padding(.horizontal, 24)
                    .scaleEffect(isPresented.wrappedValue ? 1 : 0.95)
                    .opacity(isPresented.wrappedValue ? 1 : 0)
                    .animation(.spring(response: 0.4, dampingFraction: 0.8), value: isPresented.wrappedValue)
            }
        }
    }
    
    // 增强型Modal - 完全复刻CSS modal效果 (backdrop-blur-md)
    func enhancedModal<Content: View>(
        isPresented: Binding<Bool>,
        @ViewBuilder content: @escaping () -> Content
    ) -> some View {
        ZStack {
            self
            
            if isPresented.wrappedValue {
                // 精确CSS backdrop-blur-md效果 (backdrop-blur-md = 12px blur)
                ZStack {
                    // 主要模糊层 - 对应CSS backdrop-blur-md
                    Rectangle()
                        .fill(.thickMaterial)  // .thickMaterial对应CSS backdrop-blur-md
                        .background(Color.black.opacity(0.3))  // 对应CSS bg-black/30
                        .ignoresSafeArea()
                    
                    // 额外的深度增强层 - 模拟更强的backdrop-filter效果
                    Rectangle()
                        .fill(.regularMaterial)
                        .background(Color.black.opacity(0.15))
                        .blur(radius: 1)  // 额外轻微模糊增强深度感
                        .ignoresSafeArea()
                        .opacity(0.8)
                    
                    // 顶层微调层 - 精细调整视觉效果
                    Rectangle()
                        .fill(Color.black.opacity(0.05))
                        .ignoresSafeArea()
                }
                .onTapGesture {
                    withAnimation(.easeOut(duration: 0.25)) {
                        isPresented.wrappedValue = false
                    }
                }
                .transition(.opacity)

                content()
                    .background(
                        RoundedRectangle(cornerRadius: 24)
                            .fill(Color.white)
                            .shadow(color: Color.black.opacity(0.12), radius: 30, x: 0, y: 20)
                    )
                    .cornerRadius(24)
                    .padding(.horizontal, 20)
                    .scaleEffect(isPresented.wrappedValue ? 1 : 0.92)
                    .opacity(isPresented.wrappedValue ? 1 : 0)
                    .animation(.spring(response: 0.5, dampingFraction: 0.75), value: isPresented.wrappedValue)
            }
        }
    }
    
    // 确认弹窗修饰符 - 增强版本，支持backdrop-blur
    func confirmationModal(
        isPresented: Binding<Bool>,
        title: String,
        message: String,
        confirmText: String = "确认",
        cancelText: String = "取消",
        onConfirm: @escaping () -> Void
    ) -> some View {
        ZStack {
            self
            
            if isPresented.wrappedValue {
                // 精确CSS backdrop-blur-sm效果
                Rectangle()
                    .fill(.regularMaterial)
                    .background(Color.black.opacity(0.4))
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isPresented.wrappedValue = false
                        }
                    }

                VStack(spacing: 20) {
                    VStack(spacing: 12) {
                        Text(title)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .multilineTextAlignment(.center)
                        Text(message)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    
                    HStack(spacing: 12) {
                        Button(cancelText) {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                isPresented.wrappedValue = false
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color(.systemGray6))
                        .foregroundColor(.primary)
                        .cornerRadius(8)
                        
                        Button(confirmText) {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                isPresented.wrappedValue = false
                            }
                            onConfirm()
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                }
                .padding(24)
                .background(Color.white)
                .cornerRadius(16)
                .shadow(color: Color.black.opacity(0.2), radius: 20, x: 0, y: 10)
                .padding(.horizontal, 40)
                .scaleEffect(isPresented.wrappedValue ? 1 : 0.95)
                .opacity(isPresented.wrappedValue ? 1 : 0)
                .animation(.easeInOut(duration: 0.3), value: isPresented.wrappedValue)
            }
        }
    }
}

// 自定义圆角形状
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}
