//
//  Color+Geda.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI

extension Color {
    // MARK: - 基础颜色系统 (基于CSS变量)

    // 背景颜色
    // --bg-shell: #F7F8FC
    static let gedaBgShell = Color(hex: "#F7F8FC")

    // --bg-page: #FFFFFF
    static let gedaBgPage = Color(hex: "#FFFFFF")

    // 文本颜色
    // --text-primary: #1F2937
    static let gedaTextPrimary = Color(hex: "#1F2937")

    // --text-secondary: #6B7280
    static let gedaTextSecondary = Color(hex: "#6B7280")

    // --text-tertiary: #9CA3AF (灰色400)
    static let gedaTextTertiary = Color(hex: "#9CA3AF")

    // 主题色
    // --accent-start: #818cf8
    static let gedaAccentStart = Color(hex: "#818cf8")

    // --accent-end: #a78bfa
    static let gedaAccentEnd = Color(hex: "#a78bfa")

    // 优先级颜色
    // --priority-high: #ef4444 (red-500)
    static let gedaPriorityHigh = Color(hex: "#ef4444")

    // --priority-medium: #f97316 (orange-500)
    static let gedaPriorityMedium = Color(hex: "#f97316")

    // --priority-low: #22c55e (green-500)
    static let gedaPriorityLow = Color(hex: "#22c55e")

    // 阴影颜色
    // --card-shadow: 0 10px 35px -5px rgba(180, 190, 220, 0.2)
    static let gedaCardShadow = Color(red: 180/255, green: 190/255, blue: 220/255).opacity(0.2)
    
    // 初始化十六进制颜色
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }

    // MARK: - 功能性颜色

    // 成功状态
    static let gedaSuccess = Color(hex: "#10b981") // green-500

    // 警告状态
    static let gedaWarning = Color(hex: "#f59e0b") // amber-500

    // 错误状态
    static let gedaError = Color(hex: "#ef4444") // red-500

    // 信息状态
    static let gedaInfo = Color(hex: "#3b82f6") // blue-500

    // MARK: - 中性颜色

    // 灰色系列
    static let gedaGray50 = Color(hex: "#f9fafb")
    static let gedaGray100 = Color(hex: "#f3f4f6")
    static let gedaGray200 = Color(hex: "#e5e7eb")
    static let gedaGray300 = Color(hex: "#d1d5db")
    static let gedaGray400 = Color(hex: "#9ca3af")
    static let gedaGray500 = Color(hex: "#6b7280")
    static let gedaGray600 = Color(hex: "#4b5563")
    static let gedaGray700 = Color(hex: "#374151")
    static let gedaGray800 = Color(hex: "#1f2937")
    static let gedaGray900 = Color(hex: "#111827")

    // MARK: - 语义化颜色

    // 背景颜色
    static let gedaBackgroundPrimary = gedaBgShell
    static let gedaBackgroundSecondary = gedaBgPage
    static let gedaBgSecondary = gedaBgPage  // 添加缺少的颜色别名

    // 分隔线颜色
    static let gedaDivider = gedaGray200

    // 占位符文本颜色
    static let gedaPlaceholder = gedaGray400

    // 禁用状态颜色
    static let gedaDisabled = gedaGray300

    // 选中状态背景
    static let gedaSelected = gedaAccentStart.opacity(0.1)

    // 悬停状态背景
    static let gedaHover = gedaGray50

    // MARK: - 工具方法

    // 获取优先级对应的颜色
    static func priorityColor(for priority: String) -> Color {
        switch priority.lowercased() {
        case "high":
            return .gedaPriorityHigh
        case "medium":
            return .gedaPriorityMedium
        case "low":
            return .gedaPriorityLow
        default:
            return .gedaPriorityMedium
        }
    }
}
