//
//  Constants.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import Foundation

struct Constants {
    // 应用尺寸 (基于HTML原型)
    struct AppSize {
        static let maxWidth: CGFloat = 420
        static let maxHeight: CGFloat = 880
    }
    
    // 动画时长
    struct Animation {
        static let cardExpansion: Double = 0.4
        static let modalPresentation: Double = 0.3
        static let buttonScale: Double = 0.15
        static let scanLine: Double = 2.5
    }
    
    // 番茄钟设置
    struct Pomodoro {
        static let defaultWorkTime: Int = 25 // 分钟
        static let defaultBreakTime: Int = 5 // 分钟
        static let maxPomodoroCount: Int = 10
        static let minPomodoroCount: Int = 1
    }
    
    // 布局间距
    struct Spacing {
        static let small: CGFloat = 8
        static let medium: CGFloat = 16
        static let large: CGFloat = 24
        static let extraLarge: CGFloat = 32
    }
    
    // 圆角半径
    struct CornerRadius {
        static let small: CGFloat = 8
        static let medium: CGFloat = 12
        static let large: CGFloat = 20
        static let button: CGFloat = 12
        static let card: CGFloat = 20
    }
}
