//
//  Enums.swift
//  Geda
//
//  Created by AI Assistant on 2025/7/10.
//

import SwiftUI

// MARK: - 任务相关枚举

/// 任务优先级枚举
enum Priority: String, CaseIterable {
    case high = "high"
    case medium = "medium"
    case low = "low"
    
    var displayName: String {
        switch self {
        case .high: return "高"
        case .medium: return "中"
        case .low: return "低"
        }
    }
    
    var title: String {
        return displayName
    }
    
    var color: Color {
        switch self {
        case .high: return .gedaPriorityHigh
        case .medium: return .gedaPriorityMedium
        case .low: return .gedaPriorityLow
        }
    }
    
    var sortOrder: Int {
        switch self {
        case .high: return 0
        case .medium: return 1
        case .low: return 2
        }
    }
}

/// 任务排序选项
enum TaskSortOption: String, CaseIterable {
    case startTime = "开始时间"
    case priority = "优先级"
    case title = "标题"
    case createdAt = "创建时间"
    
    var displayName: String {
        return self.rawValue
    }
}

/// 任务错误类型
enum TaskError: LocalizedError {
    case invalidForm
    case taskNotFound
    
    var errorDescription: String? {
        switch self {
        case .invalidForm:
            return "请填写完整的任务信息"
        case .taskNotFound:
            return "未找到指定任务"
        }
    }
}

// MARK: - 计划相关枚举

/// 计划状态枚举
enum PlanStatus: String, CaseIterable {
    case active = "active"
    case paused = "paused"
    case completed = "completed"
    
    var displayName: String {
        switch self {
        case .active: return "进行中"
        case .paused: return "已暂停"
        case .completed: return "已完成"
        }
    }
    
    var title: String {
        switch self {
        case .active: return "进行中"
        case .paused: return "已暂停"
        case .completed: return "已完成"
        }
    }
    
    var color: Color {
        switch self {
        case .active: return .gedaAccentStart
        case .paused: return .gedaWarning
        case .completed: return .gedaSuccess
        }
    }
    
    var textColor: Color {
        switch self {
        case .active: return .white
        case .paused: return .gedaTextPrimary
        case .completed: return .white
        }
    }
    
    var backgroundColor: Color {
        switch self {
        case .active: return .gedaAccentStart
        case .paused: return .gedaWarning.opacity(0.2)
        case .completed: return .gedaSuccess
        }
    }
}

/// 计划排序选项
enum PlanSortOption: String, CaseIterable {
    case title = "标题"
    case progress = "进度"
    case createdAt = "创建时间"
    case startDate = "开始时间"
}

/// 计划错误类型
enum PlanError: LocalizedError {
    case invalidForm
    case planNotFound
    case shareGenerationFailed
    case invalidShareCode
    case emptyShareCode
    
    var errorDescription: String? {
        switch self {
        case .invalidForm:
            return "请填写完整的计划信息"
        case .planNotFound:
            return "未找到指定计划"
        case .shareGenerationFailed:
            return "生成分享码失败"
        case .invalidShareCode:
            return "无效的分享码"
        case .emptyShareCode:
            return "请输入分享码"
        }
    }
}

// MARK: - 番茄钟相关枚举

/// 番茄钟状态
enum PomodoroState {
    case idle           // 空闲
    case working        // 工作中
    case shortBreak     // 短休息
    case longBreak      // 长休息
    case paused         // 暂停
    
    var title: String {
        switch self {
        case .idle: return "准备开始"
        case .working: return "专注中"
        case .shortBreak: return "短休息"
        case .longBreak: return "长休息"
        case .paused: return "已暂停"
        }
    }
    
    var color: Color {
        switch self {
        case .idle: return .gedaGray500
        case .working: return .gedaAccentStart
        case .shortBreak: return .gedaSuccess
        case .longBreak: return .gedaInfo
        case .paused: return .gedaWarning
        }
    }
}

/// 番茄钟类型
enum PomodoroType {
    case work
    case shortBreak
    case longBreak
    
    var duration: TimeInterval {
        switch self {
        case .work: return 25 * 60        // 25分钟
        case .shortBreak: return 5 * 60   // 5分钟
        case .longBreak: return 15 * 60   // 15分钟
        }
    }
    
    var title: String {
        switch self {
        case .work: return "工作时间"
        case .shortBreak: return "短休息"
        case .longBreak: return "长休息"
        }
    }
}

// MARK: - 界面相关枚举

/// 首页Tab枚举
enum HomeTab: String, CaseIterable {
    case schedule = "日程"
    case plan = "计划"
}

/// 时间范围枚举
enum TimeRange: String, CaseIterable {
    case week = "本周"
    case month = "本月"
    case quarter = "本季度"
    case year = "本年"
    case custom = "自定义"
}

/// 通知类型
enum NotificationType {
    case taskReminder(taskId: String)
    case pomodoroComplete(sessionId: String)
    case breakComplete(sessionId: String)
    case dailyReview
    
    var title: String {
        switch self {
        case .taskReminder: return "任务提醒"
        case .pomodoroComplete: return "专注时间结束"
        case .breakComplete: return "休息时间结束"
        case .dailyReview: return "每日复盘"
        }
    }
}

// MARK: - 数据相关枚举

/// 排序方向
enum SortDirection {
    case ascending
    case descending
    
    var title: String {
        switch self {
        case .ascending: return "升序"
        case .descending: return "降序"
        }
    }
}

/// 筛选状态
enum FilterStatus {
    case all
    case completed
    case pending
    
    var title: String {
        switch self {
        case .all: return "全部"
        case .completed: return "已完成"
        case .pending: return "进行中"
        }
    }
}

// MARK: - 错误相关枚举

/// 应用错误类型
enum AppError: LocalizedError {
    case networkError(String)
    case dataError(String)
    case validationError(String)
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .networkError(let message):
            return "网络错误: \(message)"
        case .dataError(let message):
            return "数据错误: \(message)"
        case .validationError(let message):
            return "验证错误: \(message)"
        case .unknownError:
            return "未知错误"
        }
    }
}

// MARK: - 分享数据模型

/// 计划分享数据模型
struct PlanShareData: Codable {
    let title: String
    let description: String
    let startDate: Date
    let endDate: Date
    let totalTomatoes: Int
    let tasks: [TaskShareData]
    let createdBy: String
    let version: String
    
    enum CodingKeys: String, CodingKey {
        case title, description, startDate, endDate, totalTomatoes, tasks, createdBy, version
    }
}

/// 任务分享数据模型
struct TaskShareData: Codable {
    let title: String
    let priority: String
    let tomatoCount: Int
    let isCompleted: Bool
    
    enum CodingKeys: String, CodingKey {
        case title, priority, tomatoCount, isCompleted
    }
}
