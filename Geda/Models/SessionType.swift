import SwiftUI

/// 番茄钟会话类型
enum SessionType: String, CaseIterable {
    case work = "work"
    case shortBreak = "short_break"
    case longBreak = "long_break"
    
    var title: String {
        switch self {
        case .work: return "工作"
        case .shortBreak: return "短休息"
        case .longBreak: return "长休息"
        }
    }
    
    var color: Color {
        switch self {
        case .work: return .blue
        case .shortBreak: return .green
        case .longBreak: return .purple
        }
    }
}

/// 计时器状态
enum TimerState {
    case stopped
    case running
    case paused
}