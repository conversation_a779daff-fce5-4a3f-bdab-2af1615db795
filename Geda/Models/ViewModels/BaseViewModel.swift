import Foundation
import Combine
import SwiftUI

/// ViewModel基础类 - 提供通用功能
class BaseViewModel: ObservableObject {
    
    // MARK: - 通用状态属性
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var showError: Bool = false
    
    // MARK: - 依赖注入
    internal let repositoryManager: RepositoryManager

    // MARK: - Combine管理
    internal var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    init(repositoryManager: RepositoryManager = RepositoryManager.shared) {
        self.repositoryManager = repositoryManager
    }
    
    // MARK: - 通用方法
    
    /// 设置加载状态
    func setLoading(_ loading: Bool) {
        DispatchQueue.main.async {
            self.isLoading = loading
        }
    }
    
    /// 处理错误
    func handleError(_ error: Error) {
        DispatchQueue.main.async {
            self.isLoading = false
            self.errorMessage = error.localizedDescription
            self.showError = true
        }
    }
    
    /// 清除错误
    func clearError() {
        DispatchQueue.main.async {
            self.errorMessage = nil
            self.showError = false
        }
    }
    
    /// 显示成功消息
    func showSuccess(_ message: String) {
        // 可以在子类中重写以显示成功提示
        print("成功: \(message)")
    }
    
    /// 重置状态
    func resetState() {
        DispatchQueue.main.async {
            self.isLoading = false
            self.errorMessage = nil
            self.showError = false
        }
    }
    
    // MARK: - 生命周期方法
    
    /// 视图出现时调用
    func onAppear() {
        // 子类可以重写此方法
    }
    
    /// 视图消失时调用
    func onDisappear() {
        // 子类可以重写此方法
    }
    
    /// 刷新数据
    func refresh() {
        // 子类可以重写此方法
    }
    
    deinit {
        cancellables.removeAll()
        // 性能优化：确保清理所有订阅
        print("🧹 BaseViewModel deinit - 清理资源完成")
    }
}

// MARK: - 扩展方法
extension BaseViewModel {
    
    /// 执行异步操作的通用方法
    func performAsyncOperation<T>(
        _ operation: AnyPublisher<T, Error>,
        onSuccess: @escaping (T) -> Void,
        onFailure: ((Error) -> Void)? = nil
    ) {
        setLoading(true)
        
        operation
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.setLoading(false)
                    
                    switch completion {
                    case .finished:
                        break
                    case .failure(let error):
                        if let onFailure = onFailure {
                            onFailure(error)
                        } else {
                            self?.handleError(error)
                        }
                    }
                },
                receiveValue: onSuccess
            )
            .store(in: &cancellables)
    }
}

// MARK: - 通用数据类型
struct ViewState<T> {
    var data: T?
    var isLoading: Bool = false
    var error: Error?
    
    var hasData: Bool {
        return data != nil
    }
    
    var hasError: Bool {
        return error != nil
    }
}
