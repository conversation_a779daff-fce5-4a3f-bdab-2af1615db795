import Foundation
import Combine
import SwiftUI

/// 主页面ViewModel - 管理首页业务逻辑
class HomeViewModel: BaseViewModel {
    
    // MARK: - Published属性 (UI绑定)
    @Published var currentUser: User?
    @Published var todayTasks: [Task] = []
    @Published var activePlans: [Plan] = []
    @Published var selectedTab: HomeTab = .schedule
    @Published var showAddMenu: Bool = false
    @Published var todayCompletedTasks: Int = 0
    @Published var todayTotalTasks: Int = 0
    @Published var currentDate: Date = Date()
    
    // MARK: - 计算属性
    var todayProgress: Float {
        guard todayTotalTasks > 0 else { return 0.0 }
        return Float(todayCompletedTasks) / Float(todayTotalTasks)
    }
    
    var hasTasksToday: Bool {
        return !todayTasks.isEmpty
    }
    
    var upcomingTasks: [Task] {
        return todayTasks.filter { !$0.isCompleted && $0.startTime! > Date() }
    }
    
    var overdueTasks: [Task] {
        return todayTasks.filter { !$0.isCompleted && $0.endTime! < Date() }
    }
    
    // MARK: - 初始化
    override init(repositoryManager: RepositoryManager = RepositoryManager.shared) {
        super.init(repositoryManager: repositoryManager)
        loadInitialData()
    }
    
    // MARK: - 生命周期方法
    override func onAppear() {
        refresh()
    }
    
    override func refresh() {
        loadCurrentUser()
        loadTodayTasks()
        loadActivePlans()
    }
    
    // MARK: - 数据加载方法
    
    /// 加载初始数据
    private func loadInitialData() {
        loadCurrentUser()
        loadTodayTasks()
        loadActivePlans()
    }
    
    /// 加载当前用户
    func loadCurrentUser() {
        performAsyncOperation(
            repositoryManager.userRepository.fetchCurrentUser()
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] user in
            self?.currentUser = user
            if user == nil {
                self?.createDefaultUser()
            }
        }
    }
    
    /// 创建默认用户
    private func createDefaultUser() {
        let context = CoreDataManager.shared.viewContext
        let newUser = User(context: context)
        newUser.id = UUID().uuidString
        newUser.name = "用户"
        newUser.checkInDays = 0
        newUser.createdAt = Date()
        newUser.updatedAt = Date()

        performAsyncOperation(
            repositoryManager.userRepository.createUser(newUser)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] user in
            self?.currentUser = user
        }
    }
    
    /// 加载今日任务
    func loadTodayTasks() {
        performAsyncOperation(
            repositoryManager.taskRepository.fetchTodayTasks()
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] tasks in
            self?.todayTasks = tasks
            self?.updateTodayStatistics()
        }
    }
    
    /// 加载活跃计划
    func loadActivePlans() {
        guard let user = currentUser else { return }
        
        performAsyncOperation(
            repositoryManager.planRepository.fetchActivePlans(for: user)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] plans in
            self?.activePlans = plans
        }
    }
    
    /// 更新今日统计
    private func updateTodayStatistics() {
        todayTotalTasks = todayTasks.count
        todayCompletedTasks = todayTasks.filter { $0.isCompleted }.count
    }
    
    // MARK: - 用户操作方法
    
    /// 切换标签页
    func switchTab(to tab: HomeTab) {
        selectedTab = tab
    }
    
    /// 切换添加菜单显示状态
    func toggleAddMenu() {
        showAddMenu.toggle()
    }
    
    /// 切换任务完成状态
    func toggleTaskCompletion(_ task: Task) {
        performAsyncOperation(
            repositoryManager.taskRepository.toggleTaskCompletion(task)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] updatedTask in
            self?.updateTaskInList(updatedTask)
            self?.updateTodayStatistics()
            self?.showSuccess("任务状态已更新")
        }
    }
    
    /// 更新任务列表中的任务
    private func updateTaskInList(_ updatedTask: Task) {
        if let index = todayTasks.firstIndex(where: { $0.id == updatedTask.id }) {
            todayTasks[index] = updatedTask
        }
    }
    
    /// 删除任务
    func deleteTask(_ task: Task) {
        performAsyncOperation(
            repositoryManager.taskRepository.deleteTask(task)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] _ in
            self?.todayTasks.removeAll { $0.id == task.id }
            self?.updateTodayStatistics()
            self?.showSuccess("任务已删除")
        }
    }
    
    /// 用户打卡
    func checkIn() {
        guard let user = currentUser else { return }
        
        performAsyncOperation(
            repositoryManager.userRepository.incrementCheckInDays(for: user)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] updatedUser in
            self?.currentUser = updatedUser
            self?.showSuccess("打卡成功！")
        }
    }
    
    /// 更新当前日期
    func updateCurrentDate(_ date: Date) {
        currentDate = date
        loadTasksForDate(date)
    }
    
    /// 加载指定日期的任务
    private func loadTasksForDate(_ date: Date) {
        performAsyncOperation(
            repositoryManager.taskRepository.fetchTasksForDate(date)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] tasks in
            self?.todayTasks = tasks
            self?.updateTodayStatistics()
        }
    }
}


