import Foundation
import Combine
import SwiftUI

/// 计划管理ViewModel - 管理计划CRUD和业务逻辑
class PlanViewModel: BaseViewModel {
    
    // MARK: - Published属性 (UI绑定)
    @Published var plans: [Plan] = []
    @Published var activePlans: [Plan] = []
    @Published var completedPlans: [Plan] = []
    @Published var pausedPlans: [Plan] = []
    @Published var selectedPlan: Plan?
    @Published var showPlanDetail: Bool = false
    @Published var showPlanCreation: Bool = false
    @Published var showPlanEdit: Bool = false
    @Published var planToDelete: Plan?
    @Published var showDeleteConfirmation: Bool = false
    @Published var currentUser: User?
    
    // MARK: - 计划创建/编辑表单
    @Published var planTitle: String = ""
    @Published var planDescription: String = ""
    @Published var planStartDate: Date = Date()
    @Published var planEndDate: Date = Calendar.current.date(byAdding: .month, value: 1, to: Date()) ?? Date()
    @Published var planStatus: PlanStatus = .active
    
    // MARK: - 筛选和显示选项
    @Published var selectedStatusFilter: PlanStatus? = nil
    @Published var showOnlyMyPlans: Bool = true
    @Published var sortOption: PlanSortOption = .createdAt
    @Published var sortAscending: Bool = false
    
    // MARK: - 计算属性
    var filteredPlans: [Plan] {
        var filtered = plans
        
        // 按状态筛选
        if let status = selectedStatusFilter {
            filtered = filtered.filter { $0.status == status.rawValue }
        }
        
        // 排序
        filtered.sort { plan1, plan2 in
            let result: Bool
            switch sortOption {
            case .title:
                result = plan1.title! < plan2.title!
            case .progress:
                result = plan1.progress < plan2.progress
            case .createdAt:
                result = plan1.createdAt! < plan2.createdAt!
            case .startDate:
                result = plan1.startDate! < plan2.startDate!
            }
            return sortAscending ? result : !result
        }
        
        return filtered
    }
    
    var hasValidPlanForm: Bool {
        return !planTitle.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               planStartDate <= planEndDate
    }
    
    var activePlansCount: Int {
        return activePlans.count
    }
    
    var completedPlansCount: Int {
        return completedPlans.count
    }
    
    var totalProgress: Float {
        guard !activePlans.isEmpty else { return 0.0 }
        let totalProgress = activePlans.reduce(0) { $0 + $1.progress }
        return totalProgress / Float(activePlans.count)
    }
    
    // MARK: - 初始化
    override init(repositoryManager: RepositoryManager = RepositoryManager.shared) {
        super.init(repositoryManager: repositoryManager)
        loadCurrentUser()
    }
    
    // MARK: - 生命周期方法
    override func onAppear() {
        refresh()
    }
    
    override func refresh() {
        loadPlans()
        loadPlansByStatus()
    }
    
    // MARK: - 数据加载方法
    
    /// 加载当前用户
    private func loadCurrentUser() {
        performAsyncOperation(
            repositoryManager.userRepository.fetchCurrentUser()
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] user in
            self?.currentUser = user
            if let user = user {
                self?.loadPlans(for: user)
            }
        }
    }
    
    /// 加载所有计划
    func loadPlans() {
        guard let user = currentUser else { return }
        loadPlans(for: user)
    }
    
    /// 加载指定用户的计划
    func loadPlans(for user: User) {
        performAsyncOperation(
            repositoryManager.planRepository.fetchPlans(for: user)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] plans in
            self?.plans = plans
        }
    }
    
    /// 按状态加载计划
    func loadPlansByStatus() {
        guard let user = currentUser else { return }
        
        // 加载活跃计划
        performAsyncOperation(
            repositoryManager.planRepository.fetchActivePlans(for: user)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] plans in
            self?.activePlans = plans
        }
        
        // 加载已完成计划
        performAsyncOperation(
            repositoryManager.planRepository.fetchCompletedPlans(for: user)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] plans in
            self?.completedPlans = plans
        }
        
        // 加载暂停计划
        performAsyncOperation(
            repositoryManager.planRepository.fetchPausedPlans(for: user)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] plans in
            self?.pausedPlans = plans
        }
    }
    
    // MARK: - 计划操作方法
    
    /// 创建新计划
    func createPlan() {
        guard let user = currentUser, hasValidPlanForm else {
            handleError(PlanError.invalidForm)
            return
        }
        
        let context = CoreDataManager.shared.viewContext
        let newPlan = Plan(context: context)
        newPlan.id = UUID().uuidString
        newPlan.title = planTitle.trimmingCharacters(in: .whitespacesAndNewlines)
        newPlan.planDescription = planDescription.trimmingCharacters(in: .whitespacesAndNewlines)
        newPlan.startDate = planStartDate
        newPlan.endDate = planEndDate
        newPlan.status = planStatus.rawValue
        newPlan.progress = 0.0
        newPlan.totalTomatoes = 0
        newPlan.user = user
        
        performAsyncOperation(
            repositoryManager.planRepository.createPlan(newPlan)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] plan in
            self?.plans.append(plan)
            self?.updatePlansByStatus(plan)
            self?.resetPlanForm()
            self?.showPlanCreation = false
            self?.showSuccess("计划创建成功")
        }
    }
    
    /// 更新计划
    func updatePlan() {
        guard let plan = selectedPlan, hasValidPlanForm else {
            handleError(PlanError.invalidForm)
            return
        }
        
        plan.title = planTitle.trimmingCharacters(in: .whitespacesAndNewlines)
        plan.planDescription = planDescription.trimmingCharacters(in: .whitespacesAndNewlines)
        plan.startDate = planStartDate
        plan.endDate = planEndDate
        plan.status = planStatus.rawValue
        
        performAsyncOperation(
            repositoryManager.planRepository.updatePlan(plan)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] updatedPlan in
            self?.updatePlanInList(updatedPlan)
            self?.resetPlanForm()
            self?.showPlanEdit = false
            self?.showSuccess("计划更新成功")
        }
    }
    
    /// 删除计划
    func deletePlan(_ plan: Plan) {
        performAsyncOperation(
            repositoryManager.planRepository.deletePlan(plan)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] _ in
            self?.plans.removeAll { $0.id == plan.id }
            self?.removePlanFromStatusLists(plan)
            self?.showSuccess("计划删除成功")
        }
    }
    
    /// 更新计划进度
    func updatePlanProgress(_ plan: Plan) {
        performAsyncOperation(
            repositoryManager.planRepository.updatePlanProgress(plan)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] updatedPlan in
            self?.updatePlanInList(updatedPlan)
            self?.showSuccess("计划进度已更新")
        }
    }
    
    /// 暂停计划
    func pausePlan(_ plan: Plan) {
        plan.status = PlanStatus.paused.rawValue
        updatePlan(plan)
    }
    
    /// 恢复计划
    func resumePlan(_ plan: Plan) {
        plan.status = PlanStatus.active.rawValue
        updatePlan(plan)
    }
    
    /// 完成计划
    func completePlan(_ plan: Plan) {
        plan.status = PlanStatus.completed.rawValue
        plan.endDate = Date()
        updatePlan(plan)
    }
    
    /// 重新开始计划
    func restartPlan(_ plan: Plan) {
        plan.status = PlanStatus.active.rawValue
        plan.startDate = Date()
        plan.progress = 0.0
        updatePlan(plan)
    }
    
    /// 分享计划
    func sharePlan(_ plan: Plan) {
        // 生成分享数据
        let shareData = generatePlanShareData(plan)
        
        // 生成分享码
        if let shareCode = generateShareCode(from: shareData) {
            showPlanShareModal(with: shareCode, planTitle: plan.title ?? "未命名计划")
        } else {
            handleError(PlanError.shareGenerationFailed)
        }
    }
    
    // MARK: - 分享相关属性
    @Published var showPlanShareModal = false
    @Published var currentShareCode = ""
    @Published var currentSharePlanTitle = ""
    
    /// 显示计划分享弹窗
    private func showPlanShareModal(with shareCode: String, planTitle: String) {
        currentShareCode = shareCode
        currentSharePlanTitle = planTitle
        showPlanShareModal = true
    }
    
    /// 生成计划分享数据
    private func generatePlanShareData(_ plan: Plan) -> PlanShareData {
        return PlanShareData(
            title: plan.title ?? "",
            description: plan.planDescription ?? "",
            startDate: plan.startDate ?? Date(),
            endDate: plan.endDate ?? Date(),
            totalTomatoes: Int(plan.totalTomatoes),
            tasks: extractTasksData(from: plan),
            createdBy: currentUser?.name ?? "匿名用户",
            version: "1.0"
        )
    }
    
    /// 提取任务数据
    private func extractTasksData(from plan: Plan) -> [TaskShareData] {
        guard let tasks = plan.tasks?.allObjects as? [Task] else { return [] }
        
        return tasks.map { task in
            TaskShareData(
                title: task.title ?? "",
                priority: task.priority ?? "medium",
                tomatoCount: Int(task.tomatoCount),
                isCompleted: task.isCompleted
            )
        }
    }
    
    /// 生成分享码
    private func generateShareCode(from shareData: PlanShareData) -> String? {
        do {
            let jsonData = try JSONEncoder().encode(shareData)
            let base64String = jsonData.base64EncodedString()
            
            // 添加前缀以便识别
            return "GEDA_PLAN_V1_" + base64String
        } catch {
            print("生成分享码失败: \(error)")
            return nil
        }
    }
    
    /// 解析分享码
    func parsePlanShareCode(_ shareCode: String) -> PlanShareData? {
        // 检查前缀
        guard shareCode.hasPrefix("GEDA_PLAN_V1_") else {
            handleError(PlanError.invalidShareCode)
            return nil
        }
        
        // 移除前缀
        let base64String = String(shareCode.dropFirst("GEDA_PLAN_V1_".count))
        
        // 解码
        guard let jsonData = Data(base64Encoded: base64String) else {
            handleError(PlanError.invalidShareCode)
            return nil
        }
        
        do {
            let shareData = try JSONDecoder().decode(PlanShareData.self, from: jsonData)
            return shareData
        } catch {
            handleError(PlanError.invalidShareCode)
            return nil
        }
    }
    
    /// 从分享码导入计划
    func importPlanFromShareCode(_ shareCode: String) {
        guard let shareData = parsePlanShareCode(shareCode) else { return }
        
        // 创建新计划
        let context = CoreDataManager.shared.viewContext
        let newPlan = Plan(context: context)
        newPlan.id = UUID().uuidString
        newPlan.title = shareData.title
        newPlan.planDescription = shareData.description
        newPlan.startDate = shareData.startDate
        newPlan.endDate = shareData.endDate
        newPlan.status = PlanStatus.active.rawValue
        newPlan.progress = 0.0
        newPlan.totalTomatoes = Int32(shareData.totalTomatoes)
        newPlan.user = currentUser
        newPlan.createdAt = Date()
        newPlan.updatedAt = Date()
        
        performAsyncOperation(
            repositoryManager.planRepository.createPlan(newPlan)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] plan in
            // 创建任务
            self?.createTasksFromShareData(shareData.tasks, for: plan)
            
            self?.plans.append(plan)
            self?.updatePlansByStatus(plan)
            self?.showSuccess("计划导入成功！来自：\(shareData.createdBy)")
        }
    }
    
    /// 从分享数据创建任务
    private func createTasksFromShareData(_ tasksData: [TaskShareData], for plan: Plan) {
        let context = CoreDataManager.shared.viewContext
        
        for taskData in tasksData {
            let newTask = Task(context: context)
            newTask.id = UUID().uuidString
            newTask.title = taskData.title
            newTask.priority = taskData.priority
            newTask.tomatoCount = Int32(taskData.tomatoCount)
            newTask.isCompleted = false // 导入的任务都设为未完成
            newTask.plan = plan
            newTask.createdAt = Date()
            newTask.updatedAt = Date()
            
            // 设置默认时间（当天）
            newTask.startTime = Date()
            newTask.endTime = Calendar.current.date(byAdding: .hour, value: 1, to: Date()) ?? Date()
        }
        
        // 保存到Core Data会自动处理
    }
    
    // MARK: - 分享模态控制
    @Published var showImportModal = false
    @Published var shareCodeInput = ""
    
    /// 显示导入分享码模态
    func showImportShareCodeModal() {
        shareCodeInput = ""
        showImportModal = true
    }
    
    /// 执行导入分享码
    func executeImportShareCode() {
        guard !shareCodeInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            handleError(PlanError.emptyShareCode)
            return
        }
        
        importPlanFromShareCode(shareCodeInput.trimmingCharacters(in: .whitespacesAndNewlines))
        showImportModal = false
        shareCodeInput = ""
    }
    
    // MARK: - UI操作方法
    
    /// 显示计划详情
    func showPlanDetails(_ plan: Plan) {
        selectedPlan = plan
        showPlanDetail = true
    }
    
    /// 显示计划编辑
    func showPlanEditor(_ plan: Plan) {
        selectedPlan = plan
        loadPlanToForm(plan)
        showPlanEdit = true
    }
    
    /// 显示计划创建
    func showPlanCreator() {
        resetPlanForm()
        showPlanCreation = true
    }
    
    /// 确认删除计划
    func confirmDeletePlan(_ plan: Plan) {
        planToDelete = plan
        showDeleteConfirmation = true
    }
    
    /// 执行删除计划
    func executeDeletePlan() {
        guard let plan = planToDelete else { return }
        deletePlan(plan)
        planToDelete = nil
        showDeleteConfirmation = false
    }
    
    // MARK: - 表单管理方法
    
    /// 重置计划表单
    func resetPlanForm() {
        planTitle = ""
        planDescription = ""
        planStartDate = Date()
        planEndDate = Calendar.current.date(byAdding: .month, value: 1, to: Date()) ?? Date()
        planStatus = .active
    }
    
    /// 加载计划到表单
    private func loadPlanToForm(_ plan: Plan) {
        planTitle = plan.title ?? ""
        planDescription = plan.planDescription ?? ""
        planStartDate = plan.startDate ?? Date()
        planEndDate = plan.endDate ?? Date()
        planStatus = PlanStatus(rawValue: plan.status ?? "active") ?? .active
    }
    
    /// 更新计划列表中的计划
    private func updatePlanInList(_ updatedPlan: Plan) {
        if let index = plans.firstIndex(where: { $0.id == updatedPlan.id }) {
            plans[index] = updatedPlan
        }
        updatePlansByStatus(updatedPlan)
    }
    
    /// 更新状态列表中的计划
    private func updatePlansByStatus(_ plan: Plan) {
        // 从所有状态列表中移除
        removePlanFromStatusLists(plan)
        
        // 添加到对应状态列表
        switch PlanStatus(rawValue: plan.status ?? "active") {
        case .active:
            activePlans.append(plan)
        case .completed:
            completedPlans.append(plan)
        case .paused:
            pausedPlans.append(plan)
        case .none:
            break
        }
    }
    
    /// 从状态列表中移除计划
    private func removePlanFromStatusLists(_ plan: Plan) {
        activePlans.removeAll { $0.id == plan.id }
        completedPlans.removeAll { $0.id == plan.id }
        pausedPlans.removeAll { $0.id == plan.id }
    }
    
    /// 更新计划（内部方法）
    private func updatePlan(_ plan: Plan) {
        performAsyncOperation(
            repositoryManager.planRepository.updatePlan(plan)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] updatedPlan in
            self?.updatePlanInList(updatedPlan)
        }
    }
    
    // MARK: - 筛选和排序方法
    
    /// 设置状态筛选
    func setStatusFilter(_ status: PlanStatus?) {
        selectedStatusFilter = status
    }
    
    /// 设置排序选项
    func setSortOption(_ option: PlanSortOption, ascending: Bool = false) {
        sortOption = option
        sortAscending = ascending
    }
    
    /// 清除筛选
    func clearFilters() {
        selectedStatusFilter = nil
    }
}

// 枚举定义已移至 Geda/Models/Enums.swift
