import Foundation
import Combine
import SwiftUI

/// 任务管理ViewModel - 管理任务CRUD和业务逻辑
class TaskViewModel: BaseViewModel {
    
    // MARK: - 私有属性
    private let notificationManager = NotificationManager.shared
    
    // MARK: - Published属性 (UI绑定)
    @Published var tasks: [Task] = []
    @Published var selectedTask: Task?
    @Published var showTaskDetail: Bool = false
    @Published var showTaskCreation: Bool = false
    @Published var showTaskEdit: Bool = false
    @Published var taskToDelete: Task?
    @Published var showDeleteConfirmation: Bool = false
    
    // MARK: - 任务创建/编辑表单
    @Published var taskTitle: String = ""
    @Published var taskStartTime: Date = Date()
    @Published var taskEndTime: Date = Date().addingTimeInterval(3600) // 默认1小时后
    @Published var taskPriority: Priority = .medium
    @Published var taskTomatoCount: Int = 1
    @Published var taskReminderEnabled: Bool = false
    @Published var selectedPlan: Plan?
    
    // MARK: - 筛选和排序
    @Published var filterPriority: Priority?
    @Published var filterCompleted: Bool?
    @Published var sortOption: TaskSortOption = .startTime
    @Published var sortAscending: Bool = true
    
    // MARK: - 计算属性
    var filteredTasks: [Task] {
        var filtered = tasks
        
        // 按优先级筛选
        if let priority = filterPriority {
            filtered = filtered.filter { $0.priority == priority.rawValue }
        }
        
        // 按完成状态筛选
        if let completed = filterCompleted {
            filtered = filtered.filter { $0.isCompleted == completed }
        }
        
        // 排序
        filtered.sort { task1, task2 in
            let result: Bool
            switch sortOption {
            case .startTime:
                result = task1.startTime! < task2.startTime!
            case .priority:
                let priority1 = Priority(rawValue: task1.priority!) ?? .medium
                let priority2 = Priority(rawValue: task2.priority!) ?? .medium
                result = priority1.sortOrder < priority2.sortOrder
            case .title:
                result = task1.title! < task2.title!
            case .createdAt:
                result = task1.createdAt! < task2.createdAt!
            }
            return sortAscending ? result : !result
        }
        
        return filtered
    }
    
    var hasValidTaskForm: Bool {
        return !taskTitle.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               taskStartTime < taskEndTime &&
               taskTomatoCount > 0
    }
    
    // MARK: - 初始化
    override init(repositoryManager: RepositoryManager = RepositoryManager.shared) {
        super.init(repositoryManager: repositoryManager)
    }
    
    // MARK: - 生命周期方法
    override func onAppear() {
        loadTasks()
    }
    
    override func refresh() {
        loadTasks()
    }
    
    // MARK: - 数据加载方法
    
    /// 加载任务列表
    func loadTasks() {
        performAsyncOperation(
            repositoryManager.taskRepository.fetchTodayTasks()
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] tasks in
            self?.tasks = tasks
        }
    }
    
    /// 加载指定计划的任务
    func loadTasks(for plan: Plan) {
        performAsyncOperation(
            repositoryManager.taskRepository.fetchTasks(for: plan)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] tasks in
            self?.tasks = tasks
        }
    }
    
    /// 加载指定日期的任务
    func loadTasks(for date: Date) {
        performAsyncOperation(
            repositoryManager.taskRepository.fetchTasksForDate(date)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] tasks in
            self?.tasks = tasks
        }
    }
    
    // MARK: - 任务操作方法
    
    /// 创建新任务 (使用表单数据)
    func createTask() {
        guard hasValidTaskForm else {
            handleError(TaskError.invalidForm)
            return
        }
        
        let context = CoreDataManager.shared.viewContext
        let newTask = Task(context: context)
        newTask.id = UUID().uuidString
        newTask.title = taskTitle.trimmingCharacters(in: .whitespacesAndNewlines)
        newTask.startTime = taskStartTime
        newTask.endTime = taskEndTime
        newTask.priority = taskPriority.rawValue
        newTask.tomatoCount = Int32(taskTomatoCount)
        newTask.isReminderEnabled = taskReminderEnabled
        newTask.isCompleted = false
        newTask.plan = selectedPlan
        
        performAsyncOperation(
            repositoryManager.taskRepository.createTask(newTask)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] task in
            self?.tasks.append(task)
            
            // 如果启用了提醒，安排通知
            if task.isReminderEnabled {
                self?.notificationManager.scheduleTaskReminder(for: task)
            }
            
            self?.resetTaskForm()
            self?.showTaskCreation = false
            self?.showSuccess("任务创建成功")
        }
    }
    
    /// 创建新任务 (使用参数)
    func createTask(
        title: String,
        startTime: Date,
        endTime: Date,
        priority: Priority,
        tomatoCount: Int32,
        isReminderEnabled: Bool,
        plan: Plan?,
        subtasks: [String] = []
    ) {
        guard !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            handleError(TaskError.invalidForm)
            return
        }
        
        let context = CoreDataManager.shared.viewContext
        let newTask = Task(context: context)
        newTask.id = UUID().uuidString
        newTask.title = title.trimmingCharacters(in: .whitespacesAndNewlines)
        newTask.startTime = startTime
        newTask.endTime = endTime
        newTask.priority = priority.rawValue
        newTask.tomatoCount = tomatoCount
        newTask.isReminderEnabled = isReminderEnabled
        newTask.isCompleted = false
        newTask.plan = plan
        newTask.createdAt = Date()
        newTask.updatedAt = Date()
        
        performAsyncOperation(
            repositoryManager.taskRepository.createTask(newTask)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] task in
            self?.tasks.append(task)
            
            // 如果启用了提醒，安排通知
            if task.isReminderEnabled {
                self?.notificationManager.scheduleTaskReminder(for: task)
            }
            
            // 创建子任务
            for subtaskTitle in subtasks {
                self?.createSubtask(title: subtaskTitle, for: task)
            }
            
            self?.showSuccess("任务创建成功")
        }
    }
    
    /// 创建子任务
    private func createSubtask(title: String, for task: Task) {
        let context = CoreDataManager.shared.viewContext
        let subtask = Subtask(context: context)
        subtask.id = UUID().uuidString
        subtask.title = title
        subtask.isCompleted = false
        subtask.task = task
        subtask.createdAt = Date()
        subtask.updatedAt = Date()
        
        // 这里可以添加保存到仓库的逻辑
        // 由于是作为createTask的一部分，暂时不单独保存
    }
    
    /// 更新任务
    func updateTask() {
        guard let task = selectedTask, hasValidTaskForm else {
            handleError(TaskError.invalidForm)
            return
        }
        
        task.title = taskTitle.trimmingCharacters(in: .whitespacesAndNewlines)
        task.startTime = taskStartTime
        task.endTime = taskEndTime
        task.priority = taskPriority.rawValue
        task.tomatoCount = Int32(taskTomatoCount)
        task.isReminderEnabled = taskReminderEnabled
        task.plan = selectedPlan
        
        performAsyncOperation(
            repositoryManager.taskRepository.updateTask(task)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] updatedTask in
            self?.updateTaskInList(updatedTask)
            
            // 重新安排通知
            self?.notificationManager.cancelTaskReminder(for: updatedTask)
            if updatedTask.isReminderEnabled {
                self?.notificationManager.scheduleTaskReminder(for: updatedTask)
            }
            
            self?.resetTaskForm()
            self?.showTaskEdit = false
            self?.showSuccess("任务更新成功")
        }
    }
    
    /// 删除任务
    func deleteTask(_ task: Task) {
        // 取消任务相关的通知
        notificationManager.cancelTaskReminder(for: task)
        
        performAsyncOperation(
            repositoryManager.taskRepository.deleteTask(task)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] _ in
            self?.tasks.removeAll { $0.id == task.id }
            self?.showSuccess("任务删除成功")
        }
    }
    
    /// 切换任务完成状态
    func toggleTaskCompletion(_ task: Task) {
        performAsyncOperation(
            repositoryManager.taskRepository.toggleTaskCompletion(task)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] updatedTask in
            self?.updateTaskInList(updatedTask)
            let status = updatedTask.isCompleted ? "已完成" : "未完成"
            self?.showSuccess("任务状态已更新为\(status)")
        }
    }
    
    // MARK: - UI操作方法
    
    /// 显示任务详情
    func showTaskDetails(_ task: Task) {
        selectedTask = task
        showTaskDetail = true
    }
    
    /// 显示任务编辑
    func showTaskEditor(_ task: Task) {
        selectedTask = task
        loadTaskToForm(task)
        showTaskEdit = true
    }
    
    /// 显示任务创建
    func showTaskCreator() {
        resetTaskForm()
        showTaskCreation = true
    }
    
    /// 确认删除任务
    func confirmDeleteTask(_ task: Task) {
        taskToDelete = task
        showDeleteConfirmation = true
    }
    
    /// 执行删除任务
    func executeDeleteTask() {
        guard let task = taskToDelete else { return }
        deleteTask(task)
        taskToDelete = nil
        showDeleteConfirmation = false
    }
    
    // MARK: - 表单管理方法
    
    /// 重置任务表单
    func resetTaskForm() {
        taskTitle = ""
        taskStartTime = Date()
        taskEndTime = Date().addingTimeInterval(3600)
        taskPriority = .medium
        taskTomatoCount = 1
        taskReminderEnabled = false
        selectedPlan = nil
    }
    
    /// 加载任务到表单
    private func loadTaskToForm(_ task: Task) {
        taskTitle = task.title ?? ""
        taskStartTime = task.startTime ?? Date()
        taskEndTime = task.endTime ?? Date()
        taskPriority = Priority(rawValue: task.priority ?? "medium") ?? .medium
        taskTomatoCount = Int(task.tomatoCount)
        taskReminderEnabled = task.isReminderEnabled
        selectedPlan = task.plan
    }
    
    /// 更新任务列表中的任务
    private func updateTaskInList(_ updatedTask: Task) {
        if let index = tasks.firstIndex(where: { $0.id == updatedTask.id }) {
            tasks[index] = updatedTask
        }
    }
    
    // MARK: - 筛选和排序方法
    
    /// 设置优先级筛选
    func setFilterPriority(_ priority: Priority?) {
        filterPriority = priority
    }
    
    /// 设置完成状态筛选
    func setFilterCompleted(_ completed: Bool?) {
        filterCompleted = completed
    }
    
    /// 设置排序选项
    func setSortOption(_ option: TaskSortOption, ascending: Bool = true) {
        sortOption = option
        sortAscending = ascending
    }
    
    /// 清除所有筛选
    func clearFilters() {
        filterPriority = nil
        filterCompleted = nil
    }
}

// 枚举定义已移至 Geda/Models/Enums.swift
