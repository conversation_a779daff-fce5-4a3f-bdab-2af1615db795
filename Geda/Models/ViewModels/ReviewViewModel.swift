import Foundation
import Combine
import SwiftUI

/// 数据复盘ViewModel - 管理统计分析和数据可视化
class ReviewViewModel: BaseViewModel {
    
    // MARK: - Published属性 (UI绑定)
    @Published var selectedTimeRange: TimeRange = .week
    @Published var startDate: Date = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
    @Published var endDate: Date = Date()
    @Published var currentUser: User?
    
    // MARK: - 统计数据
    @Published var totalFocusTime: TimeInterval = 0
    @Published var completedSessions: Int = 0
    @Published var completedTasks: Int = 0
    @Published var averageSessionDuration: TimeInterval = 0
    @Published var completionRate: Double = 0.0
    @Published var dailyFocusData: [DailyFocusData] = []
    @Published var taskCompletionData: [TaskCompletionData] = []
    @Published var productivityTrend: ProductivityTrend = .stable
    
    // MARK: - 详细数据
    @Published var pomodoroSessions: [PomodoroSession] = []
    @Published var tasks: [Task] = []
    @Published var plans: [Plan] = []
    @Published var achievements: [Achievement] = []
    
    // MARK: - 图表数据
    @Published var weeklyFocusChart: [ChartDataPoint] = []
    @Published var monthlyProductivityChart: [ChartDataPoint] = []
    @Published var taskPriorityDistribution: [PriorityDistribution] = []
    
    // MARK: - 计算属性
    var formattedTotalFocusTime: String {
        let hours = Int(totalFocusTime) / 3600
        let minutes = (Int(totalFocusTime) % 3600) / 60
        return "\(hours)小时\(minutes)分钟"
    }
    
    var formattedAverageSessionDuration: String {
        let minutes = Int(averageSessionDuration) / 60
        return "\(minutes)分钟"
    }
    
    var completionRatePercentage: String {
        return String(format: "%.1f%%", completionRate * 100)
    }
    
    var hasData: Bool {
        return !pomodoroSessions.isEmpty || !tasks.isEmpty
    }
    
    var bestFocusDay: DailyFocusData? {
        return dailyFocusData.max { $0.focusTime < $1.focusTime }
    }
    
    var currentStreak: Int {
        // 计算连续专注天数
        var streak = 0
        let calendar = Calendar.current
        var currentDate = calendar.startOfDay(for: Date())
        
        for data in dailyFocusData.reversed() {
            if calendar.isDate(data.date, inSameDayAs: currentDate) && data.focusTime > 0 {
                streak += 1
                currentDate = calendar.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
            } else {
                break
            }
        }
        
        return streak
    }
    
    // MARK: - 初始化
    override init(repositoryManager: RepositoryManager = RepositoryManager.shared) {
        super.init(repositoryManager: repositoryManager)
        loadCurrentUser()
        updateDateRange()
    }
    
    // MARK: - 生命周期方法
    override func onAppear() {
        refresh()
    }
    
    override func refresh() {
        loadStatisticsData()
        generateChartData()
        calculateAchievements()
    }
    
    // MARK: - 数据加载方法
    
    /// 加载当前用户
    private func loadCurrentUser() {
        performAsyncOperation(
            repositoryManager.userRepository.fetchCurrentUser()
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] user in
            self?.currentUser = user
            self?.refresh()
        }
    }
    
    /// 加载统计数据
    func loadStatisticsData() {
        loadPomodoroSessions()
        loadTasks()
        loadPlans()
    }
    
    /// 加载番茄钟会话数据
    private func loadPomodoroSessions() {
        let calendar = Calendar.current
        let sessions = pomodoroSessions.filter { session in
            guard let sessionDate = session.startTime else { return false }
            return sessionDate >= startDate && sessionDate <= endDate
        }
        
        // 计算统计数据
        let completedWorkSessions = sessions.filter { $0.isCompleted && $0.sessionType == "work" }
        completedSessions = completedWorkSessions.count
        totalFocusTime = completedWorkSessions.reduce(0) { $0 + $1.duration }
        averageSessionDuration = completedSessions > 0 ? totalFocusTime / Double(completedSessions) : 0
        
        // 生成每日专注数据
        generateDailyFocusData(from: completedWorkSessions)
    }
    
    /// 加载任务数据
    private func loadTasks() {
        let tasksInRange = tasks.filter { task in
            guard let taskDate = task.createdAt else { return false }
            return taskDate >= startDate && taskDate <= endDate
        }
        
        let completed = tasksInRange.filter { $0.isCompleted }
        completedTasks = completed.count
        completionRate = tasksInRange.isEmpty ? 0.0 : Double(completed.count) / Double(tasksInRange.count)
        
        // 生成任务完成数据
        generateTaskCompletionData(from: tasksInRange)
        generateTaskPriorityDistribution(from: tasksInRange)
    }
    
    /// 加载计划数据
    private func loadPlans() {
        // 计划相关统计可以在这里实现
    }
    
    // MARK: - 图表数据生成
    
    /// 生成每日专注数据
    private func generateDailyFocusData(from sessions: [PomodoroSession]) {
        let calendar = Calendar.current
        var dailyData: [Date: TimeInterval] = [:]
        
        // 初始化日期范围内的所有日期
        var currentDate = startDate
        while currentDate <= endDate {
            dailyData[calendar.startOfDay(for: currentDate)] = 0
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }
        
        // 累计每日专注时间
        for session in sessions {
            guard let sessionDate = session.startTime else { continue }
            let dayKey = calendar.startOfDay(for: sessionDate)
            dailyData[dayKey, default: 0] += session.duration
        }
        
        // 转换为数组并排序
        dailyFocusData = dailyData.map { date, focusTime in
            DailyFocusData(date: date, focusTime: focusTime)
        }.sorted { $0.date < $1.date }
    }
    
    /// 生成任务完成数据
    private func generateTaskCompletionData(from tasks: [Task]) {
        let calendar = Calendar.current
        var dailyData: [Date: (completed: Int, total: Int)] = [:]
        
        // 初始化日期范围
        var currentDate = startDate
        while currentDate <= endDate {
            dailyData[calendar.startOfDay(for: currentDate)] = (0, 0)
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }
        
        // 统计每日任务完成情况
        for task in tasks {
            guard let taskDate = task.createdAt else { continue }
            let dayKey = calendar.startOfDay(for: taskDate)
            var data = dailyData[dayKey] ?? (0, 0)
            data.total += 1
            if task.isCompleted {
                data.completed += 1
            }
            dailyData[dayKey] = data
        }
        
        // 转换为数组
        taskCompletionData = dailyData.map { date, data in
            TaskCompletionData(
                date: date,
                completedTasks: data.completed,
                totalTasks: data.total,
                completionRate: data.total > 0 ? Double(data.completed) / Double(data.total) : 0.0
            )
        }.sorted { $0.date < $1.date }
    }
    
    /// 生成任务优先级分布
    private func generateTaskPriorityDistribution(from tasks: [Task]) {
        let priorities = ["high", "medium", "low"]
        taskPriorityDistribution = priorities.map { priority in
            let count = tasks.filter { $0.priority == priority }.count
            return PriorityDistribution(
                priority: priority,
                count: count,
                percentage: tasks.isEmpty ? 0.0 : Double(count) / Double(tasks.count)
            )
        }
    }
    
    /// 生成图表数据
    func generateChartData() {
        // 生成周专注图表数据
        weeklyFocusChart = dailyFocusData.map { data in
            ChartDataPoint(
                date: data.date,
                value: data.focusTime / 3600, // 转换为小时
                label: DateFormatter.dayFormatter.string(from: data.date)
            )
        }
        
        // 生成月生产力图表数据
        generateMonthlyProductivityChart()
    }
    
    /// 生成月生产力图表
    private func generateMonthlyProductivityChart() {
        // 按周分组计算生产力指标
        let calendar = Calendar.current
        var weeklyData: [Date: (focusTime: TimeInterval, completedTasks: Int)] = [:]
        
        for data in dailyFocusData {
            let weekStart = calendar.dateInterval(of: .weekOfYear, for: data.date)?.start ?? data.date
            var weekData = weeklyData[weekStart] ?? (0, 0)
            weekData.focusTime += data.focusTime
            weeklyData[weekStart] = weekData
        }
        
        for data in taskCompletionData {
            let weekStart = calendar.dateInterval(of: .weekOfYear, for: data.date)?.start ?? data.date
            var weekData = weeklyData[weekStart] ?? (0, 0)
            weekData.completedTasks += data.completedTasks
            weeklyData[weekStart] = weekData
        }
        
        monthlyProductivityChart = weeklyData.map { date, data in
            // 生产力指标 = 专注时间(小时) + 完成任务数 * 0.5
            let productivityScore = (data.focusTime / 3600) + (Double(data.completedTasks) * 0.5)
            return ChartDataPoint(
                date: date,
                value: productivityScore,
                label: DateFormatter.weekFormatter.string(from: date)
            )
        }.sorted { $0.date < $1.date }
    }
    
    // MARK: - 成就计算
    
    /// 计算成就
    func calculateAchievements() {
        achievements = []
        
        // 专注时间成就
        if totalFocusTime >= 25 * 3600 { // 25小时
            achievements.append(Achievement(
                title: "专注大师",
                description: "累计专注时间超过25小时",
                icon: "clock.fill",
                isUnlocked: true
            ))
        }
        
        // 连续专注成就
        if currentStreak >= 7 {
            achievements.append(Achievement(
                title: "坚持不懈",
                description: "连续专注\(currentStreak)天",
                icon: "flame.fill",
                isUnlocked: true
            ))
        }
        
        // 任务完成成就
        if completedTasks >= 50 {
            achievements.append(Achievement(
                title: "任务达人",
                description: "完成\(completedTasks)个任务",
                icon: "checkmark.circle.fill",
                isUnlocked: true
            ))
        }
        
        // 完成率成就
        if completionRate >= 0.9 {
            achievements.append(Achievement(
                title: "完美主义者",
                description: "任务完成率达到\(completionRatePercentage)",
                icon: "star.fill",
                isUnlocked: true
            ))
        }
    }
    
    // MARK: - 时间范围管理
    
    /// 设置时间范围
    func setTimeRange(_ range: TimeRange) {
        selectedTimeRange = range
        updateDateRange()
        refresh()
    }
    
    /// 更新日期范围
    private func updateDateRange() {
        let calendar = Calendar.current
        endDate = Date()
        
        switch selectedTimeRange {
        case .week:
            startDate = calendar.date(byAdding: .day, value: -7, to: endDate) ?? endDate
        case .month:
            startDate = calendar.date(byAdding: .month, value: -1, to: endDate) ?? endDate
        case .quarter:
            startDate = calendar.date(byAdding: .month, value: -3, to: endDate) ?? endDate
        case .year:
            startDate = calendar.date(byAdding: .year, value: -1, to: endDate) ?? endDate
        case .custom:
            // 自定义范围由用户设置
            break
        }
    }
    
    /// 设置自定义日期范围
    func setCustomDateRange(start: Date, end: Date) {
        selectedTimeRange = .custom
        startDate = start
        endDate = end
        refresh()
    }
    
    /// 计算生产力趋势
    private func calculateProductivityTrend() {
        guard monthlyProductivityChart.count >= 2 else {
            productivityTrend = .stable
            return
        }
        
        let recent = monthlyProductivityChart.suffix(2)
        let current = recent.last?.value ?? 0
        let previous = recent.first?.value ?? 0
        
        let changePercentage = previous > 0 ? (current - previous) / previous : 0
        
        if changePercentage > 0.1 {
            productivityTrend = .increasing
        } else if changePercentage < -0.1 {
            productivityTrend = .decreasing
        } else {
            productivityTrend = .stable
        }
    }
}

// MARK: - 数据模型
struct DailyFocusData {
    let date: Date
    let focusTime: TimeInterval
}

struct TaskCompletionData {
    let date: Date
    let completedTasks: Int
    let totalTasks: Int
    let completionRate: Double
}

struct ChartDataPoint {
    let date: Date
    let value: Double
    let label: String
}

struct PriorityDistribution {
    let priority: String
    let count: Int
    let percentage: Double
}

struct Achievement {
    let title: String
    let description: String
    let icon: String
    let isUnlocked: Bool
}

// MARK: - 枚举定义
enum ProductivityTrend {
    case increasing
    case decreasing
    case stable

    var title: String {
        switch self {
        case .increasing: return "上升"
        case .decreasing: return "下降"
        case .stable: return "稳定"
        }
    }

    var color: Color {
        switch self {
        case .increasing: return .gedaSuccess
        case .decreasing: return .gedaError
        case .stable: return .gedaInfo
        }
    }
}

// TimeRange枚举已移至 Geda/Models/Enums.swift

// MARK: - DateFormatter扩展
extension DateFormatter {
    static let dayFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd"
        return formatter
    }()
    
    static let weekFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd"
        return formatter
    }()
}
