import Foundation
import Combine
import SwiftUI
import UserNotifications

/// 番茄钟计时器ViewModel - 管理专注计时业务逻辑
class FocusTimerViewModel: BaseViewModel {
    
    // MARK: - Published属性 (UI绑定)
    @Published var currentTask: Task?
    @Published var timerState: TimerState = .stopped
    @Published var sessionType: SessionType = .work
    @Published var timeRemaining: TimeInterval = 25 * 60 // 默认25分钟
    @Published var totalTime: TimeInterval = 25 * 60
    @Published var progress: Double = 0.0
    @Published var currentSession: PomodoroSession?
    @Published var completedSessions: [PomodoroSession] = []
    @Published var todaySessionsCount: Int = 0
    
    // MARK: - 设置属性
    @Published var workDuration: TimeInterval = 25 * 60 // 25分钟
    @Published var shortBreakDuration: TimeInterval = 5 * 60 // 5分钟
    @Published var longBreakDuration: TimeInterval = 15 * 60 // 15分钟
    @Published var longBreakInterval: Int = 4 // 每4个番茄钟后长休息
    @Published var autoStartBreaks: Bool = false
    @Published var autoStartWork: Bool = false
    @Published var enableNotifications: Bool = true
    
    // MARK: - 私有属性
    private var timer: Timer?
    private var sessionStartTime: Date?
    private var backgroundTaskID: UIBackgroundTaskIdentifier = .invalid
    private let notificationManager = NotificationManager.shared
    
    // MARK: - 计算属性
    var formattedTimeRemaining: String {
        let minutes = Int(timeRemaining) / 60
        let seconds = Int(timeRemaining) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    var timeString: String {
        return formattedTimeRemaining
    }
    
    var remainingTime: TimeInterval {
        return timeRemaining
    }
    
    var currentSessionIndex: Int {
        return todaySessionsCount
    }
    
    var totalSessions: Int {
        return 8  // 默认8个番茄钟
    }
    
    var completedSessionsCount: Int {
        return completedSessions.count
    }
    
    var sessionTypeText: String {
        return sessionType.title
    }
    
    var workDurationMinutes: Int {
        return Int(workDuration / 60)  // 转换为分钟
    }
    
    var breakDurationMinutes: Int {
        return Int(shortBreakDuration / 60)  // 转换为分钟
    }
    
    var isRunning: Bool {
        return timerState == .running
    }
    
    var isPaused: Bool {
        return timerState == .paused
    }
    
    var isStopped: Bool {
        return timerState == .stopped
    }
    
    var canStart: Bool {
        return timerState == .stopped || timerState == .paused
    }
    
    var canPause: Bool {
        return timerState == .running
    }
    
    var canReset: Bool {
        return timerState != .stopped
    }
    
    var sessionProgress: Double {
        guard totalTime > 0 else { return 0.0 }
        return 1.0 - (timeRemaining / totalTime)
    }
    
    // MARK: - 初始化
    override init(repositoryManager: RepositoryManager = RepositoryManager.shared) {
        super.init(repositoryManager: repositoryManager)
        setupNotificationObservers()
        loadTodaySessionsCount()
    }
    
    // MARK: - 生命周期方法
    override func onAppear() {
        loadTodaySessionsCount()
    }
    
    override func onDisappear() {
        // 保存当前状态
        if isRunning {
            pauseTimer()
        }
    }
    
    // MARK: - 计时器控制方法
    
    /// 开始计时器
    func startTimer() {
        guard canStart else { return }
        
        if timerState == .stopped {
            // 创建新的番茄钟会话
            createNewSession()
            sessionStartTime = Date()
            
            // 安排番茄钟完成通知
            if enableNotifications {
                notificationManager.schedulePomodoroCompletion(
                    sessionType: sessionType,
                    afterSeconds: timeRemaining
                )
            }
        }
        
        timerState = .running
        startBackgroundTask()
        
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateTimer()
        }
        
        showSuccess("计时器已开始")
    }
    
    /// 暂停计时器
    func pauseTimer() {
        guard canPause else { return }
        
        timerState = .paused
        timer?.invalidate()
        timer = nil
        endBackgroundTask()
        
        // 取消当前的番茄钟通知
        notificationManager.cancelAllPomodoroNotifications()
        
        showSuccess("计时器已暂停")
    }
    
    /// 重置计时器
    func resetTimer() {
        guard canReset else { return }
        
        timer?.invalidate()
        timer = nil
        timerState = .stopped
        timeRemaining = totalTime
        progress = 0.0
        sessionStartTime = nil
        endBackgroundTask()
        
        // 取消所有番茄钟通知
        notificationManager.cancelAllPomodoroNotifications()
        
        // 如果有未完成的会话，删除它
        if let session = currentSession, !session.isCompleted {
            deleteIncompleteSession(session)
        }
        currentSession = nil
        
        showSuccess("计时器已重置")
    }
    
    /// 跳过当前会话
    func skipSession() {
        completeCurrentSession()
        switchToNextSessionType()
        resetTimerForCurrentSession()
        
        showSuccess("已跳过当前会话")
    }
    
    /// 更新计时器
    private func updateTimer() {
        guard timeRemaining > 0 else {
            completeSession()
            return
        }
        
        timeRemaining -= 1
        progress = sessionProgress
    }
    
    /// 完成会话
    private func completeSession() {
        timer?.invalidate()
        timer = nil
        timerState = .stopped
        endBackgroundTask()
        
        completeCurrentSession()
        
        // 自动切换到下一个会话类型
        switchToNextSessionType()
        
        if shouldAutoStartNextSession() {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.startTimer()
            }
        } else {
            resetTimerForCurrentSession()
        }
        
        showSuccess("\(sessionType.title)完成！")
    }
    
    // MARK: - 会话管理方法
    
    /// 创建新的番茄钟会话
    private func createNewSession() {
        let context = CoreDataManager.shared.viewContext
        let session = PomodoroSession(context: context)
        session.id = UUID().uuidString
        session.sessionType = sessionType.rawValue
        session.duration = totalTime
        session.startTime = Date()
        session.isCompleted = false
        session.task = currentTask
        
        performAsyncOperation(
            repositoryManager.pomodoroSessionRepository.createSession(session)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] createdSession in
            self?.currentSession = createdSession
        }
    }
    
    /// 完成当前会话
    private func completeCurrentSession() {
        guard let session = currentSession else { return }
        
        performAsyncOperation(
            repositoryManager.pomodoroSessionRepository.completeSession(session)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] completedSession in
            self?.completedSessions.append(completedSession)
            self?.todaySessionsCount += 1
            self?.currentSession = nil
        }
    }
    
    /// 删除未完成的会话
    private func deleteIncompleteSession(_ session: PomodoroSession) {
        performAsyncOperation(
            repositoryManager.pomodoroSessionRepository.deleteSession(session)
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { _ in
            // 会话已删除
        }
    }
    
    /// 切换到下一个会话类型
    private func switchToNextSessionType() {
        switch sessionType {
        case .work:
            // 工作完成后，检查是否需要长休息
            if todaySessionsCount % longBreakInterval == 0 {
                sessionType = .longBreak
            } else {
                sessionType = .shortBreak
            }
        case .shortBreak, .longBreak:
            sessionType = .work
        }
    }
    
    /// 重置计时器为当前会话类型
    private func resetTimerForCurrentSession() {
        switch sessionType {
        case .work:
            totalTime = workDuration
        case .shortBreak:
            totalTime = shortBreakDuration
        case .longBreak:
            totalTime = longBreakDuration
        }
        timeRemaining = totalTime
        progress = 0.0
    }
    
    /// 是否应该自动开始下一个会话
    private func shouldAutoStartNextSession() -> Bool {
        switch sessionType {
        case .work:
            return autoStartWork
        case .shortBreak, .longBreak:
            return autoStartBreaks
        }
    }
    
    // MARK: - 设置方法
    
    /// 设置工作时长
    func setWorkDuration(_ duration: TimeInterval) {
        workDuration = duration
        if sessionType == .work && isStopped {
            totalTime = duration
            timeRemaining = duration
        }
    }
    
    /// 设置短休息时长
    func setShortBreakDuration(_ duration: TimeInterval) {
        shortBreakDuration = duration
        if sessionType == .shortBreak && isStopped {
            totalTime = duration
            timeRemaining = duration
        }
    }
    
    /// 设置长休息时长
    func setLongBreakDuration(_ duration: TimeInterval) {
        longBreakDuration = duration
        if sessionType == .longBreak && isStopped {
            totalTime = duration
            timeRemaining = duration
        }
    }
    
    /// 设置当前任务
    func setCurrentTask(_ task: Task?) {
        currentTask = task
    }
    
    /// 手动切换会话类型
    func switchSessionType(to type: SessionType) {
        guard isStopped else { return }
        
        sessionType = type
        resetTimerForCurrentSession()
    }
    
    /// 准备开始会话
    func prepareForSession() {
        resetTimerForCurrentSession()
    }
    
    /// 停止会话
    func stopSession() {
        resetTimer()
    }
    
    /// 重置会话
    func resetSession() {
        resetTimer()
    }
    
    /// 切换计时器状态
    func toggleTimer() {
        if isRunning {
            pauseTimer()
        } else {
            startTimer()
        }
    }
    
    /// 减少工作时间
    func decreaseWorkTime() {
        guard isStopped && workDuration > 5 * 60 else { return }
        setWorkDuration(workDuration - 5 * 60)
    }
    
    /// 增加工作时间
    func increaseWorkTime() {
        guard isStopped && workDuration < 60 * 60 else { return }
        setWorkDuration(workDuration + 5 * 60)
    }
    
    /// 减少休息时间
    func decreaseBreakTime() {
        guard isStopped && shortBreakDuration > 1 * 60 else { return }
        setShortBreakDuration(shortBreakDuration - 1 * 60)
    }
    
    /// 增加休息时间
    func increaseBreakTime() {
        guard isStopped && shortBreakDuration < 15 * 60 else { return }
        setShortBreakDuration(shortBreakDuration + 1 * 60)
    }
    
    // MARK: - 数据加载方法
    
    /// 加载今日会话数量
    private func loadTodaySessionsCount() {
        performAsyncOperation(
            repositoryManager.pomodoroSessionRepository.fetchSessionsForDate(Date())
                .mapError { $0 as Error }
                .eraseToAnyPublisher()
        ) { [weak self] sessions in
            self?.todaySessionsCount = sessions.filter { $0.isCompleted && $0.sessionType == "work" }.count
        }
    }
    
    // MARK: - 通知观察者设置
    
    /// 设置通知观察者
    private func setupNotificationObservers() {
        // 监听通知中心的动作
        NotificationCenter.default.addObserver(
            forName: .startBreakSession,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.startBreakSession()
        }
        
        NotificationCenter.default.addObserver(
            forName: .startWorkSession,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.startWorkSession()
        }
        
        NotificationCenter.default.addObserver(
            forName: .skipCurrentSession,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.skipSession()
        }
    }
    
    /// 开始休息会话
    private func startBreakSession() {
        if sessionType == .work {
            switchToNextSessionType()
            resetTimerForCurrentSession()
            startTimer()
        }
    }
    
    /// 开始工作会话
    private func startWorkSession() {
        if sessionType != .work {
            sessionType = .work
            resetTimerForCurrentSession()
            startTimer()
        }
    }
    
    // MARK: - 后台任务管理
    
    /// 开始后台任务
    private func startBackgroundTask() {
        backgroundTaskID = UIApplication.shared.beginBackgroundTask { [weak self] in
            self?.endBackgroundTask()
        }
    }
    
    /// 结束后台任务
    private func endBackgroundTask() {
        if backgroundTaskID != .invalid {
            UIApplication.shared.endBackgroundTask(backgroundTaskID)
            backgroundTaskID = .invalid
        }
    }
    
    deinit {
        timer?.invalidate()
        endBackgroundTask()
    }
}

