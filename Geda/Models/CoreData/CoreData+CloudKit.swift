//
//  CoreData+CloudKit.swift
//  Geda
//
//  Created by AI Assistant on 2025-07-22.
//

import Foundation
import CoreData
import CloudKit

// MARK: - User + CloudKitConvertible
extension User: CloudKitConvertible {
    
    func toCKRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: self.id ?? UUID().uuidString)
        let record = CKRecord(recordType: CloudKitSchema.RecordType.user.recordType, recordID: recordID)
        
        // 基本属性
        record[CloudKitSchema.UserFields.id.rawValue] = self.id
        record[CloudKitSchema.UserFields.name.rawValue] = self.name
        record[CloudKitSchema.UserFields.avatar.rawValue] = self.avatar
        record[CloudKitSchema.UserFields.checkInDays.rawValue] = Int(self.checkInDays)
        record[CloudKitSchema.UserFields.createdAt.rawValue] = self.createdAt
        record[CloudKitSchema.UserFields.updatedAt.rawValue] = self.updatedAt ?? Date()
        
        // 关联Apple ID（如果已认证）
        if let currentUser = AuthenticationService.shared.currentUser {
            record[CloudKitSchema.UserFields.appleUserID.rawValue] = currentUser.appleUserID
        }
        
        record[CloudKitSchema.UserFields.lastSyncDate.rawValue] = Date()
        
        return record
    }
    
    static func fromCKRecord(_ record: CKRecord) -> Self? {
        let context = CoreDataManager.shared.viewContext
        
        let user = User(context: context)
        
        // 基本属性
        user.id = record[CloudKitSchema.UserFields.id.rawValue] as? String ?? UUID().uuidString
        user.name = record[CloudKitSchema.UserFields.name.rawValue] as? String ?? ""
        user.avatar = record[CloudKitSchema.UserFields.avatar.rawValue] as? String
        user.checkInDays = Int32(record[CloudKitSchema.UserFields.checkInDays.rawValue] as? Int ?? 0)
        user.createdAt = record[CloudKitSchema.UserFields.createdAt.rawValue] as? Date ?? Date()
        user.updatedAt = record[CloudKitSchema.UserFields.updatedAt.rawValue] as? Date ?? Date()
        
        return user as? Self
    }
}

// MARK: - Plan + CloudKitConvertible
extension Plan: CloudKitConvertible {
    
    func toCKRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: self.id ?? UUID().uuidString)
        let record = CKRecord(recordType: CloudKitSchema.RecordType.plan.recordType, recordID: recordID)
        
        // 基本属性
        record[CloudKitSchema.PlanFields.id.rawValue] = self.id
        record[CloudKitSchema.PlanFields.title.rawValue] = self.title
        record[CloudKitSchema.PlanFields.planDescription.rawValue] = self.planDescription
        record[CloudKitSchema.PlanFields.status.rawValue] = self.status
        record[CloudKitSchema.PlanFields.progress.rawValue] = self.progress
        record[CloudKitSchema.PlanFields.startDate.rawValue] = self.startDate
        record[CloudKitSchema.PlanFields.endDate.rawValue] = self.endDate
        record[CloudKitSchema.PlanFields.totalTomatoes.rawValue] = Int(self.totalTomatoes)
        record[CloudKitSchema.PlanFields.createdAt.rawValue] = self.createdAt
        record[CloudKitSchema.PlanFields.updatedAt.rawValue] = self.updatedAt ?? Date()
        
        // 用户关联
        if let user = self.user, let userID = user.id {
            let userRecordID = CKRecord.ID(recordName: userID)
            record[CloudKitSchema.PlanFields.userReference.rawValue] = CKRecord.Reference(recordID: userRecordID, action: .deleteSelf)
        }
        
        return record
    }
    
    static func fromCKRecord(_ record: CKRecord) -> Self? {
        let context = CoreDataManager.shared.viewContext
        
        let plan = Plan(context: context)
        
        // 基本属性
        plan.id = record[CloudKitSchema.PlanFields.id.rawValue] as? String ?? UUID().uuidString
        plan.title = record[CloudKitSchema.PlanFields.title.rawValue] as? String ?? ""
        plan.planDescription = record[CloudKitSchema.PlanFields.planDescription.rawValue] as? String
        plan.status = record[CloudKitSchema.PlanFields.status.rawValue] as? String ?? "active"
        plan.progress = record[CloudKitSchema.PlanFields.progress.rawValue] as? Float ?? 0.0
        plan.startDate = record[CloudKitSchema.PlanFields.startDate.rawValue] as? Date ?? Date()
        plan.endDate = record[CloudKitSchema.PlanFields.endDate.rawValue] as? Date
        plan.totalTomatoes = Int32(record[CloudKitSchema.PlanFields.totalTomatoes.rawValue] as? Int ?? 0)
        plan.createdAt = record[CloudKitSchema.PlanFields.createdAt.rawValue] as? Date ?? Date()
        plan.updatedAt = record[CloudKitSchema.PlanFields.updatedAt.rawValue] as? Date ?? Date()
        
        // 处理用户关联（需要在同步完成后建立关系）
        
        return plan as? Self
    }
}

// MARK: - Task + CloudKitConvertible
extension Task: CloudKitConvertible {
    
    func toCKRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: self.id ?? UUID().uuidString)
        let record = CKRecord(recordType: CloudKitSchema.RecordType.task.recordType, recordID: recordID)
        
        // 基本属性
        record[CloudKitSchema.TaskFields.id.rawValue] = self.id
        record[CloudKitSchema.TaskFields.title.rawValue] = self.title
        record[CloudKitSchema.TaskFields.startTime.rawValue] = self.startTime
        record[CloudKitSchema.TaskFields.endTime.rawValue] = self.endTime
        record[CloudKitSchema.TaskFields.priority.rawValue] = self.priority
        record[CloudKitSchema.TaskFields.tomatoCount.rawValue] = Int(self.tomatoCount)
        record[CloudKitSchema.TaskFields.isCompleted.rawValue] = self.isCompleted
        record[CloudKitSchema.TaskFields.isReminderEnabled.rawValue] = self.isReminderEnabled
        record[CloudKitSchema.TaskFields.createdAt.rawValue] = self.createdAt
        record[CloudKitSchema.TaskFields.updatedAt.rawValue] = self.updatedAt ?? Date()
        
        // 计划关联
        if let plan = self.plan, let planID = plan.id {
            let planRecordID = CKRecord.ID(recordName: planID)
            record[CloudKitSchema.TaskFields.planReference.rawValue] = CKRecord.Reference(recordID: planRecordID, action: .deleteSelf)
        }
        
        return record
    }
    
    static func fromCKRecord(_ record: CKRecord) -> Self? {
        let context = CoreDataManager.shared.viewContext
        
        let task = Task(context: context)
        
        // 基本属性
        task.id = record[CloudKitSchema.TaskFields.id.rawValue] as? String ?? UUID().uuidString
        task.title = record[CloudKitSchema.TaskFields.title.rawValue] as? String ?? ""
        task.startTime = record[CloudKitSchema.TaskFields.startTime.rawValue] as? Date ?? Date()
        task.endTime = record[CloudKitSchema.TaskFields.endTime.rawValue] as? Date ?? Date()
        task.priority = record[CloudKitSchema.TaskFields.priority.rawValue] as? String ?? "medium"
        task.tomatoCount = Int32(record[CloudKitSchema.TaskFields.tomatoCount.rawValue] as? Int ?? 1)
        task.isCompleted = record[CloudKitSchema.TaskFields.isCompleted.rawValue] as? Bool ?? false
        task.isReminderEnabled = record[CloudKitSchema.TaskFields.isReminderEnabled.rawValue] as? Bool ?? false
        task.createdAt = record[CloudKitSchema.TaskFields.createdAt.rawValue] as? Date ?? Date()
        task.updatedAt = record[CloudKitSchema.TaskFields.updatedAt.rawValue] as? Date ?? Date()
        
        return task as? Self
    }
}

// MARK: - Subtask + CloudKitConvertible
extension Subtask: CloudKitConvertible {
    
    func toCKRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: self.id ?? UUID().uuidString)
        let record = CKRecord(recordType: CloudKitSchema.RecordType.subtask.recordType, recordID: recordID)
        
        // 基本属性
        record[CloudKitSchema.SubtaskFields.id.rawValue] = self.id
        record[CloudKitSchema.SubtaskFields.title.rawValue] = self.title
        record[CloudKitSchema.SubtaskFields.isCompleted.rawValue] = self.isCompleted
        record[CloudKitSchema.SubtaskFields.createdAt.rawValue] = self.createdAt
        record[CloudKitSchema.SubtaskFields.updatedAt.rawValue] = self.updatedAt ?? Date()
        
        // 任务关联
        if let task = self.task, let taskID = task.id {
            let taskRecordID = CKRecord.ID(recordName: taskID)
            record[CloudKitSchema.SubtaskFields.taskReference.rawValue] = CKRecord.Reference(recordID: taskRecordID, action: .deleteSelf)
        }
        
        return record
    }
    
    static func fromCKRecord(_ record: CKRecord) -> Self? {
        let context = CoreDataManager.shared.viewContext
        
        let subtask = Subtask(context: context)
        
        // 基本属性
        subtask.id = record[CloudKitSchema.SubtaskFields.id.rawValue] as? String ?? UUID().uuidString
        subtask.title = record[CloudKitSchema.SubtaskFields.title.rawValue] as? String ?? ""
        subtask.isCompleted = record[CloudKitSchema.SubtaskFields.isCompleted.rawValue] as? Bool ?? false
        subtask.createdAt = record[CloudKitSchema.SubtaskFields.createdAt.rawValue] as? Date ?? Date()
        subtask.updatedAt = record[CloudKitSchema.SubtaskFields.updatedAt.rawValue] as? Date ?? Date()
        
        return subtask as? Self
    }
}

// MARK: - PomodoroSession + CloudKitConvertible
extension PomodoroSession: CloudKitConvertible {
    
    func toCKRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: self.id ?? UUID().uuidString)
        let record = CKRecord(recordType: CloudKitSchema.RecordType.pomodoroSession.recordType, recordID: recordID)
        
        // 基本属性
        record[CloudKitSchema.PomodoroSessionFields.id.rawValue] = self.id
        record[CloudKitSchema.PomodoroSessionFields.sessionType.rawValue] = self.sessionType
        record[CloudKitSchema.PomodoroSessionFields.startTime.rawValue] = self.startTime
        record[CloudKitSchema.PomodoroSessionFields.endTime.rawValue] = self.endTime
        record[CloudKitSchema.PomodoroSessionFields.duration.rawValue] = self.duration
        record[CloudKitSchema.PomodoroSessionFields.isCompleted.rawValue] = self.isCompleted
        record[CloudKitSchema.PomodoroSessionFields.createdAt.rawValue] = self.createdAt
        
        // 任务关联
        if let task = self.task, let taskID = task.id {
            let taskRecordID = CKRecord.ID(recordName: taskID)
            record[CloudKitSchema.PomodoroSessionFields.taskReference.rawValue] = CKRecord.Reference(recordID: taskRecordID, action: .deleteSelf)
        }
        
        return record
    }
    
    static func fromCKRecord(_ record: CKRecord) -> Self? {
        let context = CoreDataManager.shared.viewContext
        
        let session = PomodoroSession(context: context)
        
        // 基本属性
        session.id = record[CloudKitSchema.PomodoroSessionFields.id.rawValue] as? String ?? UUID().uuidString
        session.sessionType = record[CloudKitSchema.PomodoroSessionFields.sessionType.rawValue] as? String ?? "work"
        session.startTime = record[CloudKitSchema.PomodoroSessionFields.startTime.rawValue] as? Date ?? Date()
        session.endTime = record[CloudKitSchema.PomodoroSessionFields.endTime.rawValue] as? Date
        session.duration = record[CloudKitSchema.PomodoroSessionFields.duration.rawValue] as? Double ?? 0.0
        session.isCompleted = record[CloudKitSchema.PomodoroSessionFields.isCompleted.rawValue] as? Bool ?? false
        session.createdAt = record[CloudKitSchema.PomodoroSessionFields.createdAt.rawValue] as? Date ?? Date()
        
        return session as? Self
    }
}

// MARK: - 数据同步帮助方法
extension NSManagedObject {
    
    /// 获取CloudKit记录ID
    var cloudKitRecordID: CKRecord.ID? {
        guard let entityID = self.value(forKey: "id") as? String else { return nil }
        return CKRecord.ID(recordName: entityID)
    }
    
    /// 设置CloudKit同步标记
    func markAsSynced() {
        // 可以添加同步状态字段到Core Data模型中
        // 这里暂时使用UserDefaults作为示例
        if let entityID = self.value(forKey: "id") as? String {
            UserDefaults.standard.set(Date(), forKey: "sync_\(entityID)")
        }
    }
    
    /// 检查是否需要同步
    func needsSync() -> Bool {
        guard let entityID = self.value(forKey: "id") as? String,
              let updatedAt = self.value(forKey: "updatedAt") as? Date else {
            return true
        }
        
        let lastSyncDate = UserDefaults.standard.object(forKey: "sync_\(entityID)") as? Date ?? Date.distantPast
        return updatedAt > lastSyncDate
    }
}