<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22758" systemVersion="23G93" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Plan" representedClassName="Plan" syncable="YES" codeGenerationType="class">
        <attribute name="createdAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="endDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="planDescription" optional="YES" attributeType="String"/>
        <attribute name="progress" attributeType="Float" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="startDate" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="status" attributeType="String" defaultValueString="active"/>
        <attribute name="title" attributeType="String"/>
        <attribute name="totalTomatoes" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="tasks" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Task" inverseName="plan" inverseEntity="Task"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="plans" inverseEntity="User"/>
    </entity>
    <entity name="PomodoroSession" representedClassName="PomodoroSession" syncable="YES" codeGenerationType="class">
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="duration" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="endTime" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="isCompleted" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="sessionType" attributeType="String" defaultValueString="work"/>
        <attribute name="startTime" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="task" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Task" inverseName="pomodoroSessions" inverseEntity="Task"/>
    </entity>
    <entity name="Subtask" representedClassName="Subtask" syncable="YES" codeGenerationType="class">
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="isCompleted" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="title" attributeType="String"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="task" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Task" inverseName="subtasks" inverseEntity="Task"/>
    </entity>
    <entity name="Task" representedClassName="Task" syncable="YES" codeGenerationType="class">
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="endTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="isCompleted" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="isReminderEnabled" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
        <attribute name="priority" attributeType="String" defaultValueString="medium"/>
        <attribute name="startTime" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="title" attributeType="String"/>
        <attribute name="tomatoCount" attributeType="Integer 32" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="plan" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Plan" inverseName="tasks" inverseEntity="Plan"/>
        <relationship name="pomodoroSessions" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="PomodoroSession" inverseName="task" inverseEntity="PomodoroSession"/>
        <relationship name="subtasks" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Subtask" inverseName="task" inverseEntity="Subtask"/>
    </entity>
    <entity name="User" representedClassName="User" syncable="YES" codeGenerationType="class">
        <attribute name="avatar" optional="YES" attributeType="String"/>
        <attribute name="checkInDays" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <relationship name="plans" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Plan" inverseName="user" inverseEntity="Plan"/>
    </entity>
</model>
