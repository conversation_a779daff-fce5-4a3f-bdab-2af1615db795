import Foundation
import CoreData
import Combine
import CloudKit

/// Core Data管理器 - 负责数据持久化操作，支持CloudKit同步
class CoreDataManager: ObservableObject {
    
    // MARK: - 单例
    static let shared = CoreDataManager()
    
    // MARK: - CloudKit支持
    @Published var cloudKitAvailable = false
    @Published var syncInProgress = false
    
    // MARK: - Core Data栈
    lazy var persistentContainer: NSPersistentContainer = {
        // 创建支持CloudKit的容器
        let container = NSPersistentCloudKitContainer(name: "<PERSON><PERSON>")
        
        // 配置存储描述
        guard let storeDescription = container.persistentStoreDescriptions.first else {
            fatalError("无法获取存储描述")
        }
        
        // CloudKit配置
        storeDescription.shouldInferMappingModelAutomatically = true
        storeDescription.shouldMigrateStoreAutomatically = true
        
        // 启用CloudKit支持
        storeDescription.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
        storeDescription.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)
        
        // 设置CloudKit容器选项
        if AuthenticationService.shared.isAuthenticated {
            let cloudKitOptions = NSPersistentCloudKitContainerOptions(containerIdentifier: CloudKitConfiguration.containerIdentifier)
            storeDescription.cloudKitContainerOptions = cloudKitOptions
        }
        
        container.loadPersistentStores { [weak self] _, error in
            if let error = error as NSError? {
                print("Core Data加载失败: \(error), \(error.userInfo)")
                // 在生产环境中，应该有更好的错误处理策略
                self?.handleCoreDataError(error)
            } else {
                print("Core Data加载成功")
                self?.cloudKitAvailable = true
            }
        }
        
        // 配置上下文
        container.viewContext.automaticallyMergesChangesFromParent = true
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
        
        return container
    }()
    
    // MARK: - 上下文
    var viewContext: NSManagedObjectContext {
        return persistentContainer.viewContext
    }
    
    var backgroundContext: NSManagedObjectContext {
        return persistentContainer.newBackgroundContext()
    }
    
    // MARK: - 私有初始化
    private init() {
        setupRemoteChangeNotifications()
    }
    
    // MARK: - CloudKit相关方法
    
    /// 处理Core Data错误
    private func handleCoreDataError(_ error: NSError) {
        if error.domain == CKErrorDomain {
            handleCloudKitError(error)
        } else {
            // 其他Core Data错误处理
            print("Core Data错误: \(error.localizedDescription)")
        }
    }
    
    /// 处理CloudKit错误
    private func handleCloudKitError(_ error: NSError) {
        switch error.code {
        case CKError.notAuthenticated.rawValue:
            print("CloudKit: 用户未认证")
            cloudKitAvailable = false
        case CKError.networkUnavailable.rawValue:
            print("CloudKit: 网络不可用")
        case CKError.quotaExceeded.rawValue:
            print("CloudKit: 存储配额已满")
        default:
            print("CloudKit错误: \(error.localizedDescription)")
        }
    }
    
    /// 设置远程变更通知
    private func setupRemoteChangeNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleRemoteStoreChange),
            name: .NSPersistentStoreRemoteChange,
            object: nil
        )
    }
    
    /// 处理远程存储变更
    @objc private func handleRemoteStoreChange(_ notification: Notification) {
        print("CloudKit: 检测到远程数据变更")
        
        // 在主队列上合并变更
        DispatchQueue.main.async { [weak self] in
            self?.processRemoteChanges()
        }
    }
    
    /// 处理远程变更
    private func processRemoteChanges() {
        syncInProgress = true
        
        // 创建后台上下文来处理变更
        let backgroundContext = persistentContainer.newBackgroundContext()
        
        backgroundContext.perform { [weak self] in
            // 执行远程变更的合并逻辑
            do {
                try backgroundContext.save()
                
                DispatchQueue.main.async {
                    self?.syncInProgress = false
                    print("CloudKit: 远程变更合并完成")
                }
            } catch {
                DispatchQueue.main.async {
                    self?.syncInProgress = false
                    print("CloudKit: 远程变更合并失败 - \(error.localizedDescription)")
                }
            }
        }
    }
    
    /// 启用CloudKit同步
    func enableCloudKitSync() {
        guard let storeDescription = persistentContainer.persistentStoreDescriptions.first else {
            return
        }
        
        let cloudKitOptions = NSPersistentCloudKitContainerOptions(containerIdentifier: CloudKitConfiguration.containerIdentifier)
        storeDescription.cloudKitContainerOptions = cloudKitOptions
        
        // 重新加载存储
        persistentContainer.loadPersistentStores { [weak self] _, error in
            if let error = error {
                print("启用CloudKit同步失败: \(error.localizedDescription)")
                self?.cloudKitAvailable = false
            } else {
                print("CloudKit同步已启用")
                self?.cloudKitAvailable = true
            }
        }
    }
    
    /// 禁用CloudKit同步
    func disableCloudKitSync() {
        guard let storeDescription = persistentContainer.persistentStoreDescriptions.first else {
            return
        }
        
        storeDescription.cloudKitContainerOptions = nil
        cloudKitAvailable = false
        print("CloudKit同步已禁用")
    }
    
    /// 获取CloudKit容器
    var cloudKitContainer: CKContainer? {
        guard cloudKitAvailable else { return nil }
        return CKContainer(identifier: CloudKitConfiguration.containerIdentifier)
    }
    
    // MARK: - 保存操作
    
    /// 保存主上下文
    func save() {
        let context = persistentContainer.viewContext
        
        if context.hasChanges {
            do {
                try context.save()
            } catch {
                let nsError = error as NSError
                print("Core Data保存失败: \(nsError), \(nsError.userInfo)")
            }
        }
    }
    
    /// 保存后台上下文
    func saveBackground(_ context: NSManagedObjectContext) {
        if context.hasChanges {
            do {
                try context.save()
            } catch {
                let nsError = error as NSError
                print("Core Data后台保存失败: \(nsError), \(nsError.userInfo)")
            }
        }
    }
    
    // MARK: - 批量操作
    
    /// 执行后台批量操作
    func performBackgroundTask<T>(_ block: @escaping (NSManagedObjectContext) throws -> T) -> AnyPublisher<T, Error> {
        return Future<T, Error> { promise in
            let context = self.backgroundContext
            context.perform {
                do {
                    let result = try block(context)
                    self.saveBackground(context)
                    promise(.success(result))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - 数据清理
    
    /// 删除所有数据
    func deleteAllData() {
        let entities = ["User", "Plan", "Task", "Subtask", "PomodoroSession"]
        
        for entityName in entities {
            let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: entityName)
            let deleteRequest = NSBatchDeleteRequest(fetchRequest: fetchRequest)
            
            do {
                try viewContext.execute(deleteRequest)
            } catch {
                print("删除\(entityName)数据失败: \(error)")
            }
        }
        
        save()
    }
}

// MARK: - 错误定义
enum CoreDataError: LocalizedError {
    case saveError(Error)
    case fetchError(Error)
    case deleteError(Error)
    case entityNotFound(String)
    
    var errorDescription: String? {
        switch self {
        case .saveError(let error):
            return "数据保存失败: \(error.localizedDescription)"
        case .fetchError(let error):
            return "数据获取失败: \(error.localizedDescription)"
        case .deleteError(let error):
            return "数据删除失败: \(error.localizedDescription)"
        case .entityNotFound(let entityName):
            return "未找到实体: \(entityName)"
        }
    }
}
