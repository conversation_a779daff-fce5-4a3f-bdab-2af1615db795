import Foundation
import Combine
import CoreData

/// Task数据访问层实现
class TaskRepository: TaskRepositoryProtocol {
    
    // MARK: - 属性
    private let coreDataManager: CoreDataManager
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    init(coreDataManager: CoreDataManager) {
        self.coreDataManager = coreDataManager
    }
    
    // MARK: - TaskRepositoryProtocol实现
    
    /// 获取计划的所有任务
    func fetchTasks(for plan: Plan) -> AnyPublisher<[Task], RepositoryError> {
        return Future<[Task], RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<Task> = Task.fetchRequest()
            request.predicate = NSPredicate(format: "plan == %@", plan)
            request.sortDescriptors = [NSSortDescriptor(key: "startTime", ascending: true)]
            
            do {
                let tasks = try context.fetch(request)
                promise(.success(tasks))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 根据ID获取任务
    func fetchTask(by id: String) -> AnyPublisher<Task?, RepositoryError> {
        return Future<Task?, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<Task> = Task.fetchRequest()
            request.predicate = NSPredicate(format: "id == %@", id)
            request.fetchLimit = 1
            
            do {
                let tasks = try context.fetch(request)
                promise(.success(tasks.first))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 获取指定日期的任务
    func fetchTasksForDate(_ date: Date) -> AnyPublisher<[Task], RepositoryError> {
        return Future<[Task], RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let calendar = Calendar.current
            let startOfDay = calendar.startOfDay(for: date)
            let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
            
            let request: NSFetchRequest<Task> = Task.fetchRequest()
            request.predicate = NSPredicate(format: "startTime >= %@ AND startTime < %@", startOfDay as NSDate, endOfDay as NSDate)
            request.sortDescriptors = [NSSortDescriptor(key: "startTime", ascending: true)]
            
            do {
                let tasks = try context.fetch(request)
                promise(.success(tasks))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 获取今日任务
    func fetchTodayTasks() -> AnyPublisher<[Task], RepositoryError> {
        return fetchTasksForDate(Date())
    }
    
    /// 获取即将到来的任务
    func fetchUpcomingTasks() -> AnyPublisher<[Task], RepositoryError> {
        return Future<[Task], RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let now = Date()
            
            let request: NSFetchRequest<Task> = Task.fetchRequest()
            request.predicate = NSPredicate(format: "startTime > %@ AND isCompleted == NO", now as NSDate)
            request.sortDescriptors = [NSSortDescriptor(key: "startTime", ascending: true)]
            request.fetchLimit = 10
            
            do {
                let tasks = try context.fetch(request)
                promise(.success(tasks))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 获取计划的已完成任务
    func fetchCompletedTasks(for plan: Plan) -> AnyPublisher<[Task], RepositoryError> {
        return Future<[Task], RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<Task> = Task.fetchRequest()
            request.predicate = NSPredicate(format: "plan == %@ AND isCompleted == YES", plan)
            request.sortDescriptors = [NSSortDescriptor(key: "updatedAt", ascending: false)]
            
            do {
                let tasks = try context.fetch(request)
                promise(.success(tasks))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 创建新任务
    func createTask(_ task: Task) -> AnyPublisher<Task, RepositoryError> {
        return Future<Task, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            // 验证任务数据
            guard !task.title!.isEmpty else {
                promise(.failure(.validationError("任务标题不能为空")))
                return
            }
            
            guard task.startTime! < task.endTime! else {
                promise(.failure(.validationError("开始时间必须早于结束时间")))
                return
            }
            
            // 设置默认值
            task.createdAt = Date()
            task.updatedAt = Date()
            task.isCompleted = false
            task.isReminderEnabled = false
            
            if task.priority == nil {
                task.priority = "medium"
            }
            
            if task.tomatoCount == 0 {
                task.tomatoCount = 1
            }
            
            do {
                try context.save()
                promise(.success(task))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 更新任务
    func updateTask(_ task: Task) -> AnyPublisher<Task, RepositoryError> {
        return Future<Task, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            // 验证任务数据
            guard !task.title!.isEmpty else {
                promise(.failure(.validationError("任务标题不能为空")))
                return
            }
            
            guard task.startTime! < task.endTime! else {
                promise(.failure(.validationError("开始时间必须早于结束时间")))
                return
            }
            
            task.updatedAt = Date()
            
            do {
                try context.save()
                promise(.success(task))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 删除任务
    func deleteTask(_ task: Task) -> AnyPublisher<Void, RepositoryError> {
        return Future<Void, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            context.delete(task)
            
            do {
                try context.save()
                promise(.success(()))
            } catch {
                promise(.failure(.deleteError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 切换任务完成状态
    func toggleTaskCompletion(_ task: Task) -> AnyPublisher<Task, RepositoryError> {
        return Future<Task, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            task.isCompleted.toggle()
            task.updatedAt = Date()
            
            do {
                try context.save()
                promise(.success(task))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
}
