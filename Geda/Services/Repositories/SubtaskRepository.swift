import Foundation
import Combine
import CoreData

/// Subtask数据访问层实现
class SubtaskRepository: SubtaskRepositoryProtocol {
    
    // MARK: - 属性
    private let coreDataManager: CoreDataManager
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    init(coreDataManager: CoreDataManager) {
        self.coreDataManager = coreDataManager
    }
    
    // MARK: - SubtaskRepositoryProtocol实现
    
    /// 获取任务的所有子任务
    func fetchSubtasks(for task: Task) -> AnyPublisher<[Subtask], RepositoryError> {
        return Future<[Subtask], RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<Subtask> = Subtask.fetchRequest()
            request.predicate = NSPredicate(format: "task == %@", task)
            request.sortDescriptors = [NSSortDescriptor(key: "createdAt", ascending: true)]
            
            do {
                let subtasks = try context.fetch(request)
                promise(.success(subtasks))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 根据ID获取子任务
    func fetchSubtask(by id: String) -> AnyPublisher<Subtask?, RepositoryError> {
        return Future<Subtask?, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<Subtask> = Subtask.fetchRequest()
            request.predicate = NSPredicate(format: "id == %@", id)
            request.fetchLimit = 1
            
            do {
                let subtasks = try context.fetch(request)
                promise(.success(subtasks.first))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 创建新子任务
    func createSubtask(_ subtask: Subtask) -> AnyPublisher<Subtask, RepositoryError> {
        return Future<Subtask, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            // 验证子任务数据
            guard !subtask.title!.isEmpty else {
                promise(.failure(.validationError("子任务标题不能为空")))
                return
            }
            
            // 设置默认值
            subtask.createdAt = Date()
            subtask.updatedAt = Date()
            subtask.isCompleted = false
            
            do {
                try context.save()
                promise(.success(subtask))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 更新子任务
    func updateSubtask(_ subtask: Subtask) -> AnyPublisher<Subtask, RepositoryError> {
        return Future<Subtask, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            // 验证子任务数据
            guard !subtask.title!.isEmpty else {
                promise(.failure(.validationError("子任务标题不能为空")))
                return
            }
            
            subtask.updatedAt = Date()
            
            do {
                try context.save()
                promise(.success(subtask))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 删除子任务
    func deleteSubtask(_ subtask: Subtask) -> AnyPublisher<Void, RepositoryError> {
        return Future<Void, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            context.delete(subtask)
            
            do {
                try context.save()
                promise(.success(()))
            } catch {
                promise(.failure(.deleteError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 切换子任务完成状态
    func toggleSubtaskCompletion(_ subtask: Subtask) -> AnyPublisher<Subtask, RepositoryError> {
        return Future<Subtask, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            subtask.isCompleted.toggle()
            subtask.updatedAt = Date()
            
            do {
                try context.save()
                promise(.success(subtask))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
}
