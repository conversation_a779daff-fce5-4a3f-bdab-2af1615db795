import Foundation
import Combine
import CoreData

/// PomodoroSession数据访问层实现
class PomodoroSessionRepository: PomodoroSessionRepositoryProtocol {
    
    // MARK: - 属性
    private let coreDataManager: CoreDataManager
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    init(coreDataManager: CoreDataManager) {
        self.coreDataManager = coreDataManager
    }
    
    // MARK: - PomodoroSessionRepositoryProtocol实现
    
    /// 获取任务的所有番茄钟会话
    func fetchSessions(for task: Task) -> AnyPublisher<[PomodoroSession], RepositoryError> {
        return Future<[PomodoroSession], RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<PomodoroSession> = PomodoroSession.fetchRequest()
            request.predicate = NSPredicate(format: "task == %@", task)
            request.sortDescriptors = [NSSortDescriptor(key: "startTime", ascending: true)]
            
            do {
                let sessions = try context.fetch(request)
                promise(.success(sessions))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 根据ID获取番茄钟会话
    func fetchSession(by id: String) -> AnyPublisher<PomodoroSession?, RepositoryError> {
        return Future<PomodoroSession?, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<PomodoroSession> = PomodoroSession.fetchRequest()
            request.predicate = NSPredicate(format: "id == %@", id)
            request.fetchLimit = 1
            
            do {
                let sessions = try context.fetch(request)
                promise(.success(sessions.first))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 获取指定日期的番茄钟会话
    func fetchSessionsForDate(_ date: Date) -> AnyPublisher<[PomodoroSession], RepositoryError> {
        return Future<[PomodoroSession], RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let calendar = Calendar.current
            let startOfDay = calendar.startOfDay(for: date)
            let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
            
            let request: NSFetchRequest<PomodoroSession> = PomodoroSession.fetchRequest()
            request.predicate = NSPredicate(format: "startTime >= %@ AND startTime < %@", startOfDay as NSDate, endOfDay as NSDate)
            request.sortDescriptors = [NSSortDescriptor(key: "startTime", ascending: true)]
            
            do {
                let sessions = try context.fetch(request)
                promise(.success(sessions))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 获取任务的已完成番茄钟会话
    func fetchCompletedSessions(for task: Task) -> AnyPublisher<[PomodoroSession], RepositoryError> {
        return Future<[PomodoroSession], RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<PomodoroSession> = PomodoroSession.fetchRequest()
            request.predicate = NSPredicate(format: "task == %@ AND isCompleted == YES", task)
            request.sortDescriptors = [NSSortDescriptor(key: "startTime", ascending: true)]
            
            do {
                let sessions = try context.fetch(request)
                promise(.success(sessions))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 创建新番茄钟会话
    func createSession(_ session: PomodoroSession) -> AnyPublisher<PomodoroSession, RepositoryError> {
        return Future<PomodoroSession, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            // 验证会话数据
            guard session.duration > 0 else {
                promise(.failure(.validationError("会话时长必须大于0")))
                return
            }
            
            // 设置默认值
            session.createdAt = Date()
            session.isCompleted = false
            
            if session.sessionType == nil {
                session.sessionType = "work"
            }
            
            if session.startTime == nil {
                session.startTime = Date()
            }
            
            do {
                try context.save()
                promise(.success(session))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 更新番茄钟会话
    func updateSession(_ session: PomodoroSession) -> AnyPublisher<PomodoroSession, RepositoryError> {
        return Future<PomodoroSession, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            // 验证会话数据
            guard session.duration > 0 else {
                promise(.failure(.validationError("会话时长必须大于0")))
                return
            }
            
            do {
                try context.save()
                promise(.success(session))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 删除番茄钟会话
    func deleteSession(_ session: PomodoroSession) -> AnyPublisher<Void, RepositoryError> {
        return Future<Void, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            context.delete(session)
            
            do {
                try context.save()
                promise(.success(()))
            } catch {
                promise(.failure(.deleteError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 完成番茄钟会话
    func completeSession(_ session: PomodoroSession) -> AnyPublisher<PomodoroSession, RepositoryError> {
        return Future<PomodoroSession, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            session.isCompleted = true
            session.endTime = Date()
            
            do {
                try context.save()
                promise(.success(session))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
}
