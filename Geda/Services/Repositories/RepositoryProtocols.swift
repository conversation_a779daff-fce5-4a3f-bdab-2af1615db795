import Foundation
import Combine
import CoreData

// MARK: - Repository错误类型
enum RepositoryError: LocalizedError {
    case entityNotFound(String)
    case saveError(Error)
    case fetchError(Error)
    case deleteError(Error)
    case validationError(String)
    case coreDataError(Error)
    case unknownError(Error)
    
    var errorDescription: String? {
        switch self {
        case .entityNotFound(let entityName):
            return "未找到\(entityName)实体"
        case .saveError(let error):
            return "保存失败: \(error.localizedDescription)"
        case .fetchError(let error):
            return "获取数据失败: \(error.localizedDescription)"
        case .deleteError(let error):
            return "删除失败: \(error.localizedDescription)"
        case .validationError(let message):
            return "数据验证失败: \(message)"
        case .coreDataError(let error):
            return "Core Data错误: \(error.localizedDescription)"
        case .unknownError(let error):
            return "未知错误: \(error.localizedDescription)"
        }
    }
}

// MARK: - User Repository协议
protocol UserRepositoryProtocol {
    func fetchUser(by id: String) -> AnyPublisher<User?, RepositoryError>
    func fetchCurrentUser() -> AnyPublisher<User?, RepositoryError>
    func createUser(_ user: User) -> AnyPublisher<User, RepositoryError>
    func updateUser(_ user: User) -> AnyPublisher<User, RepositoryError>
    func deleteUser(_ user: User) -> AnyPublisher<Void, RepositoryError>
    func incrementCheckInDays(for user: User) -> AnyPublisher<User, RepositoryError>
}

// MARK: - Plan Repository协议
protocol PlanRepositoryProtocol {
    func fetchPlans(for user: User) -> AnyPublisher<[Plan], RepositoryError>
    func fetchPlan(by id: String) -> AnyPublisher<Plan?, RepositoryError>
    func fetchActivePlans(for user: User) -> AnyPublisher<[Plan], RepositoryError>
    func fetchCompletedPlans(for user: User) -> AnyPublisher<[Plan], RepositoryError>
    func fetchPausedPlans(for user: User) -> AnyPublisher<[Plan], RepositoryError>
    func createPlan(_ plan: Plan) -> AnyPublisher<Plan, RepositoryError>
    func updatePlan(_ plan: Plan) -> AnyPublisher<Plan, RepositoryError>
    func deletePlan(_ plan: Plan) -> AnyPublisher<Void, RepositoryError>
    func updatePlanProgress(_ plan: Plan) -> AnyPublisher<Plan, RepositoryError>
}

// MARK: - Task Repository协议
protocol TaskRepositoryProtocol {
    func fetchTasks(for plan: Plan) -> AnyPublisher<[Task], RepositoryError>
    func fetchTask(by id: String) -> AnyPublisher<Task?, RepositoryError>
    func fetchTasksForDate(_ date: Date) -> AnyPublisher<[Task], RepositoryError>
    func fetchTodayTasks() -> AnyPublisher<[Task], RepositoryError>
    func fetchUpcomingTasks() -> AnyPublisher<[Task], RepositoryError>
    func fetchCompletedTasks(for plan: Plan) -> AnyPublisher<[Task], RepositoryError>
    func createTask(_ task: Task) -> AnyPublisher<Task, RepositoryError>
    func updateTask(_ task: Task) -> AnyPublisher<Task, RepositoryError>
    func deleteTask(_ task: Task) -> AnyPublisher<Void, RepositoryError>
    func toggleTaskCompletion(_ task: Task) -> AnyPublisher<Task, RepositoryError>
}

// MARK: - Subtask Repository协议
protocol SubtaskRepositoryProtocol {
    func fetchSubtasks(for task: Task) -> AnyPublisher<[Subtask], RepositoryError>
    func fetchSubtask(by id: String) -> AnyPublisher<Subtask?, RepositoryError>
    func createSubtask(_ subtask: Subtask) -> AnyPublisher<Subtask, RepositoryError>
    func updateSubtask(_ subtask: Subtask) -> AnyPublisher<Subtask, RepositoryError>
    func deleteSubtask(_ subtask: Subtask) -> AnyPublisher<Void, RepositoryError>
    func toggleSubtaskCompletion(_ subtask: Subtask) -> AnyPublisher<Subtask, RepositoryError>
}

// MARK: - PomodoroSession Repository协议
protocol PomodoroSessionRepositoryProtocol {
    func fetchSessions(for task: Task) -> AnyPublisher<[PomodoroSession], RepositoryError>
    func fetchSession(by id: String) -> AnyPublisher<PomodoroSession?, RepositoryError>
    func fetchSessionsForDate(_ date: Date) -> AnyPublisher<[PomodoroSession], RepositoryError>
    func fetchCompletedSessions(for task: Task) -> AnyPublisher<[PomodoroSession], RepositoryError>
    func createSession(_ session: PomodoroSession) -> AnyPublisher<PomodoroSession, RepositoryError>
    func updateSession(_ session: PomodoroSession) -> AnyPublisher<PomodoroSession, RepositoryError>
    func deleteSession(_ session: PomodoroSession) -> AnyPublisher<Void, RepositoryError>
    func completeSession(_ session: PomodoroSession) -> AnyPublisher<PomodoroSession, RepositoryError>
}

// MARK: - Repository管理器协议
protocol RepositoryManagerProtocol {
    var userRepository: UserRepositoryProtocol { get }
    var planRepository: PlanRepositoryProtocol { get }
    var taskRepository: TaskRepositoryProtocol { get }
    var subtaskRepository: SubtaskRepositoryProtocol { get }
    var pomodoroSessionRepository: PomodoroSessionRepositoryProtocol { get }
}
