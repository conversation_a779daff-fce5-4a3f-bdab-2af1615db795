import Foundation
import Combine

/// Repository管理器 - 统一管理所有数据访问层
class RepositoryManager: RepositoryManagerProtocol, ObservableObject {
    
    // MARK: - 单例
    static let shared = RepositoryManager()
    
    // MARK: - Repository实例
    let userRepository: UserRepositoryProtocol
    let planRepository: PlanRepositoryProtocol
    let taskRepository: TaskRepositoryProtocol
    let subtaskRepository: SubtaskRepositoryProtocol
    let pomodoroSessionRepository: PomodoroSessionRepositoryProtocol
    
    // MARK: - 私有属性
    private let coreDataManager: CoreDataManager
    
    // MARK: - 初始化
    private init() {
        self.coreDataManager = CoreDataManager.shared
        
        // 初始化所有Repository
        self.userRepository = UserRepository(coreDataManager: coreDataManager)
        self.planRepository = PlanRepository(coreDataManager: coreDataManager)
        self.taskRepository = TaskRepository(coreDataManager: coreDataManager)
        self.subtaskRepository = SubtaskRepository(coreDataManager: coreDataManager)
        self.pomodoroSessionRepository = PomodoroSessionRepository(coreDataManager: coreDataManager)
    }
    
    // MARK: - 便捷方法
    
    /// 获取当前用户的所有数据
    func fetchUserData() -> AnyPublisher<UserData, RepositoryError> {
        return userRepository.fetchCurrentUser()
            .flatMap { user -> AnyPublisher<UserData, RepositoryError> in
                guard let user = user else {
                    return Fail(error: RepositoryError.entityNotFound("User"))
                        .eraseToAnyPublisher()
                }
                
                return Publishers.Zip3(
                    self.planRepository.fetchPlans(for: user),
                    self.taskRepository.fetchTodayTasks(),
                    self.pomodoroSessionRepository.fetchSessionsForDate(Date())
                )
                .map { plans, todayTasks, todaySessions in
                    UserData(
                        user: user,
                        plans: plans,
                        todayTasks: todayTasks,
                        todaySessions: todaySessions
                    )
                }
                .mapError { error in
                    if let repoError = error as? RepositoryError {
                        return repoError
                    } else {
                        return RepositoryError.fetchError(error)
                    }
                }
                .eraseToAnyPublisher()
            }
            .eraseToAnyPublisher()
    }
    
    /// 创建示例数据（用于开发和测试）
    func createSampleData() -> AnyPublisher<Void, RepositoryError> {
        return coreDataManager.performBackgroundTask { context in
            // 创建示例用户
            let user = User(context: context)
            user.id = UUID().uuidString
            user.name = "示例用户"
            user.checkInDays = 7
            user.createdAt = Date()
            user.updatedAt = Date()
            
            // 创建示例计划
            let plan = Plan(context: context)
            plan.id = UUID().uuidString
            plan.title = "学习Swift开发"
            plan.planDescription = "掌握iOS应用开发技能"
            plan.status = "active"
            plan.progress = 0.3
            plan.startDate = Date()
            plan.totalTomatoes = 0
            plan.createdAt = Date()
            plan.updatedAt = Date()
            plan.user = user
            
            // 创建示例任务
            let task1 = Task(context: context)
            task1.id = UUID().uuidString
            task1.title = "学习SwiftUI基础"
            task1.startTime = Calendar.current.date(byAdding: .hour, value: 1, to: Date())
            task1.endTime = Calendar.current.date(byAdding: .hour, value: 3, to: Date())
            task1.priority = "high"
            task1.tomatoCount = 4
            task1.isReminderEnabled = true
            task1.isCompleted = false
            task1.createdAt = Date()
            task1.updatedAt = Date()
            task1.plan = plan
            
            let task2 = Task(context: context)
            task2.id = UUID().uuidString
            task2.title = "练习Core Data集成"
            task2.startTime = Calendar.current.date(byAdding: .hour, value: 4, to: Date())
            task2.endTime = Calendar.current.date(byAdding: .hour, value: 6, to: Date())
            task2.priority = "medium"
            task2.tomatoCount = 3
            task2.isReminderEnabled = false
            task2.isCompleted = false
            task2.createdAt = Date()
            task2.updatedAt = Date()
            task2.plan = plan
            
            // 创建示例子任务
            let subtask1 = Subtask(context: context)
            subtask1.id = UUID().uuidString
            subtask1.title = "阅读SwiftUI文档"
            subtask1.isCompleted = true
            subtask1.createdAt = Date()
            subtask1.updatedAt = Date()
            subtask1.task = task1
            
            let subtask2 = Subtask(context: context)
            subtask2.id = UUID().uuidString
            subtask2.title = "完成练习项目"
            subtask2.isCompleted = false
            subtask2.createdAt = Date()
            subtask2.updatedAt = Date()
            subtask2.task = task1
            
            return ()
        }
        .mapError { error in
            RepositoryError.saveError(error)
        }
        .eraseToAnyPublisher()
    }
}

// MARK: - 数据模型
struct UserData {
    let user: User
    let plans: [Plan]
    let todayTasks: [Task]
    let todaySessions: [PomodoroSession]
}
