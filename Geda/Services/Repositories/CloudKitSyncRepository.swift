//
//  CloudKitSyncRepository.swift
//  Geda
//
//  Created by AI Assistant on 2025-07-22.
//

import Foundation
import Combine
import CoreData
import CloudKit

/// CloudKit同步扩展，为现有Repository添加云端同步功能
extension UserRepository {
    
    /// 同步用户数据到CloudKit
    func syncToCloudKit(_ user: User) -> AnyPublisher<Void, RepositoryError> {
        return CloudKitManager.shared.uploadRecord(user)
            .map { _ in () }
            .mapError { cloudKitError in
                RepositoryError.saveError(cloudKitError)
            }
            .eraseToAnyPublisher()
    }
    
    /// 从CloudKit同步用户数据
    func syncFromCloudKit() -> AnyPublisher<[User], RepositoryError> {
        return CloudKitManager.shared.syncRecordType(User.self)
            .mapError { cloudKitError in
                RepositoryError.fetchError(cloudKitError)
            }
            .flatMap { [weak self] _ -> AnyPublisher<[User], RepositoryError> in
                guard let self = self else {
                    return Fail(error: RepositoryError.fetchError(NSError(domain: "Repository", code: -1)))
                        .eraseToAnyPublisher()
                }
                return self.fetchAll()
            }
            .eraseToAnyPublisher()
    }
    
    /// 获取所有用户
    private func fetchAll() -> AnyPublisher<[User], RepositoryError> {
        return Future<[User], RepositoryError> { [weak self] promise in
            guard let self = self else {
                promise(.failure(.fetchError(NSError(domain: "Repository", code: -1))))
                return
            }
            
            let request = NSFetchRequest<User>(entityName: "User")
            
            do {
                let results = try CoreDataManager.shared.viewContext.fetch(request)
                promise(.success(results))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
}

extension PlanRepository {
    
    /// 同步计划数据到CloudKit
    func syncToCloudKit(_ plan: Plan) -> AnyPublisher<Void, RepositoryError> {
        return CloudKitManager.shared.uploadRecord(plan)
            .map { _ in () }
            .mapError { cloudKitError in
                RepositoryError.saveError(cloudKitError)
            }
            .eraseToAnyPublisher()
    }
}

extension TaskRepository {
    
    /// 同步任务数据到CloudKit
    func syncToCloudKit(_ task: Task) -> AnyPublisher<Void, RepositoryError> {
        return CloudKitManager.shared.uploadRecord(task)
            .map { _ in () }
            .mapError { cloudKitError in
                RepositoryError.saveError(cloudKitError)
            }
            .eraseToAnyPublisher()
    }
}

extension SubtaskRepository {
    
    /// 同步子任务数据到CloudKit
    func syncToCloudKit(_ subtask: Subtask) -> AnyPublisher<Void, RepositoryError> {
        return CloudKitManager.shared.uploadRecord(subtask)
            .map { _ in () }
            .mapError { cloudKitError in
                RepositoryError.saveError(cloudKitError)
            }
            .eraseToAnyPublisher()
    }
}

extension PomodoroSessionRepository {
    
    /// 同步番茄钟会话数据到CloudKit
    func syncToCloudKit(_ session: PomodoroSession) -> AnyPublisher<Void, RepositoryError> {
        return CloudKitManager.shared.uploadRecord(session)
            .map { _ in () }
            .mapError { cloudKitError in
                RepositoryError.saveError(cloudKitError)
            }
            .eraseToAnyPublisher()
    }
}