import Foundation
import Combine
import CoreData

/// User数据访问层实现
class UserRepository: UserRepositoryProtocol {
    
    // MARK: - 属性
    private let coreDataManager: CoreDataManager
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    init(coreDataManager: CoreDataManager) {
        self.coreDataManager = coreDataManager
    }
    
    // MARK: - UserRepositoryProtocol实现
    
    /// 根据ID获取用户
    func fetchUser(by id: String) -> AnyPublisher<User?, RepositoryError> {
        return Future<User?, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<User> = User.fetchRequest()
            request.predicate = NSPredicate(format: "id == %@", id)
            request.fetchLimit = 1
            
            do {
                let users = try context.fetch(request)
                promise(.success(users.first))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 获取当前用户（假设只有一个用户）
    func fetchCurrentUser() -> AnyPublisher<User?, RepositoryError> {
        return Future<User?, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<User> = User.fetchRequest()
            request.fetchLimit = 1
            
            do {
                let users = try context.fetch(request)
                promise(.success(users.first))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 创建新用户
    func createUser(_ user: User) -> AnyPublisher<User, RepositoryError> {
        return Future<User, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            // 验证用户数据
            guard !user.name!.isEmpty else {
                promise(.failure(.validationError("用户名不能为空")))
                return
            }
            
            // 设置创建时间
            user.createdAt = Date()
            user.updatedAt = Date()
            
            do {
                try context.save()
                promise(.success(user))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 更新用户信息
    func updateUser(_ user: User) -> AnyPublisher<User, RepositoryError> {
        return Future<User, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            // 验证用户数据
            guard !user.name!.isEmpty else {
                promise(.failure(.validationError("用户名不能为空")))
                return
            }
            
            // 更新时间戳
            user.updatedAt = Date()
            
            do {
                try context.save()
                promise(.success(user))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 删除用户
    func deleteUser(_ user: User) -> AnyPublisher<Void, RepositoryError> {
        return Future<Void, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            context.delete(user)
            
            do {
                try context.save()
                promise(.success(()))
            } catch {
                promise(.failure(.deleteError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 增加用户打卡天数
    func incrementCheckInDays(for user: User) -> AnyPublisher<User, RepositoryError> {
        return Future<User, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            user.checkInDays += 1
            user.updatedAt = Date()
            
            do {
                try context.save()
                promise(.success(user))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
}
