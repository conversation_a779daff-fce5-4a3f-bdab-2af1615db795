import Foundation
import Combine
import CoreData

/// Plan数据访问层实现
class PlanRepository: PlanRepositoryProtocol {
    
    // MARK: - 属性
    private let coreDataManager: CoreDataManager
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    init(coreDataManager: CoreDataManager) {
        self.coreDataManager = coreDataManager
    }
    
    // MARK: - PlanRepositoryProtocol实现
    
    /// 获取用户的所有计划
    func fetchPlans(for user: User) -> AnyPublisher<[Plan], RepositoryError> {
        return Future<[Plan], RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<Plan> = Plan.fetchRequest()
            request.predicate = NSPredicate(format: "user == %@", user)
            request.sortDescriptors = [NSSortDescriptor(key: "createdAt", ascending: false)]
            
            do {
                let plans = try context.fetch(request)
                promise(.success(plans))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 根据ID获取计划
    func fetchPlan(by id: String) -> AnyPublisher<Plan?, RepositoryError> {
        return Future<Plan?, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<Plan> = Plan.fetchRequest()
            request.predicate = NSPredicate(format: "id == %@", id)
            request.fetchLimit = 1
            
            do {
                let plans = try context.fetch(request)
                promise(.success(plans.first))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 获取用户的活跃计划
    func fetchActivePlans(for user: User) -> AnyPublisher<[Plan], RepositoryError> {
        return Future<[Plan], RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<Plan> = Plan.fetchRequest()
            request.predicate = NSPredicate(format: "user == %@ AND status == %@", user, "active")
            request.sortDescriptors = [NSSortDescriptor(key: "createdAt", ascending: false)]
            
            do {
                let plans = try context.fetch(request)
                promise(.success(plans))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 获取用户的已完成计划
    func fetchCompletedPlans(for user: User) -> AnyPublisher<[Plan], RepositoryError> {
        return Future<[Plan], RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<Plan> = Plan.fetchRequest()
            request.predicate = NSPredicate(format: "user == %@ AND status == %@", user, "completed")
            request.sortDescriptors = [NSSortDescriptor(key: "updatedAt", ascending: false)]
            
            do {
                let plans = try context.fetch(request)
                promise(.success(plans))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 获取用户的暂停计划
    func fetchPausedPlans(for user: User) -> AnyPublisher<[Plan], RepositoryError> {
        return Future<[Plan], RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            let request: NSFetchRequest<Plan> = Plan.fetchRequest()
            request.predicate = NSPredicate(format: "user == %@ AND status == %@", user, "paused")
            request.sortDescriptors = [NSSortDescriptor(key: "updatedAt", ascending: false)]
            
            do {
                let plans = try context.fetch(request)
                promise(.success(plans))
            } catch {
                promise(.failure(.fetchError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 创建新计划
    func createPlan(_ plan: Plan) -> AnyPublisher<Plan, RepositoryError> {
        return Future<Plan, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            // 验证计划数据
            guard !plan.title!.isEmpty else {
                promise(.failure(.validationError("计划标题不能为空")))
                return
            }
            
            // 设置默认值
            plan.createdAt = Date()
            plan.updatedAt = Date()
            plan.progress = 0.0
            plan.totalTomatoes = 0
            
            if plan.status == nil {
                plan.status = "active"
            }
            
            do {
                try context.save()
                promise(.success(plan))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 更新计划
    func updatePlan(_ plan: Plan) -> AnyPublisher<Plan, RepositoryError> {
        return Future<Plan, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            // 验证计划数据
            guard !plan.title!.isEmpty else {
                promise(.failure(.validationError("计划标题不能为空")))
                return
            }
            
            plan.updatedAt = Date()
            
            do {
                try context.save()
                promise(.success(plan))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 删除计划
    func deletePlan(_ plan: Plan) -> AnyPublisher<Void, RepositoryError> {
        return Future<Void, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            context.delete(plan)
            
            do {
                try context.save()
                promise(.success(()))
            } catch {
                promise(.failure(.deleteError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 更新计划进度
    func updatePlanProgress(_ plan: Plan) -> AnyPublisher<Plan, RepositoryError> {
        return Future<Plan, RepositoryError> { promise in
            let context = self.coreDataManager.viewContext
            
            // 计算进度：已完成任务数 / 总任务数
            let tasks = plan.tasks?.allObjects as? [Task] ?? []
            let completedTasks = tasks.filter { $0.isCompleted }
            
            if !tasks.isEmpty {
                plan.progress = Float(completedTasks.count) / Float(tasks.count)
            } else {
                plan.progress = 0.0
            }
            
            // 如果所有任务都完成，更新计划状态
            if plan.progress >= 1.0 && plan.status != "completed" {
                plan.status = "completed"
                plan.endDate = Date()
            }
            
            plan.updatedAt = Date()
            
            do {
                try context.save()
                promise(.success(plan))
            } catch {
                promise(.failure(.saveError(error)))
            }
        }
        .eraseToAnyPublisher()
    }
}
