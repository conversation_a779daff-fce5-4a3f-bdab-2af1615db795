import Foundation
import UserNotifications
import SwiftUI

/// 通知管理器 - 统一管理应用的所有通知功能
class NotificationManager: NSObject, ObservableObject {
    static let shared = NotificationManager()
    
    @Published var isAuthorized = false
    
    override init() {
        super.init()
        checkAuthorizationStatus()
    }
    
    /// 请求通知权限
    func requestPermission() async -> Bool {
        do {
            let granted = try await UNUserNotificationCenter.current().requestAuthorization(
                options: [.alert, .sound, .badge, .criticalAlert]
            )
            
            await MainActor.run {
                self.isAuthorized = granted
            }
            
            if granted {
                await registerForRemoteNotifications()
            }
            
            return granted
        } catch {
            print("通知权限请求失败: \(error)")
            return false
        }
    }
    
    /// 检查当前授权状态
    private func checkAuthorizationStatus() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.isAuthorized = settings.authorizationStatus == .authorized
            }
        }
    }
    
    /// 注册远程通知
    @MainActor
    private func registerForRemoteNotifications() async {
        UIApplication.shared.registerForRemoteNotifications()
    }
    
    // MARK: - 任务提醒通知
    
    /// 安排任务提醒通知
    func scheduleTaskReminder(for task: Task, reminderMinutes: Int = 15) {
        guard isAuthorized, let startTime = task.startTime else { return }
        
        let reminderTime = startTime.addingTimeInterval(-TimeInterval(reminderMinutes * 60))
        
        // 确保提醒时间在未来
        guard reminderTime > Date() else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "任务提醒"
        content.body = "您的任务「\(task.title ?? "")」将在\(reminderMinutes)分钟后开始"
        content.sound = .default
        content.badge = 1
        
        // 添加操作按钮
        let startAction = UNNotificationAction(
            identifier: "START_FOCUS",
            title: "开始专注",
            options: [.foreground]
        )
        
        let snoozeAction = UNNotificationAction(
            identifier: "SNOOZE_5MIN",
            title: "延后5分钟",
            options: []
        )
        
        let category = UNNotificationCategory(
            identifier: "TASK_REMINDER",
            actions: [startAction, snoozeAction],
            intentIdentifiers: [],
            options: []
        )
        
        UNUserNotificationCenter.current().setNotificationCategories([category])
        content.categoryIdentifier = "TASK_REMINDER"
        
        // 添加用户信息
        content.userInfo = [
            "type": "task_reminder",
            "taskId": task.objectID.uriRepresentation().absoluteString,
            "taskTitle": task.title ?? ""
        ]
        
        let dateComponents = Calendar.current.dateComponents([.year, .month, .day, .hour, .minute], from: reminderTime)
        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: false)
        
        let request = UNNotificationRequest(
            identifier: "task_reminder_\(task.objectID.uriRepresentation().absoluteString)",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("任务提醒安排失败: \(error)")
            } else {
                print("任务提醒已安排: \(task.title ?? "") at \(reminderTime)")
            }
        }
    }
    
    /// 取消任务提醒
    func cancelTaskReminder(for task: Task) {
        let identifier = "task_reminder_\(task.objectID.uriRepresentation().absoluteString)"
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [identifier])
    }
    
    // MARK: - 番茄钟通知
    
    /// 安排番茄钟完成通知
    func schedulePomodoroCompletion(sessionType: SessionType, afterSeconds: TimeInterval) {
        guard isAuthorized else { return }
        
        let content = UNMutableNotificationContent()
        
        switch sessionType {
        case .work:
            content.title = "专注时间结束"
            content.body = "恭喜完成25分钟专注，休息一下吧！"
        case .shortBreak:
            content.title = "休息时间结束"
            content.body = "休息结束，准备开始下一个专注时段"
        case .longBreak:
            content.title = "长休息结束"
            content.body = "长休息结束，准备开始新的专注循环"
        }
        
        content.sound = .default
        content.badge = 1
        
        // 添加操作按钮
        let continueAction = UNNotificationAction(
            identifier: sessionType == .work ? "START_BREAK" : "START_WORK",
            title: sessionType == .work ? "开始休息" : "继续专注",
            options: [.foreground]
        )
        
        let skipAction = UNNotificationAction(
            identifier: "SKIP_SESSION",
            title: "跳过",
            options: []
        )
        
        let category = UNNotificationCategory(
            identifier: "POMODORO_COMPLETE",
            actions: [continueAction, skipAction],
            intentIdentifiers: [],
            options: []
        )
        
        UNUserNotificationCenter.current().setNotificationCategories([category])
        content.categoryIdentifier = "POMODORO_COMPLETE"
        
        content.userInfo = [
            "type": "pomodoro_complete",
            "sessionType": sessionType.rawValue
        ]
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: afterSeconds, repeats: false)
        
        let request = UNNotificationRequest(
            identifier: "pomodoro_complete_\(Date().timeIntervalSince1970)",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("番茄钟通知安排失败: \(error)")
            } else {
                print("番茄钟通知已安排，\(afterSeconds)秒后触发")
            }
        }
    }
    
    /// 取消所有番茄钟通知
    func cancelAllPomodoroNotifications() {
        UNUserNotificationCenter.current().getPendingNotificationRequests { requests in
            let pomodoroIdentifiers = requests
                .filter { $0.identifier.hasPrefix("pomodoro_complete_") }
                .map { $0.identifier }
            
            UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: pomodoroIdentifiers)
        }
    }
    
    // MARK: - 日常提醒通知
    
    /// 安排每日复盘提醒
    func scheduleDailyReviewReminder(at time: DateComponents) {
        guard isAuthorized else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "每日复盘时间"
        content.body = "是时候回顾今天的学习成果了"
        content.sound = .default
        content.badge = 1
        
        content.userInfo = [
            "type": "daily_review"
        ]
        
        let trigger = UNCalendarNotificationTrigger(dateMatching: time, repeats: true)
        
        let request = UNNotificationRequest(
            identifier: "daily_review",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("每日复盘提醒安排失败: \(error)")
            } else {
                print("每日复盘提醒已安排")
            }
        }
    }
    
    /// 安排学习计划提醒
    func schedulePlanReminder(for plan: Plan, at time: Date) {
        guard isAuthorized else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "学习计划提醒"
        content.body = "您的计划「\(plan.title ?? "")」需要继续执行"
        content.sound = .default
        content.badge = 1
        
        content.userInfo = [
            "type": "plan_reminder",
            "planId": plan.objectID.uriRepresentation().absoluteString,
            "planTitle": plan.title ?? ""
        ]
        
        let dateComponents = Calendar.current.dateComponents([.year, .month, .day, .hour, .minute], from: time)
        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: false)
        
        let request = UNNotificationRequest(
            identifier: "plan_reminder_\(plan.objectID.uriRepresentation().absoluteString)",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("计划提醒安排失败: \(error)")
            } else {
                print("计划提醒已安排: \(plan.title ?? "")")
            }
        }
    }
    
    // MARK: - 通知管理
    
    /// 获取所有待处理的通知
    func getPendingNotifications() async -> [UNNotificationRequest] {
        return await UNUserNotificationCenter.current().pendingNotificationRequests()
    }
    
    /// 清除应用图标上的角标
    func clearBadge() {
        UNUserNotificationCenter.current().setBadgeCount(0) { error in
            if let error = error {
                print("清除角标失败: \(error)")
            }
        }
    }
    
    /// 清除所有通知
    func clearAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        UNUserNotificationCenter.current().removeAllDeliveredNotifications()
        clearBadge()
    }
    
    /// 取消特定类型的通知
    func cancelNotifications(ofType type: String) {
        UNUserNotificationCenter.current().getPendingNotificationRequests { requests in
            let identifiersToRemove = requests
                .filter { request in
                    if let userInfo = request.content.userInfo as? [String: Any],
                       let notificationType = userInfo["type"] as? String {
                        return notificationType == type
                    }
                    return false
                }
                .map { $0.identifier }
            
            UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: identifiersToRemove)
        }
    }
}


// MARK: - UNUserNotificationCenterDelegate
extension NotificationManager: UNUserNotificationCenterDelegate {
    
    /// 在前台显示通知
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // 在前台也显示通知
        completionHandler([.banner, .sound, .badge])
    }
    
    /// 处理通知点击事件
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        
        switch response.actionIdentifier {
        case "START_FOCUS":
            handleStartFocusAction(userInfo: userInfo)
        case "SNOOZE_5MIN":
            handleSnoozeAction(userInfo: userInfo)
        case "START_BREAK":
            handleStartBreakAction(userInfo: userInfo)
        case "START_WORK":
            handleStartWorkAction(userInfo: userInfo)
        case "SKIP_SESSION":
            handleSkipSessionAction(userInfo: userInfo)
        case UNNotificationDefaultActionIdentifier:
            handleDefaultAction(userInfo: userInfo)
        default:
            break
        }
        
        completionHandler()
    }
    
    // MARK: - 通知动作处理
    
    private func handleStartFocusAction(userInfo: [AnyHashable: Any]) {
        guard let taskId = userInfo["taskId"] as? String else { return }
        
        // 发送通知给相关ViewModel开始专注
        NotificationCenter.default.post(
            name: .startFocusForTask,
            object: nil,
            userInfo: ["taskId": taskId]
        )
    }
    
    private func handleSnoozeAction(userInfo: [AnyHashable: Any]) {
        guard let taskId = userInfo["taskId"] as? String,
              let taskTitle = userInfo["taskTitle"] as? String else { return }
        
        // 5分钟后再次提醒
        let content = UNMutableNotificationContent()
        content.title = "任务提醒"
        content.body = "您延后的任务「\(taskTitle)」准备开始了"
        content.sound = .default
        content.badge = 1
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 5 * 60, repeats: false)
        let request = UNNotificationRequest(
            identifier: "task_snooze_\(taskId)",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request)
    }
    
    private func handleStartBreakAction(userInfo: [AnyHashable: Any]) {
        NotificationCenter.default.post(name: .startBreakSession, object: nil)
    }
    
    private func handleStartWorkAction(userInfo: [AnyHashable: Any]) {
        NotificationCenter.default.post(name: .startWorkSession, object: nil)
    }
    
    private func handleSkipSessionAction(userInfo: [AnyHashable: Any]) {
        NotificationCenter.default.post(name: .skipCurrentSession, object: nil)
    }
    
    private func handleDefaultAction(userInfo: [AnyHashable: Any]) {
        guard let type = userInfo["type"] as? String else { return }
        
        switch type {
        case "task_reminder":
            // 打开任务详情页面
            if let taskId = userInfo["taskId"] as? String {
                NotificationCenter.default.post(
                    name: .openTaskDetail,
                    object: nil,
                    userInfo: ["taskId": taskId]
                )
            }
        case "pomodoro_complete":
            // 打开番茄钟页面
            NotificationCenter.default.post(name: .openFocusTimer, object: nil)
        case "daily_review":
            // 打开复盘页面
            NotificationCenter.default.post(name: .openReview, object: nil)
        case "plan_reminder":
            // 打开计划页面
            if let planId = userInfo["planId"] as? String {
                NotificationCenter.default.post(
                    name: .openPlanDetail,
                    object: nil,
                    userInfo: ["planId": planId]
                )
            }
        default:
            break
        }
    }
}

// MARK: - 通知名称定义
extension Notification.Name {
    static let startFocusForTask = Notification.Name("startFocusForTask")
    static let startBreakSession = Notification.Name("startBreakSession")
    static let startWorkSession = Notification.Name("startWorkSession")
    static let skipCurrentSession = Notification.Name("skipCurrentSession")
    static let openTaskDetail = Notification.Name("openTaskDetail")
    static let openFocusTimer = Notification.Name("openFocusTimer")
    static let openReview = Notification.Name("openReview")
    static let openPlanDetail = Notification.Name("openPlanDetail")
}