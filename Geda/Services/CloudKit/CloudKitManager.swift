//
//  CloudKitManager.swift
//  Geda
//
//  Created by AI Assistant on 2025-07-22.
//

import Foundation
import CloudKit
import Combine
import UIKit
import CoreData

/// CloudKit同步管理器 - 负责与iCloud的数据同步
class CloudKitManager: ObservableObject {
    
    // MARK: - 单例
    static let shared = CloudKitManager()
    
    // MARK: - CloudKit容器和数据库
    private let container: CKContainer
    private let privateDatabase: CKDatabase
    private let publicDatabase: CKDatabase
    
    // MARK: - Published属性
    @Published var syncStatus: CloudKitSyncStatus = .notSynced
    @Published var isAccountAvailable = false
    @Published var lastSyncDate: Date?
    
    // MARK: - 私有属性
    private var cancellables = Set<AnyCancellable>()
    private let coreDataManager = CoreDataManager.shared
    private let authService = AuthenticationService.shared
    
    // 同步队列和定时器
    private let syncQueue = DispatchQueue(label: "cloudkit.sync", qos: .utility)
    private var syncTimer: Timer?
    private var isInitialSyncCompleted = false
    
    // MARK: - 初始化
    private init() {
        self.container = CKContainer(identifier: CloudKitConfiguration.containerIdentifier)
        self.privateDatabase = container.privateCloudDatabase
        self.publicDatabase = container.publicCloudDatabase
        
        setupObservers()
        checkAccountStatus()
    }
    
    // MARK: - 公共方法
    
    /// 开始同步
    func startSync() {
        guard authService.isAuthenticated && isAccountAvailable else {
            print("CloudKit: 用户未认证或iCloud不可用")
            return
        }
        
        syncStatus = .syncing
        
        // 执行初始同步
        if !isInitialSyncCompleted {
            performInitialSync()
        } else {
            performIncrementalSync()
        }
        
        // 启动定期同步
        startPeriodicSync()
    }
    
    /// 停止同步
    func stopSync() {
        syncTimer?.invalidate()
        syncTimer = nil
        syncStatus = .notSynced
    }
    
    /// 强制全量同步
    func forceSyncAll() -> AnyPublisher<Void, CloudKitError> {
        return Publishers.Sequence(sequence: [
            syncUsers(),
            syncPlans(),
            syncTasks(),
            syncSubtasks(),
            syncPomodoroSessions()
        ])
        .flatMap { $0 }
        .collect()
        .map { _ in () }
        .eraseToAnyPublisher()
    }
    
    /// 同步单个记录类型
    func syncRecordType<T: NSManagedObject & CloudKitConvertible>(_ type: T.Type) -> AnyPublisher<Void, CloudKitError> {
        switch type {
        case is User.Type:
            return syncUsers()
        case is Plan.Type:
            return syncPlans()
        case is Task.Type:
            return syncTasks()
        case is Subtask.Type:
            return syncSubtasks()
        case is PomodoroSession.Type:
            return syncPomodoroSessions()
        default:
            return Fail(error: CloudKitError.unknownError(NSError(domain: "CloudKit", code: -1, userInfo: [NSLocalizedDescriptionKey: "不支持的记录类型"])))
                .eraseToAnyPublisher()
        }
    }
    
    // MARK: - 私有方法
    
    private func setupObservers() {
        // 监听认证状态变化
        authService.$isAuthenticated
            .sink { [weak self] isAuthenticated in
                if isAuthenticated {
                    self?.checkAccountStatus()
                } else {
                    self?.stopSync()
                }
            }
            .store(in: &cancellables)
        
        // 监听应用生命周期
        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .sink { [weak self] _ in
                self?.checkAccountStatus()
            }
            .store(in: &cancellables)
    }
    
    private func checkAccountStatus() {
        container.accountStatus { [weak self] status, error in
            DispatchQueue.main.async {
                switch status {
                case .available:
                    self?.isAccountAvailable = true
                    print("CloudKit: iCloud账户可用")
                case .noAccount:
                    self?.isAccountAvailable = false
                    print("CloudKit: 未登录iCloud账户")
                case .restricted, .couldNotDetermine:
                    self?.isAccountAvailable = false
                    print("CloudKit: iCloud账户受限或无法确定状态")
                @unknown default:
                    self?.isAccountAvailable = false
                    print("CloudKit: 未知账户状态")
                }
                
                if let error = error {
                    print("CloudKit: 检查账户状态失败 - \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func performInitialSync() {
        print("CloudKit: 开始初始同步")
        
        forceSyncAll()
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        switch completion {
                        case .finished:
                            self?.isInitialSyncCompleted = true
                            self?.syncStatus = .synced
                            self?.lastSyncDate = Date()
                            print("CloudKit: 初始同步完成")
                        case .failure(let error):
                            self?.syncStatus = .failed(error)
                            print("CloudKit: 初始同步失败 - \(error.localizedDescription)")
                        }
                    }
                },
                receiveValue: { _ in }
            )
            .store(in: &cancellables)
    }
    
    private func performIncrementalSync() {
        print("CloudKit: 开始增量同步")
        
        // 获取上次同步后的变更
        fetchChanges()
            .sink(
                receiveCompletion: { [weak self] completion in
                    DispatchQueue.main.async {
                        switch completion {
                        case .finished:
                            self?.syncStatus = .synced
                            self?.lastSyncDate = Date()
                            print("CloudKit: 增量同步完成")
                        case .failure(let error):
                            self?.syncStatus = .failed(error)
                            print("CloudKit: 增量同步失败 - \(error.localizedDescription)")
                        }
                    }
                },
                receiveValue: { _ in }
            )
            .store(in: &cancellables)
    }
    
    private func startPeriodicSync() {
        // 每30分钟自动同步一次
        syncTimer = Timer.scheduledTimer(withTimeInterval: 1800, repeats: true) { [weak self] _ in
            self?.performIncrementalSync()
        }
    }
    
    private func fetchChanges() -> AnyPublisher<Void, CloudKitError> {
        // 使用CKFetchDatabaseChangesOperation获取数据库变更
        return Future<Void, CloudKitError> { [weak self] promise in
            guard let self = self else {
                promise(.failure(.unknownError(NSError(domain: "CloudKit", code: -1, userInfo: [NSLocalizedDescriptionKey: "CloudKitManager已释放"]))))
                return
            }
            
            let operation = CKFetchDatabaseChangesOperation()
            
            operation.fetchDatabaseChangesCompletionBlock = { serverChangeToken, moreComing, error in
                if let error = error {
                    promise(.failure(.unknownError(error)))
                } else {
                    // 保存serverChangeToken用于下次增量同步
                    if let token = serverChangeToken {
                        self.saveServerChangeToken(token)
                    }
                    promise(.success(()))
                }
            }
            
            self.privateDatabase.add(operation)
        }
        .eraseToAnyPublisher()
    }
    
    private func saveServerChangeToken(_ token: CKServerChangeToken) {
        do {
            let data = try NSKeyedArchiver.archivedData(withRootObject: token, requiringSecureCoding: true)
            UserDefaults.standard.set(data, forKey: "cloudkit.serverChangeToken")
        } catch {
            print("CloudKit: 保存ServerChangeToken失败 - \(error.localizedDescription)")
        }
    }
    
    private func loadServerChangeToken() -> CKServerChangeToken? {
        guard let data = UserDefaults.standard.data(forKey: "cloudkit.serverChangeToken") else {
            return nil
        }
        
        do {
            return try NSKeyedUnarchiver.unarchivedObject(ofClass: CKServerChangeToken.self, from: data)
        } catch {
            print("CloudKit: 加载ServerChangeToken失败 - \(error.localizedDescription)")
            return nil
        }
    }
}

// MARK: - 具体同步方法
extension CloudKitManager {
    
    private func syncUsers() -> AnyPublisher<Void, CloudKitError> {
        return fetchAndSyncRecords(
            recordType: CloudKitSchema.RecordType.user.recordType,
            entityName: "User"
        )
    }
    
    private func syncPlans() -> AnyPublisher<Void, CloudKitError> {
        return fetchAndSyncRecords(
            recordType: CloudKitSchema.RecordType.plan.recordType,
            entityName: "Plan"
        )
    }
    
    private func syncTasks() -> AnyPublisher<Void, CloudKitError> {
        return fetchAndSyncRecords(
            recordType: CloudKitSchema.RecordType.task.recordType,
            entityName: "Task"
        )
    }
    
    private func syncSubtasks() -> AnyPublisher<Void, CloudKitError> {
        return fetchAndSyncRecords(
            recordType: CloudKitSchema.RecordType.subtask.recordType,
            entityName: "Subtask"
        )
    }
    
    private func syncPomodoroSessions() -> AnyPublisher<Void, CloudKitError> {
        return fetchAndSyncRecords(
            recordType: CloudKitSchema.RecordType.pomodoroSession.recordType,
            entityName: "PomodoroSession"
        )
    }
    
    private func fetchAndSyncRecords(recordType: String, entityName: String) -> AnyPublisher<Void, CloudKitError> {
        return Future<Void, CloudKitError> { [weak self] promise in
            guard let self = self else {
                promise(.failure(.unknownError(NSError(domain: "CloudKit", code: -1, userInfo: [NSLocalizedDescriptionKey: "CloudKitManager已释放"]))))
                return
            }
            
            let query = CKQuery(recordType: recordType, predicate: NSPredicate(value: true))
            
            self.privateDatabase.perform(query, inZoneWith: nil) { records, error in
                if let error = error {
                    promise(.failure(.unknownError(error)))
                    return
                }
                
                // 处理获取到的记录
                let records = records ?? []
                print("CloudKit: 获取到 \(records.count) 条 \(recordType) 记录")
                
                // 这里应该实现具体的Core Data同步逻辑
                // 暂时标记为成功
                promise(.success(()))
            }
        }
        .eraseToAnyPublisher()
    }
}

// MARK: - 记录上传方法
extension CloudKitManager {
    
    /// 上传Core Data实体到CloudKit
    func uploadRecord<T: NSManagedObject & CloudKitConvertible>(_ entity: T) -> AnyPublisher<CKRecord, CloudKitError> {
        return Future<CKRecord, CloudKitError> { [weak self] promise in
            guard let self = self else {
                promise(.failure(.unknownError(NSError(domain: "CloudKit", code: -1, userInfo: [NSLocalizedDescriptionKey: "CloudKitManager已释放"]))))
                return
            }
            
            let record = entity.toCKRecord()
            
            self.privateDatabase.save(record) { savedRecord, error in
                if let error = error {
                    promise(.failure(.unknownError(error)))
                } else if let savedRecord = savedRecord {
                    promise(.success(savedRecord))
                } else {
                    promise(.failure(.unknownError(NSError(domain: "CloudKit", code: -1, userInfo: [NSLocalizedDescriptionKey: "保存记录失败"]))))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 批量上传记录
    func uploadRecords<T: NSManagedObject & CloudKitConvertible>(_ entities: [T]) -> AnyPublisher<[CKRecord], CloudKitError> {
        let records = entities.map { $0.toCKRecord() }
        
        return Future<[CKRecord], CloudKitError> { [weak self] promise in
            guard let self = self else {
                promise(.failure(.unknownError(NSError(domain: "CloudKit", code: -1, userInfo: [NSLocalizedDescriptionKey: "CloudKitManager已释放"]))))
                return
            }
            
            let operation = CKModifyRecordsOperation(recordsToSave: records, recordIDsToDelete: nil)
            
            operation.modifyRecordsCompletionBlock = { savedRecords, deletedRecordIDs, error in
                if let error = error {
                    promise(.failure(.unknownError(error)))
                } else {
                    promise(.success(savedRecords ?? []))
                }
            }
            
            self.privateDatabase.add(operation)
        }
        .eraseToAnyPublisher()
    }
    
    /// 删除CloudKit记录
    func deleteRecord(with recordID: CKRecord.ID) -> AnyPublisher<Void, CloudKitError> {
        return Future<Void, CloudKitError> { [weak self] promise in
            guard let self = self else {
                promise(.failure(.unknownError(NSError(domain: "CloudKit", code: -1, userInfo: [NSLocalizedDescriptionKey: "CloudKitManager已释放"]))))
                return
            }
            
            self.privateDatabase.delete(withRecordID: recordID) { deletedRecordID, error in
                if let error = error {
                    promise(.failure(.unknownError(error)))
                } else {
                    promise(.success(()))
                }
            }
        }
        .eraseToAnyPublisher()
    }
}