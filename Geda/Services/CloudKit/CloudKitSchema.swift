//
//  CloudKitSchema.swift
//  Geda
//
//  Created by AI Assistant on 2025-07-22.
//

import Foundation
import CloudKit

/// CloudKit Schema定义 - 映射Core Data模型到CloudKit记录类型
enum CloudKitSchema {
    
    // MARK: - 记录类型定义
    enum RecordType: String, CaseIterable {
        case user = "User"
        case plan = "Plan" 
        case task = "Task"
        case subtask = "Subtask"
        case pomodoroSession = "PomodoroSession"
        
        var recordType: String {
            return self.rawValue
        }
    }
    
    // MARK: - 字段名定义
    enum UserFields: String, CaseIterable {
        case id = "id"
        case name = "name"
        case avatar = "avatar"
        case checkInDays = "checkInDays"
        case createdAt = "createdAt"
        case updatedAt = "updatedAt"
        case appleUserID = "appleUserID"  // 新增：关联Apple ID
        case lastSyncDate = "lastSyncDate"  // 新增：最后同步时间
    }
    
    enum PlanFields: String, CaseIterable {
        case id = "id"
        case title = "title"
        case planDescription = "planDescription"
        case status = "status"
        case progress = "progress"
        case startDate = "startDate"
        case endDate = "endDate"
        case totalTomatoes = "totalTomatoes"
        case createdAt = "createdAt"
        case updatedAt = "updatedAt"
        case userReference = "userReference"  // 引用User记录
    }
    
    enum TaskFields: String, CaseIterable {
        case id = "id"
        case title = "title"
        case startTime = "startTime"
        case endTime = "endTime"
        case priority = "priority"
        case tomatoCount = "tomatoCount"
        case isCompleted = "isCompleted"
        case isReminderEnabled = "isReminderEnabled"
        case createdAt = "createdAt"
        case updatedAt = "updatedAt"
        case planReference = "planReference"  // 引用Plan记录
    }
    
    enum SubtaskFields: String, CaseIterable {
        case id = "id"
        case title = "title"
        case isCompleted = "isCompleted"
        case createdAt = "createdAt"
        case updatedAt = "updatedAt"
        case taskReference = "taskReference"  // 引用Task记录
    }
    
    enum PomodoroSessionFields: String, CaseIterable {
        case id = "id"
        case sessionType = "sessionType"
        case startTime = "startTime"
        case endTime = "endTime"
        case duration = "duration"
        case isCompleted = "isCompleted"
        case createdAt = "createdAt"
        case taskReference = "taskReference"  // 引用Task记录
    }
}

// MARK: - CloudKit记录转换协议
protocol CloudKitConvertible {
    func toCKRecord() -> CKRecord
    static func fromCKRecord(_ record: CKRecord) -> Self?
}

// MARK: - CloudKit同步状态
enum CloudKitSyncStatus {
    case notSynced      // 未同步
    case syncing        // 同步中
    case synced         // 已同步
    case conflicted     // 有冲突
    case failed(Error)  // 同步失败
}

// MARK: - CloudKit同步元数据
struct CloudKitSyncMetadata {
    let recordID: CKRecord.ID
    let recordChangeTag: String
    let lastSyncDate: Date
    let syncStatus: CloudKitSyncStatus
}

// MARK: - CloudKit错误类型
enum CloudKitError: LocalizedError {
    case notAuthenticated
    case networkUnavailable
    case quotaExceeded
    case recordNotFound(String)
    case conflictResolutionFailed
    case syncTimeout
    case unknownError(Error)
    
    var errorDescription: String? {
        switch self {
        case .notAuthenticated:
            return "未登录iCloud账户"
        case .networkUnavailable:
            return "网络连接不可用"
        case .quotaExceeded:
            return "iCloud存储空间不足"
        case .recordNotFound(let recordID):
            return "未找到记录: \(recordID)"
        case .conflictResolutionFailed:
            return "数据冲突解决失败"
        case .syncTimeout:
            return "同步超时"
        case .unknownError(let error):
            return "未知错误: \(error.localizedDescription)"
        }
    }
}

// MARK: - CloudKit配置
struct CloudKitConfiguration {
    static let containerIdentifier = "iCloud.com.geda.app"
    static let privateDatabase = "private"
    static let publicDatabase = "public"
    
    // 同步配置
    static let batchSize = 100
    static let syncTimeout: TimeInterval = 30.0
    static let retryAttempts = 3
    static let backoffDelay: TimeInterval = 2.0
}