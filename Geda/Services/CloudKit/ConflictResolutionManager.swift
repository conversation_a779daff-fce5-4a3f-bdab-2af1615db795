//
//  ConflictResolutionManager.swift
//  Geda
//
//  Created by AI Assistant on 2025-07-22.
//

import Foundation
import CoreData
import CloudKit

/// 数据冲突解决管理器
class ConflictResolutionManager {
    
    // MARK: - 单例
    static let shared = ConflictResolutionManager()
    
    // MARK: - 冲突解决策略
    enum ConflictResolutionStrategy {
        case localWins          // 本地优先
        case remoteWins         // 远程优先  
        case newestWins         // 最新修改优先
        case userChoose         // 用户选择
        case mergeFields        // 字段级别合并
    }
    
    // MARK: - 冲突类型
    enum ConflictType {
        case dataConflict(local: NSManagedObject, remote: CKRecord)
        case deletionConflict(localExists: Bool, remoteExists: Bool)
        case schemaConflict(String)
    }
    
    // MARK: - 冲突解决结果
    struct ConflictResolution {
        let strategy: ConflictResolutionStrategy
        let resolvedData: NSManagedObject?
        let shouldSync: Bool
        let error: Error?
    }
    
    private init() {}
    
    // MARK: - 主要冲突解决方法
    
    /// 解决Core Data与CloudKit之间的数据冲突
    func resolveConflict<T: NSManagedObject & CloudKitConvertible>(
        localEntity: T,
        remoteRecord: CKRecord,
        strategy: ConflictResolutionStrategy = .newestWins
    ) -> ConflictResolution {
        
        switch strategy {
        case .localWins:
            return resolveWithLocalWins(localEntity: localEntity, remoteRecord: remoteRecord)
            
        case .remoteWins:
            return resolveWithRemoteWins(localEntity: localEntity, remoteRecord: remoteRecord)
            
        case .newestWins:
            return resolveWithNewestWins(localEntity: localEntity, remoteRecord: remoteRecord)
            
        case .userChoose:
            return resolveWithUserChoice(localEntity: localEntity, remoteRecord: remoteRecord)
            
        case .mergeFields:
            return resolveWithFieldMerge(localEntity: localEntity, remoteRecord: remoteRecord)
        }
    }
    
    // MARK: - 具体解决策略实现
    
    /// 本地优先策略
    private func resolveWithLocalWins<T: NSManagedObject & CloudKitConvertible>(
        localEntity: T,
        remoteRecord: CKRecord
    ) -> ConflictResolution {
        print("冲突解决: 采用本地数据优先策略")
        
        // 保持本地数据不变，需要同步到远程
        return ConflictResolution(
            strategy: .localWins,
            resolvedData: localEntity,
            shouldSync: true,
            error: nil
        )
    }
    
    /// 远程优先策略
    private func resolveWithRemoteWins<T: NSManagedObject & CloudKitConvertible>(
        localEntity: T,
        remoteRecord: CKRecord
    ) -> ConflictResolution {
        print("冲突解决: 采用远程数据优先策略")
        
        // 使用远程数据更新本地实体
        guard let updatedEntity = updateLocalEntityFromRemote(localEntity, remoteRecord: remoteRecord) else {
            return ConflictResolution(
                strategy: .remoteWins,
                resolvedData: nil,
                shouldSync: false,
                error: ConflictResolutionError.updateFailed
            )
        }
        
        return ConflictResolution(
            strategy: .remoteWins,
            resolvedData: updatedEntity,
            shouldSync: false,
            error: nil
        )
    }
    
    /// 最新修改优先策略
    private func resolveWithNewestWins<T: NSManagedObject & CloudKitConvertible>(
        localEntity: T,
        remoteRecord: CKRecord
    ) -> ConflictResolution {
        print("冲突解决: 采用最新修改优先策略")
        
        let localUpdateTime = localEntity.value(forKey: "updatedAt") as? Date ?? Date.distantPast
        let remoteUpdateTime = remoteRecord.modificationDate ?? Date.distantPast
        
        if localUpdateTime > remoteUpdateTime {
            // 本地更新时间较新
            return resolveWithLocalWins(localEntity: localEntity, remoteRecord: remoteRecord)
        } else {
            // 远程更新时间较新
            return resolveWithRemoteWins(localEntity: localEntity, remoteRecord: remoteRecord)
        }
    }
    
    /// 用户选择策略
    private func resolveWithUserChoice<T: NSManagedObject & CloudKitConvertible>(
        localEntity: T,
        remoteRecord: CKRecord
    ) -> ConflictResolution {
        print("冲突解决: 需要用户选择解决策略")
        
        // 这里应该弹出用户界面让用户选择
        // 暂时使用最新修改优先作为默认策略
        return resolveWithNewestWins(localEntity: localEntity, remoteRecord: remoteRecord)
    }
    
    /// 字段级别合并策略
    private func resolveWithFieldMerge<T: NSManagedObject & CloudKitConvertible>(
        localEntity: T,
        remoteRecord: CKRecord
    ) -> ConflictResolution {
        print("冲突解决: 采用字段级别合并策略")
        
        // 实现智能字段合并逻辑
        guard let mergedEntity = mergeEntityFields(localEntity, remoteRecord: remoteRecord) else {
            return ConflictResolution(
                strategy: .mergeFields,
                resolvedData: nil,
                shouldSync: false,
                error: ConflictResolutionError.mergeFailed
            )
        }
        
        return ConflictResolution(
            strategy: .mergeFields,
            resolvedData: mergedEntity,
            shouldSync: true,
            error: nil
        )
    }
    
    // MARK: - 辅助方法
    
    /// 使用远程记录更新本地实体
    private func updateLocalEntityFromRemote<T: NSManagedObject & CloudKitConvertible>(
        _ localEntity: T,
        remoteRecord: CKRecord
    ) -> T? {
        
        // 根据实体类型进行特定的更新逻辑
        switch localEntity {
        case let user as User:
            return updateUserFromRemote(user, remoteRecord: remoteRecord) as? T
        case let plan as Plan:
            return updatePlanFromRemote(plan, remoteRecord: remoteRecord) as? T
        case let task as Task:
            return updateTaskFromRemote(task, remoteRecord: remoteRecord) as? T
        case let subtask as Subtask:
            return updateSubtaskFromRemote(subtask, remoteRecord: remoteRecord) as? T
        case let session as PomodoroSession:
            return updateSessionFromRemote(session, remoteRecord: remoteRecord) as? T
        default:
            print("未支持的实体类型: \(type(of: localEntity))")
            return nil
        }
    }
    
    /// 字段级别合并
    private func mergeEntityFields<T: NSManagedObject & CloudKitConvertible>(
        _ localEntity: T,
        remoteRecord: CKRecord
    ) -> T? {
        
        let localUpdateTime = localEntity.value(forKey: "updatedAt") as? Date ?? Date.distantPast
        let remoteUpdateTime = remoteRecord.modificationDate ?? Date.distantPast
        
        // 对每个字段单独比较修改时间并选择较新的值
        // 这里简化实现，实际应用中可能需要更复杂的字段级别时间戳
        
        switch localEntity {
        case let user as User:
            return mergeUserFields(user, remoteRecord: remoteRecord, localTime: localUpdateTime, remoteTime: remoteUpdateTime) as? T
        case let plan as Plan:
            return mergePlanFields(plan, remoteRecord: remoteRecord, localTime: localUpdateTime, remoteTime: remoteUpdateTime) as? T
        case let task as Task:
            return mergeTaskFields(task, remoteRecord: remoteRecord, localTime: localUpdateTime, remoteTime: remoteUpdateTime) as? T
        default:
            // 对于复杂合并，回退到最新优先策略
            if localUpdateTime > remoteUpdateTime {
                return localEntity
            } else {
                return updateLocalEntityFromRemote(localEntity, remoteRecord: remoteRecord)
            }
        }
    }
    
    // MARK: - 特定实体更新方法
    
    private func updateUserFromRemote(_ user: User, remoteRecord: CKRecord) -> User {
        user.name = remoteRecord[CloudKitSchema.UserFields.name.rawValue] as? String ?? user.name
        user.avatar = remoteRecord[CloudKitSchema.UserFields.avatar.rawValue] as? String
        user.checkInDays = Int32(remoteRecord[CloudKitSchema.UserFields.checkInDays.rawValue] as? Int ?? Int(user.checkInDays))
        user.updatedAt = remoteRecord[CloudKitSchema.UserFields.updatedAt.rawValue] as? Date ?? Date()
        return user
    }
    
    private func updatePlanFromRemote(_ plan: Plan, remoteRecord: CKRecord) -> Plan {
        plan.title = remoteRecord[CloudKitSchema.PlanFields.title.rawValue] as? String ?? plan.title
        plan.planDescription = remoteRecord[CloudKitSchema.PlanFields.planDescription.rawValue] as? String
        plan.status = remoteRecord[CloudKitSchema.PlanFields.status.rawValue] as? String ?? plan.status
        plan.progress = remoteRecord[CloudKitSchema.PlanFields.progress.rawValue] as? Float ?? plan.progress
        plan.startDate = remoteRecord[CloudKitSchema.PlanFields.startDate.rawValue] as? Date ?? plan.startDate
        plan.endDate = remoteRecord[CloudKitSchema.PlanFields.endDate.rawValue] as? Date
        plan.totalTomatoes = Int32(remoteRecord[CloudKitSchema.PlanFields.totalTomatoes.rawValue] as? Int ?? Int(plan.totalTomatoes))
        plan.updatedAt = remoteRecord[CloudKitSchema.PlanFields.updatedAt.rawValue] as? Date ?? Date()
        return plan
    }
    
    private func updateTaskFromRemote(_ task: Task, remoteRecord: CKRecord) -> Task {
        task.title = remoteRecord[CloudKitSchema.TaskFields.title.rawValue] as? String ?? task.title
        task.startTime = remoteRecord[CloudKitSchema.TaskFields.startTime.rawValue] as? Date ?? task.startTime
        task.endTime = remoteRecord[CloudKitSchema.TaskFields.endTime.rawValue] as? Date ?? task.endTime
        task.priority = remoteRecord[CloudKitSchema.TaskFields.priority.rawValue] as? String ?? task.priority
        task.tomatoCount = Int32(remoteRecord[CloudKitSchema.TaskFields.tomatoCount.rawValue] as? Int ?? Int(task.tomatoCount))
        task.isCompleted = remoteRecord[CloudKitSchema.TaskFields.isCompleted.rawValue] as? Bool ?? task.isCompleted
        task.isReminderEnabled = remoteRecord[CloudKitSchema.TaskFields.isReminderEnabled.rawValue] as? Bool ?? task.isReminderEnabled
        task.updatedAt = remoteRecord[CloudKitSchema.TaskFields.updatedAt.rawValue] as? Date ?? Date()
        return task
    }
    
    private func updateSubtaskFromRemote(_ subtask: Subtask, remoteRecord: CKRecord) -> Subtask {
        subtask.title = remoteRecord[CloudKitSchema.SubtaskFields.title.rawValue] as? String ?? subtask.title
        subtask.isCompleted = remoteRecord[CloudKitSchema.SubtaskFields.isCompleted.rawValue] as? Bool ?? subtask.isCompleted
        subtask.updatedAt = remoteRecord[CloudKitSchema.SubtaskFields.updatedAt.rawValue] as? Date ?? Date()
        return subtask
    }
    
    private func updateSessionFromRemote(_ session: PomodoroSession, remoteRecord: CKRecord) -> PomodoroSession {
        session.sessionType = remoteRecord[CloudKitSchema.PomodoroSessionFields.sessionType.rawValue] as? String ?? session.sessionType
        session.startTime = remoteRecord[CloudKitSchema.PomodoroSessionFields.startTime.rawValue] as? Date ?? session.startTime
        session.endTime = remoteRecord[CloudKitSchema.PomodoroSessionFields.endTime.rawValue] as? Date
        session.duration = remoteRecord[CloudKitSchema.PomodoroSessionFields.duration.rawValue] as? Double ?? session.duration
        session.isCompleted = remoteRecord[CloudKitSchema.PomodoroSessionFields.isCompleted.rawValue] as? Bool ?? session.isCompleted
        return session
    }
    
    // MARK: - 字段级别合并方法
    
    private func mergeUserFields(_ user: User, remoteRecord: CKRecord, localTime: Date, remoteTime: Date) -> User {
        // 智能合并用户字段
        // 姓名：选择非空且较新的值
        if let remoteName = remoteRecord[CloudKitSchema.UserFields.name.rawValue] as? String,
           !remoteName.isEmpty && remoteTime > localTime {
            user.name = remoteName
        }
        
        // 头像：选择较新的值
        if remoteTime > localTime {
            user.avatar = remoteRecord[CloudKitSchema.UserFields.avatar.rawValue] as? String
        }
        
        // 签到天数：选择较大的值
        let remoteCheckInDays = remoteRecord[CloudKitSchema.UserFields.checkInDays.rawValue] as? Int ?? 0
        user.checkInDays = max(user.checkInDays, Int32(remoteCheckInDays))
        
        user.updatedAt = max(localTime, remoteTime)
        return user
    }
    
    private func mergePlanFields(_ plan: Plan, remoteRecord: CKRecord, localTime: Date, remoteTime: Date) -> Plan {
        // 智能合并计划字段
        // 进度：选择较高的值
        let remoteProgress = remoteRecord[CloudKitSchema.PlanFields.progress.rawValue] as? Float ?? 0
        plan.progress = max(plan.progress, remoteProgress)
        
        // 番茄钟总数：选择较大的值
        let remoteTomatoes = remoteRecord[CloudKitSchema.PlanFields.totalTomatoes.rawValue] as? Int ?? 0
        plan.totalTomatoes = max(plan.totalTomatoes, Int32(remoteTomatoes))
        
        // 其他字段根据时间选择
        if remoteTime > localTime {
            plan.title = remoteRecord[CloudKitSchema.PlanFields.title.rawValue] as? String ?? plan.title
            plan.planDescription = remoteRecord[CloudKitSchema.PlanFields.planDescription.rawValue] as? String
            plan.status = remoteRecord[CloudKitSchema.PlanFields.status.rawValue] as? String ?? plan.status
        }
        
        plan.updatedAt = max(localTime, remoteTime)
        return plan
    }
    
    private func mergeTaskFields(_ task: Task, remoteRecord: CKRecord, localTime: Date, remoteTime: Date) -> Task {
        // 智能合并任务字段
        // 完成状态：优先选择已完成状态
        let remoteCompleted = remoteRecord[CloudKitSchema.TaskFields.isCompleted.rawValue] as? Bool ?? false
        task.isCompleted = task.isCompleted || remoteCompleted
        
        // 其他字段根据时间选择
        if remoteTime > localTime {
            task.title = remoteRecord[CloudKitSchema.TaskFields.title.rawValue] as? String ?? task.title
            task.priority = remoteRecord[CloudKitSchema.TaskFields.priority.rawValue] as? String ?? task.priority
            task.tomatoCount = Int32(remoteRecord[CloudKitSchema.TaskFields.tomatoCount.rawValue] as? Int ?? Int(task.tomatoCount))
        }
        
        task.updatedAt = max(localTime, remoteTime)
        return task
    }
}

// MARK: - 冲突解决错误
enum ConflictResolutionError: LocalizedError {
    case updateFailed
    case mergeFailed
    case unsupportedEntityType
    case dataCorruption
    
    var errorDescription: String? {
        switch self {
        case .updateFailed:
            return "实体更新失败"
        case .mergeFailed:
            return "数据合并失败"
        case .unsupportedEntityType:
            return "不支持的实体类型"
        case .dataCorruption:
            return "数据损坏"
        }
    }
}