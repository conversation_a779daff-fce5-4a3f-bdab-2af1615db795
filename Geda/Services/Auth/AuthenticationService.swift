//
//  AuthenticationService.swift
//  Geda
//
//  Created by AI Assistant on 2025-07-22.
//

import Foundation
import AuthenticationServices
import Combine
import SwiftUI

/// Apple ID认证服务
class AuthenticationService: NSObject, ObservableObject {
    
    // MARK: - 单例
    static let shared = AuthenticationService()
    
    // MARK: - Published属性
    @Published var isAuthenticated = false
    @Published var currentUser: AuthenticatedUser?
    @Published var authenticationState: AuthenticationState = .unknown
    
    // MARK: - 开发测试模式
    @Published var testModeEnabled = false  // 测试模式开关
    
    // MARK: - 私有属性
    private var cancellables = Set<AnyCancellable>()
    private let userDefaults = UserDefaults.standard
    
    // MARK: - 认证状态枚举
    enum AuthenticationState {
        case unknown
        case authenticated(AuthenticatedUser)
        case notAuthenticated
        case authenticating
        case error(Error)
    }
    
    // MARK: - 认证用户模型
    struct AuthenticatedUser: Codable {
        let appleUserID: String
        let email: String?
        let fullName: PersonNameComponents?
        let isVerified: Bool
        let authorizationCode: Data?
        let identityToken: Data?
        let authenticatedAt: Date
        
        init(credential: ASAuthorizationAppleIDCredential) {
            self.appleUserID = credential.user
            self.email = credential.email
            self.fullName = credential.fullName
            self.isVerified = true
            self.authorizationCode = credential.authorizationCode
            self.identityToken = credential.identityToken
            self.authenticatedAt = Date()
        }
        
        // 测试模式初始化器
        init(appleUserID: String, email: String?, fullName: PersonNameComponents?, isVerified: Bool, authorizationCode: Data?, identityToken: Data?, authenticatedAt: Date) {
            self.appleUserID = appleUserID
            self.email = email
            self.fullName = fullName
            self.isVerified = isVerified
            self.authorizationCode = authorizationCode
            self.identityToken = identityToken
            self.authenticatedAt = authenticatedAt
        }
        
        var displayName: String {
            if let fullName = fullName {
                let formatter = PersonNameComponentsFormatter()
                formatter.style = .medium
                return formatter.string(from: fullName)
            } else if let email = email {
                return email
            } else {
                return "用户"
            }
        }
    }
    
    // MARK: - 初始化
    override init() {
        super.init()
        
        // 检查是否启用测试模式
        testModeEnabled = userDefaults.bool(forKey: "GedaTestModeEnabled")
        
        checkAuthenticationState()
    }
    
    // MARK: - 公共方法
    
    /// 启动Apple ID登录流程
    func signInWithApple() {
        authenticationState = .authenticating
        
        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]
        
        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.delegate = self
        authorizationController.presentationContextProvider = self
        authorizationController.performRequests()
    }
    
    /// 静默登录检查
    func checkAuthenticationState() {
        // 如果测试模式启用，直接跳过认证检查
        if testModeEnabled {
            enableTestMode()
            return
        }
        
        // 检查本地存储的用户信息
        if let userData = userDefaults.data(forKey: "authenticatedUser"),
           let user = try? JSONDecoder().decode(AuthenticatedUser.self, from: userData) {
            
            // 验证Apple ID状态
            let provider = ASAuthorizationAppleIDProvider()
            provider.getCredentialState(forUserID: user.appleUserID) { [weak self] credentialState, error in
                DispatchQueue.main.async {
                    switch credentialState {
                    case .authorized:
                        self?.setAuthenticatedUser(user)
                    case .revoked, .notFound:
                        self?.signOut()
                    case .transferred:
                        // 处理用户ID转移情况
                        self?.handleUserIDTransfer()
                    @unknown default:
                        self?.authenticationState = .notAuthenticated
                    }
                }
            }
        } else {
            authenticationState = .notAuthenticated
        }
    }
    
    /// 退出登录
    func signOut() {
        currentUser = nil
        isAuthenticated = false
        authenticationState = .notAuthenticated
        
        // 清除本地存储
        userDefaults.removeObject(forKey: "authenticatedUser")
        
        // 清除Core Data用户数据（可选）
        // CoreDataManager.shared.deleteAllData()
        
        // 发送登出通知
        NotificationCenter.default.post(name: .userDidSignOut, object: nil)
    }
    
    /// 启用测试模式（绕过认证）
    func enableTestMode() {
        testModeEnabled = true
        userDefaults.set(true, forKey: "GedaTestModeEnabled")
        isAuthenticated = true  // 在测试模式下标记为已认证
        
        // 创建测试用户
        let testUser = AuthenticatedUser(
            appleUserID: "test_user_id",
            email: "<EMAIL>", 
            fullName: PersonNameComponents(),
            isVerified: true,
            authorizationCode: nil,
            identityToken: nil,
            authenticatedAt: Date()
        )
        
        currentUser = testUser
        authenticationState = .authenticated(testUser)
        
        print("测试模式已启用 - 认证已绕过")
    }
    
    /// 禁用测试模式
    func disableTestMode() {
        testModeEnabled = false
        userDefaults.set(false, forKey: "GedaTestModeEnabled")
        
        // 恢复正常认证流程
        signOut()
        checkAuthenticationState()
        
        print("测试模式已禁用 - 恢复正常认证")
    }
    
    /// 获取当前认证状态
    func getAuthenticationStatus() -> AnyPublisher<AuthenticationState, Never> {
        return $authenticationState.eraseToAnyPublisher()
    }
    
    // MARK: - 私有方法
    
    private func setAuthenticatedUser(_ user: AuthenticatedUser) {
        currentUser = user
        isAuthenticated = true
        authenticationState = .authenticated(user)
        
        // 保存到本地存储
        if let userData = try? JSONEncoder().encode(user) {
            userDefaults.set(userData, forKey: "authenticatedUser")
        }
        
        // 发送登录成功通知
        NotificationCenter.default.post(name: .userDidSignIn, object: user)
    }
    
    private func handleUserIDTransfer() {
        // 处理Apple ID转移情况
        // 通常需要重新认证
        signOut()
    }
    
    private func handleAuthenticationError(_ error: Error) {
        authenticationState = .error(error)
        print("认证错误: \(error.localizedDescription)")
    }
}

// MARK: - ASAuthorizationControllerDelegate
extension AuthenticationService: ASAuthorizationControllerDelegate {
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        switch authorization.credential {
        case let appleIDCredential as ASAuthorizationAppleIDCredential:
            let user = AuthenticatedUser(credential: appleIDCredential)
            setAuthenticatedUser(user)
            
        case let passwordCredential as ASPasswordCredential:
            // 处理密码凭据（如果需要）
            print("Password credential: \(passwordCredential.user)")
            
        default:
            handleAuthenticationError(AuthenticationError.unsupportedCredential)
        }
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        handleAuthenticationError(error)
    }
}

// MARK: - ASAuthorizationControllerPresentationContextProviding
extension AuthenticationService: ASAuthorizationControllerPresentationContextProviding {
    
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        // 获取当前窗口
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return UIWindow()
        }
        return window
    }
}

// MARK: - 认证错误
enum AuthenticationError: LocalizedError {
    case unsupportedCredential
    case invalidCredential
    case userCancelled
    case networkError
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .unsupportedCredential:
            return "不支持的凭据类型"
        case .invalidCredential:
            return "无效的凭据"
        case .userCancelled:
            return "用户取消了认证"
        case .networkError:
            return "网络连接错误"
        case .unknownError:
            return "未知认证错误"
        }
    }
}

// MARK: - 通知名称
extension Notification.Name {
    static let userDidSignIn = Notification.Name("userDidSignIn")
    static let userDidSignOut = Notification.Name("userDidSignOut")
    static let authenticationStateChanged = Notification.Name("authenticationStateChanged")
}

// MARK: - SwiftUI视图扩展
extension View {
    /// 在用户未认证时显示登录界面
    func requireAuthentication() -> some View {
        modifier(AuthenticationRequiredModifier())
    }
}

struct AuthenticationRequiredModifier: ViewModifier {
    @StateObject private var authService = AuthenticationService.shared
    
    func body(content: Content) -> some View {
        Group {
            if authService.isAuthenticated {
                content
            } else {
                SignInView()
            }
        }
    }
}