/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/BaseViewModel.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/FocusTimerViewModel.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/HomeViewModel.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/PlanViewModel.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/ReviewViewModel.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/TaskViewModel.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/Enums.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PlanRepository.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/TaskRepository.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/UserRepository.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/CoreDataManager.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/DesignTokens.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/CalendarViewModel.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/ProfileViewModel.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/ScannerViewModel.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GedaCard.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GradientButton.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskCard.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskRowView.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/FocusTimerView.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/PlanView.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskDetailView.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskEditView.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/ModalPresentationModifier.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/NewTaskModal.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift
