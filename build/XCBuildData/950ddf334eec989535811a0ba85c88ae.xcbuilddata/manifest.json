{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/EagerLinkingTBDs/Release-iphoneos": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/EagerLinkingTBDs/Release-iphoneos", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/_CodeSignature", "/var/folders/wx/n1w_lfdd1mz8ty9fr6ms0k1w0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache", "<target-Geda-****************************************************************--begin-scanning>", "<target-Geda-****************************************************************--end>", "<target-Geda-****************************************************************--linker-inputs-ready>", "<target-Geda-****************************************************************--modules-ready>", "<workspace-none--stale-file-removal>"], "outputs": ["<all>"]}, "<target-Geda-****************************************************************-Release-iphoneos--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/_CodeSignature", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Assets.car", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/README.md", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda.momd", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM/Contents/Resources/DWARF/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/PkgInfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/embedded.mobileprovision", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent.der", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-project-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/empty-Geda.plist"], "roots": ["/tmp/Geda.dst", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build"], "outputs": ["<target-Geda-****************************************************************-Release-iphoneos--arm64-build-headers-stale-file-removal>"]}, "<workspace-none--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphoneos/all-product-headers.yaml"], "outputs": ["<workspace-none--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk /var/folders/wx/n1w_lfdd1mz8ty9fr6ms0k1w0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk /var/folders/wx/n1w_lfdd1mz8ty9fr6ms0k1w0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache", "inputs": [], "outputs": ["/var/folders/wx/n1w_lfdd1mz8ty9fr6ms0k1w0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache", "<ClangStatCache /var/folders/wx/n1w_lfdd1mz8ty9fr6ms0k1w0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk", "-o", "/var/folders/wx/n1w_lfdd1mz8ty9fr6ms0k1w0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda.xcodeproj", "signature": "ccb2b12c88ce431fd409f2b1dd73ba56"}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/EagerLinkingTBDs/Release-iphoneos": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/EagerLinkingTBDs/Release-iphoneos", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/EagerLinkingTBDs/Release-iphoneos>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/EagerLinkingTBDs/Release-iphoneos"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos"]}, "P0:::Gate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM-target-Geda-****************************************************************-": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM/Contents/Resources/DWARF/Geda", "<GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM/Contents/Resources/DWARF/Geda>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM/"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphoneos/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-Geda-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Metadata.appintents>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList"], "outputs": ["<target-Geda-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-ChangePermissions>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-StripSymbols>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-GenerateStubAPI>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-CodeSign>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-Validate>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-CopyAside>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-Geda-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--GeneratedFilesTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ProductStructureTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/embedded.mobileprovision", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent.der", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Entitlements.plist"], "outputs": ["<target-Geda-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--RealityAssetsTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-project-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.hmap"], "outputs": ["<target-Geda-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/PkgInfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/empty-Geda.plist"], "outputs": ["<target-Geda-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--HeadermapTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleMapTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Geda-****************************************************************--InfoPlistTaskProducer>", "<target-Geda-****************************************************************--VersionPlistTaskProducer>", "<target-Geda-****************************************************************--SanitizerTaskProducer>", "<target-Geda-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Geda-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Geda-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Geda-****************************************************************--StubBinaryTaskProducer>", "<target-Geda-****************************************************************--TestTargetTaskProducer>", "<target-Geda-****************************************************************--TestHostTaskProducer>", "<target-Geda-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Geda-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Geda-****************************************************************--DocumentationTaskProducer>", "<target-Geda-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--start>", "<target-Geda-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--VersionPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--VersionPlistTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-Geda-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Assets.car", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/README.md", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda.momd", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json"], "outputs": ["<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-Geda-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-Geda-****************************************************************--generated-headers>"]}, "P0:::Gate target-Geda-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Geda-Swift.h"], "outputs": ["<target-Geda-****************************************************************--swift-generated-headers>"]}, "P0:target-Geda-****************************************************************-::AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Metadata.appintents>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Info.plist", "--temp-dir-path", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/ssu", "--bundle-id", "com.ryan.Geda", "--product-path", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "--extracted-metadata-path", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Metadata.appintents", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "35d28e2e7a8fb8352e5f03aa35243ead"}, "P0:target-Geda-****************************************************************-::CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/Enums.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/BaseViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/FocusTimerViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/HomeViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/PlanViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/ReviewViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/TaskViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/README.md/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/CoreDataManager.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PlanRepository.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/TaskRepository.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/UserRepository.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/DesignTokens.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/CalendarViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/ProfileViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/ScannerViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GedaCard.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GradientButton.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskCard.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskRowView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/FocusTimerView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/PlanView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskDetailView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskEditView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/ModalPresentationModifier.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/NewTaskModal.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Info.plist/", "<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda normal>", "<TRIGGER: MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/_CodeSignature", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"]}, "P0:target-Geda-****************************************************************-::CompileAssetCatalog /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalog /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Assets.car"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphoneos", "--compile", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "control-enabled": false, "deps": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/assetcatalog_dependencies"], "deps-style": "dependency-info", "signature": "442b391d92b404a11d2021fcc0a6d9a5"}, "P0:target-Geda-****************************************************************-::CopySwiftLibs /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"], "deps": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-Geda-****************************************************************-::CpResource /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/README.md /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/README.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/README.md /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/README.md", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/README.md/", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/README.md"]}, "P0:target-Geda-****************************************************************-::DataModelCodegen /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld": {"tool": "shell", "description": "DataModelCodegen /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld/", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/momc", "--action", "generate", "--swift-version", "5.0", "--sdk<PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk", "--iphoneos-deployment-target", "18.2", "--module", "<PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "ef8970b00da15fdda87ae8e2abbabf37"}, "P0:target-Geda-****************************************************************-::DataModelCompile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/ /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld": {"tool": "shell", "description": "DataModelCompile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/ /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda.momd"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/momc", "--sdk<PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk", "--iphoneos-deployment-target", "18.2", "--module", "<PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "control-enabled": false, "signature": "9317a18c89c922af38bae3a9350834ea"}, "P0:target-Geda-****************************************************************-::ExtractAppIntentsMetadata": {"tool": "appintents-metadata", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/BaseViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/FocusTimerViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/HomeViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/PlanViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/ReviewViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/TaskViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/Enums.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PlanRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/TaskRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/UserRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/CoreDataManager.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/DesignTokens.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/CalendarViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/ProfileViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/ScannerViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GedaCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GradientButton.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskRowView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/FocusTimerView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/PlanView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskDetailView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskEditView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/ModalPresentationModifier.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/NewTaskModal.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Metadata.appintents>"]}, "P0:target-Geda-****************************************************************-::Gate target-Geda-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Geda.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-Geda-****************************************************************--begin-compiling>"]}, "P0:target-Geda-****************************************************************-::Gate target-Geda-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Geda.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-Geda-****************************************************************--begin-linking>"]}, "P0:target-Geda-****************************************************************-::Gate target-Geda-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Geda.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/EagerLinkingTBDs/Release-iphoneos>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--begin-scanning>"]}, "P0:target-Geda-****************************************************************-::Gate target-Geda-****************************************************************--end": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--entry>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Assets.car", "<CopySwiftStdlib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/README.md", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda.momd", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Metadata.appintents>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "<GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM/Contents/Resources/DWARF/Geda>", "<GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM/Contents/Resources/DWARF/Geda>", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/PkgInfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/embedded.mobileprovision", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished", "<Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>", "<Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-project-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/empty-Geda.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM/", "<target-Geda-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Geda-****************************************************************--Barrier-ChangePermissions>", "<target-Geda-****************************************************************--Barrier-CodeSign>", "<target-Geda-****************************************************************--Barrier-CopyAside>", "<target-Geda-****************************************************************--Barrier-GenerateStubAPI>", "<target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Geda-****************************************************************--Barrier-RegisterProduct>", "<target-Geda-****************************************************************--Barrier-StripSymbols>", "<target-Geda-****************************************************************--Barrier-Validate>", "<target-Geda-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Geda-****************************************************************--DocumentationTaskProducer>", "<target-Geda-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Geda-****************************************************************--GeneratedFilesTaskProducer>", "<target-Geda-****************************************************************--HeadermapTaskProducer>", "<target-Geda-****************************************************************--InfoPlistTaskProducer>", "<target-Geda-****************************************************************--ModuleMapTaskProducer>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Geda-****************************************************************--ProductStructureTaskProducer>", "<target-Geda-****************************************************************--RealityAssetsTaskProducer>", "<target-Geda-****************************************************************--SanitizerTaskProducer>", "<target-Geda-****************************************************************--StubBinaryTaskProducer>", "<target-Geda-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Geda-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Geda-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Geda-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Geda-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Geda-****************************************************************--TestHostTaskProducer>", "<target-Geda-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-Geda-****************************************************************--TestTargetTaskProducer>", "<target-Geda-****************************************************************--VersionPlistTaskProducer>", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--generated-headers>", "<target-Geda-****************************************************************--swift-generated-headers>"], "outputs": ["<target-Geda-****************************************************************--end>"]}, "P0:target-Geda-****************************************************************-::Gate target-Geda-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Geda.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/EagerLinkingTBDs/Release-iphoneos>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--entry>"]}, "P0:target-Geda-****************************************************************-::Gate target-Geda-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Release-iphoneos--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Geda.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/EagerLinkingTBDs/Release-iphoneos>"], "outputs": ["<target-Geda-****************************************************************--immediate>"]}, "P0:target-Geda-****************************************************************-::Gate target-Geda-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.LinkFileList"], "outputs": ["<target-Geda-****************************************************************--linker-inputs-ready>"]}, "P0:target-Geda-****************************************************************-::Gate target-Geda-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Geda-Swift.h"], "outputs": ["<target-Geda-****************************************************************--modules-ready>"]}, "P0:target-Geda-****************************************************************-::Gate target-Geda-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Assets.car", "<CopySwiftStdlib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda.momd", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Metadata.appintents>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "<GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM/Contents/Resources/DWARF/Geda>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/embedded.mobileprovision", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent.der", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "<target-Geda-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-Geda-****************************************************************--unsigned-product-ready>"]}, "P0:target-Geda-****************************************************************-::Gate target-Geda-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-Geda-****************************************************************--will-sign>"]}, "P0:target-Geda-****************************************************************-::GenerateAssetSymbols /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets/", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.h"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphoneos", "--compile", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets", "--bundle-identifier", "com.ryan.Geda", "--generate-swift-asset-symbols", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "control-enabled": false, "signature": "846388c77db51354fb52ced3139595ab"}, "P0:target-Geda-****************************************************************-::GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda": {"tool": "shell", "description": "GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Info.plist", "<Linked Binary /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftmodule", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM/Contents/Resources/DWARF/Geda", "<GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM/Contents/Resources/DWARF/Geda>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/dsymutil", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda", "-o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app.dSYM"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "2078eaf1d8c9ebb8279acfdde6ca877f"}, "P0:target-Geda-****************************************************************-::MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "inputs": ["<target-Geda-****************************************************************--start>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>", "<TRIGGER: MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"]}, "P0:target-Geda-****************************************************************-::ProcessInfoPlistFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Info.plist /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/empty-Geda.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Info.plist /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/empty-Geda.plist", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/empty-Geda.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/assetcatalog_generated_info.plist", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/PkgInfo"]}, "P0:target-Geda-****************************************************************-::ProcessProductPackaging  /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Entitlements.plist", "<target-Geda-****************************************************************--ProductStructureTaskProducer>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent"]}, "P0:target-Geda-****************************************************************-::ProcessProductPackaging /Users/<USER>/Library/Developer/Xcode/UserData/Provisioning Profiles/cc31df16-6438-4f06-a1b5-a8e8345111a8.mobileprovision /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/embedded.mobileprovision": {"tool": "process-product-provisioning-profile", "description": "ProcessProductPackaging /Users/<USER>/Library/Developer/Xcode/UserData/Provisioning Profiles/cc31df16-6438-4f06-a1b5-a8e8345111a8.mobileprovision /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/embedded.mobileprovision", "inputs": ["/Users/<USER>/Library/Developer/Xcode/UserData/Provisioning Profiles/cc31df16-6438-4f06-a1b5-a8e8345111a8.mobileprovision", "<target-Geda-****************************************************************--ProductStructureTaskProducer>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/embedded.mobileprovision"]}, "P0:target-Geda-****************************************************************-::ProcessProductPackagingDER /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent.der", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent", "<target-Geda-****************************************************************--ProductStructureTaskProducer>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent", "-o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.app.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "cb063b1e2111fd6e6c4e8b2f830669ad"}, "P0:target-Geda-****************************************************************-::RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "<target-Geda-****************************************************************--Barrier-CodeSign>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"]}, "P0:target-Geda-****************************************************************-::SwiftDriver Compilation Geda normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation Geda normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/BaseViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/FocusTimerViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/HomeViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/PlanViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/ReviewViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/TaskViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/Enums.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PlanRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/TaskRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/UserRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/CoreDataManager.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/DesignTokens.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/CalendarViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/ProfileViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/ScannerViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GedaCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GradientButton.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskRowView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/FocusTimerView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/PlanView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskDetailView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskEditView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/ModalPresentationModifier.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/NewTaskModal.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-project-headers.hmap", "<ClangStatCache /var/folders/wx/n1w_lfdd1mz8ty9fr6ms0k1w0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache>", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished"]}, "P0:target-Geda-****************************************************************-::Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app": {"tool": "shell", "description": "Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "<target-Geda-****************************************************************--Barrier-Validate>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "ceaeb79ae500771ff0d659269a622e98"}, "P0:target-Geda-****************************************************************-::Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Info.plist", "<target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"], "outputs": ["<Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app>"]}, "P0:target-Geda-****************************************************************-::ValidateDevelopmentAssets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content", "<target-Geda-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphoneos/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphoneos/all-product-headers.yaml", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphoneos/all-product-headers.yaml"]}, "P2:target-Geda-****************************************************************-::Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo/", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo"]}, "P2:target-Geda-****************************************************************-::Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.abi.json /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.abi.json /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.abi.json", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.abi.json/", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.abi.json"]}, "P2:target-Geda-****************************************************************-::Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftdoc /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftdoc /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftdoc/", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftdoc"]}, "P2:target-Geda-****************************************************************-::Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftmodule /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftmodule /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftmodule/", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.swiftmodule/arm64-apple-ios.swiftmodule"]}, "P2:target-Geda-****************************************************************-::Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda normal", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos", "<target-Geda-****************************************************************--generated-headers>", "<target-Geda-****************************************************************--swift-generated-headers>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda", "<Linked Binary /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk", "-<PERSON><PERSON>", "-L/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/EagerLinkingTBDs/Release-iphoneos", "-L/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos", "-F/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/EagerLinkingTBDs/Release-iphoneos", "-F/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos", "-filelist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_lto.o", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "-o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Release-iphoneos/Geda.app/Geda"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "deps": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat"], "deps-style": "dependency-info", "signature": "b3f5090f71c07c7e1909b27e0df28799"}, "P2:target-Geda-****************************************************************-::SwiftDriver Compilation Requirements Geda normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements Geda normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/BaseViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/FocusTimerViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/HomeViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/PlanViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/ReviewViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/TaskViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/Enums.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PlanRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/TaskRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/UserRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/CoreDataManager.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/DesignTokens.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/CalendarViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/ProfileViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/ScannerViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GedaCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GradientButton.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskRowView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/FocusTimerView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/PlanView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskDetailView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskEditView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/ModalPresentationModifier.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/NewTaskModal.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-project-headers.hmap", "<ClangStatCache /var/folders/wx/n1w_lfdd1mz8ty9fr6ms0k1w0000gn/C/com.apple.DeveloperTools/16.2-16C5032a/Xcode/SDKStatCaches.noindex/iphoneos18.2-22C146-d5b9239ec3bf5b3adbecdf21472871e3.sdkstatcache>", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.swiftdoc"]}, "P2:target-Geda-****************************************************************-::SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Geda-Swift.h /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Geda-Swift.h /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-Swift.h", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-Swift.h", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Geda-Swift.h"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Entitlements.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Entitlements.plist", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/Entitlements.plist"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-non-framework-target-headers.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-non-framework-target-headers.hmap"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-target-headers.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-all-target-headers.hmap"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-generated-files.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-generated-files.hmap"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-own-target-headers.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-own-target-headers.hmap"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-project-headers.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda-project-headers.hmap"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.DependencyMetadataFileList", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.DependencyMetadataFileList"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Geda.hmap"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.LinkFileList"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda.SwiftFileList"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json"]}, "P2:target-Geda-****************************************************************-::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/empty-Geda.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/empty-Geda.plist", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/empty-Geda.plist"]}}}