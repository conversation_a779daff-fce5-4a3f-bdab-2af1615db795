{"": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.swiftdeps"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ContentView.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/Enums.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Enums.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/BaseViewModel.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/FocusTimerViewModel.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/HomeViewModel.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/PlanViewModel.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/ReviewViewModel.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/TaskViewModel.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/CoreDataManager.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PlanRepository.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/TaskRepository.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/UserRepository.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Constants.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/DesignTokens.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/CalendarViewModel.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GedaCard.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GradientButton.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskCard.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskRowView.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/FocusTimerView.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/HomeView.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/PlanView.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PlanView.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskDetailView.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskEditView.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/ModalPresentationModifier.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/NewTaskModal.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift": {"index-unit-output-path": "/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o"}}