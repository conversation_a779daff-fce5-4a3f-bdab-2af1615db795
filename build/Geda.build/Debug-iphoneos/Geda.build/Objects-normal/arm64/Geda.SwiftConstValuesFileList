/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ContentView.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Enums.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeView.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanView.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.swiftconstvalues
/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues
