"/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift":
  dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.d"
  const-values: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.swiftconstvalues"
  diagnostics: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.dia"
  swift-dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.swiftdeps"
  object: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o"
"/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift":
  dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.d"
  object: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o"
  diagnostics: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.dia"
  const-values: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.swiftdeps"
"/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift":
  object: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o"
  diagnostics: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.dia"
  dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.d"
  swift-dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.swiftdeps"
  const-values: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.swiftconstvalues"
"/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift":
  const-values: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.swiftconstvalues"
  object: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o"
  swift-dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.swiftdeps"
  dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.d"
  diagnostics: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.dia"
