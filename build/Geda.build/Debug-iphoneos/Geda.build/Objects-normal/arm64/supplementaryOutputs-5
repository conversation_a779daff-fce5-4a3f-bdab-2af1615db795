"/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift":
  diagnostics: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants.dia"
  dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants.d"
  object: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants.o"
  const-values: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants.swiftdeps"
"/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift":
  diagnostics: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.dia"
  dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.d"
  object: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o"
  swift-dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.swiftdeps"
  const-values: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.swiftconstvalues"
"/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift":
  dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.d"
  diagnostics: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.dia"
  swift-dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.swiftdeps"
  const-values: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.swiftconstvalues"
  object: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o"
"/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift":
  swift-dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.swiftdeps"
  dependencies: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.d"
  const-values: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.swiftconstvalues"
  diagnostics: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.dia"
  object: "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o"
