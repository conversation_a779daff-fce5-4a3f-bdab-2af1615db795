{"": {"diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Geda-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Geda-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Geda-master.swiftdeps"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ContentView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ContentView.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ContentView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ContentView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaApp.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaApp~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/Enums.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Enums.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Enums.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Enums.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Enums.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Enums.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Enums.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Enums.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Enums~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/BaseViewModel.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BaseViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/FocusTimerViewModel.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/HomeViewModel.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/PlanViewModel.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/ReviewViewModel.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/TaskViewModel.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/CoreDataManager.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CoreDataManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PlanRepository.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanRepository~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSessionRepository~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/RepositoryProtocols~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/SubtaskRepository~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/TaskRepository.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRepository~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/UserRepository.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/UserRepository.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/UserRepository~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Constants~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/DesignTokens.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/DesignTokens~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Color+Geda~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/LinearGradient+Geda~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Typography+Geda~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/View+Extensions~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/CalendarViewModel.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarViewModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/BottomActionBar~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CircularProgressView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CompactCalendar~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GedaCard.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaCard.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GedaCard~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GradientButton.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GradientButton.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GradientButton~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/StackedTaskCards~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskCard.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskCard.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskCard~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskRowView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskRowView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskSectionHeader~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TodayReminderCard~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TopNavigationSwitch~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/CalendarView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/FocusTimerView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/FocusTimerView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeView.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/HomeView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/PlanView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanView.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PlanView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ProfileView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ProfileView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ReviewView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ScannerView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ScannerView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskDetailView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskDetailView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskEditView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/TaskEditView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/ModalPresentationModifier.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/ModalPresentationModifier~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/NewTaskModal.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/NewTaskModal~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Geda+CoreDataModel~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/Task+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataClass~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/User+CoreDataProperties~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Debug-iphoneos/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}}