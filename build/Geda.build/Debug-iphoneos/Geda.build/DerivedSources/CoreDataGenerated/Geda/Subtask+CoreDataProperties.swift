//
//  Subtask+CoreDataProperties.swift
//  
//
//  Created by 杨瑞光 on 2025/7/14.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension Subtask {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<Subtask> {
        return NSFetchRequest<Subtask>(entityName: "Subtask")
    }

    @NSManaged public var createdAt: Date?
    @NSManaged public var id: String?
    @NSManaged public var isCompleted: Bool
    @NSManaged public var title: String?
    @NSManaged public var updatedAt: Date?
    @NSManaged public var task: Task?

}

extension Subtask : Identifiable {

}
