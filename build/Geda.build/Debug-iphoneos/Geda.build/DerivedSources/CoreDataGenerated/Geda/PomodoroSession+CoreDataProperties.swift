//
//  PomodoroSession+CoreDataProperties.swift
//  
//
//  Created by 杨瑞光 on 2025/7/14.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension PomodoroSession {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<PomodoroSession> {
        return NSFetchRequest<PomodoroSession>(entityName: "PomodoroSession")
    }

    @NSManaged public var createdAt: Date?
    @NSManaged public var duration: Double
    @NSManaged public var endTime: Date?
    @NSManaged public var id: String?
    @NSManaged public var isCompleted: Bool
    @NSManaged public var sessionType: String?
    @NSManaged public var startTime: Date?
    @NSManaged public var task: Task?

}

extension PomodoroSession : Identifiable {

}
