//
//  Task+CoreDataProperties.swift
//  
//
//  Created by 杨瑞光 on 2025/7/14.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension Task {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<Task> {
        return NSFetchRequest<Task>(entityName: "Task")
    }

    @NSManaged public var createdAt: Date?
    @NSManaged public var endTime: Date?
    @NSManaged public var id: String?
    @NSManaged public var isCompleted: Bool
    @NSManaged public var isReminderEnabled: Bool
    @NSManaged public var priority: String?
    @NSManaged public var startTime: Date?
    @NSManaged public var title: String?
    @NSManaged public var tomatoCount: Int32
    @NSManaged public var updatedAt: Date?
    @NSManaged public var plan: Plan?
    @NSManaged public var pomodoroSessions: NSSet?
    @NSManaged public var subtasks: NSSet?

}

// MARK: Generated accessors for pomodoroSessions
extension Task {

    @objc(addPomodoroSessionsObject:)
    @NSManaged public func addToPomodoroSessions(_ value: PomodoroSession)

    @objc(removePomodoroSessionsObject:)
    @NSManaged public func removeFromPomodoroSessions(_ value: PomodoroSession)

    @objc(addPomodoroSessions:)
    @NSManaged public func addToPomodoroSessions(_ values: NSSet)

    @objc(removePomodoroSessions:)
    @NSManaged public func removeFromPomodoroSessions(_ values: NSSet)

}

// MARK: Generated accessors for subtasks
extension Task {

    @objc(addSubtasksObject:)
    @NSManaged public func addToSubtasks(_ value: Subtask)

    @objc(removeSubtasksObject:)
    @NSManaged public func removeFromSubtasks(_ value: Subtask)

    @objc(addSubtasks:)
    @NSManaged public func addToSubtasks(_ values: NSSet)

    @objc(removeSubtasks:)
    @NSManaged public func removeFromSubtasks(_ values: NSSet)

}

extension Task : Identifiable {

}
