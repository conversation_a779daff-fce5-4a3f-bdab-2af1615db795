//
//  Plan+CoreDataProperties.swift
//  
//
//  Created by 杨瑞光 on 2025/7/14.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension Plan {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<Plan> {
        return NSFetchRequest<Plan>(entityName: "Plan")
    }

    @NSManaged public var createdAt: Date?
    @NSManaged public var endDate: Date?
    @NSManaged public var id: String?
    @NSManaged public var planDescription: String?
    @NSManaged public var progress: Float
    @NSManaged public var startDate: Date?
    @NSManaged public var status: String?
    @NSManaged public var title: String?
    @NSManaged public var totalTomatoes: Int32
    @NSManaged public var updatedAt: Date?
    @NSManaged public var tasks: NSSet?
    @NSManaged public var user: User?

}

// MARK: Generated accessors for tasks
extension Plan {

    @objc(addTasksObject:)
    @NSManaged public func addToTasks(_ value: Task)

    @objc(removeTasksObject:)
    @NSManaged public func removeFromTasks(_ value: Task)

    @objc(addTasks:)
    @NSManaged public func addToTasks(_ values: NSSet)

    @objc(removeTasks:)
    @NSManaged public func removeFromTasks(_ values: NSSet)

}

extension Plan : Identifiable {

}
