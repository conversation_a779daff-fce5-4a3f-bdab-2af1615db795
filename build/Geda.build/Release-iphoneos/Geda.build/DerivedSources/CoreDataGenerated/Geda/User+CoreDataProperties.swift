//
//  User+CoreDataProperties.swift
//  
//
//  Created by 杨瑞光 on 2025/7/14.
//
//  This file was automatically generated and should not be edited.
//

import Foundation
import CoreData


extension User {

    @nonobjc public class func fetchRequest() -> NSFetchRequest<User> {
        return NSFetchRequest<User>(entityName: "User")
    }

    @NSManaged public var avatar: String?
    @NSManaged public var checkInDays: Int32
    @NSManaged public var createdAt: Date?
    @NSManaged public var id: String?
    @NSManaged public var name: String?
    @NSManaged public var updatedAt: Date?
    @NSManaged public var plans: NSSet?

}

// MARK: Generated accessors for plans
extension User {

    @objc(addPlansObject:)
    @NSManaged public func addToPlans(_ value: Plan)

    @objc(removePlansObject:)
    @NSManaged public func removeFromPlans(_ value: Plan)

    @objc(addPlans:)
    @NSManaged public func addToPlans(_ values: NSSet)

    @objc(removePlans:)
    @NSManaged public func removeFromPlans(_ values: NSSet)

}

extension User : Identifiable {

}
