{"source": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift", "tables": {"Localizable": [{"comment": "", "key": "%lld%%", "location": {"startingColumn": 18, "startingLine": 101}}, {"comment": "", "key": "完成", "location": {"startingColumn": 18, "startingLine": 105}}, {"comment": "", "key": "基础样式", "location": {"startingColumn": 1, "startingLine": 269}}, {"comment": "", "key": "渐变样式", "location": {"startingColumn": 1, "startingLine": 269}}, {"comment": "", "key": "纯色样式", "location": {"startingColumn": 1, "startingLine": 269}}, {"comment": "", "key": "便捷构造器", "location": {"startingColumn": 1, "startingLine": 269}}, {"comment": "", "key": "番茄钟样式", "location": {"startingColumn": 1, "startingLine": 269}}, {"comment": "", "key": "任务进度样式", "location": {"startingColumn": 1, "startingLine": 269}}, {"comment": "", "key": "动画效果", "location": {"startingColumn": 1, "startingLine": 269}}, {"comment": "", "key": "带动画的进度", "location": {"startingColumn": 1, "startingLine": 269}}, {"comment": "", "key": "多层进度", "location": {"startingColumn": 1, "startingLine": 269}}, {"comment": "", "key": "多层环形进度", "location": {"startingColumn": 1, "startingLine": 269}}]}, "version": 1}