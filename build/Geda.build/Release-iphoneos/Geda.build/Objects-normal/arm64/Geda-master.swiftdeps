version: "Apple Swift version 6.0.3 (swiftlang-6.0.3.1.10 clang-1600.0.30.1)"
options: "d23e123419262430c0e3894c935e518a385734bdd89f0bfedd1c9edcda08d14e"
build_start_time: [1752483583, 969772000]
build_end_time: [1752483591, 525007000]
inputs:
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift": [1752460853, 646782061]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift": [1752142228, 266480371]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/Enums.swift": [1752461949, 484503552]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/BaseViewModel.swift": [1752143886, 129491185]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/FocusTimerViewModel.swift": [1752462307, 104891957]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/HomeViewModel.swift": [1752143971, 839830536]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/PlanViewModel.swift": [1752461958, 109845240]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/ReviewViewModel.swift": [1752145240, 139860650]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/TaskViewModel.swift": [1752461038, 509292289]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/CoreDataManager.swift": [1752142218, 297720787]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PlanRepository.swift": [1752143027, 276221922]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift": [1752143103, 134363632]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift": [1752143134, 975517747]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift": [1752142979, 169109784]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift": [1752143076, 655848369]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/TaskRepository.swift": [1752143056, 651149197]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/UserRepository.swift": [1752142997, 751118050]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift": [1752139835, 318907138]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/DesignTokens.swift": [1752462023, 177398958]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift": [1752198671, 768997530]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift": [1752144444, 806594729]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift": [1752473019, 537436496]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift": [1752476336, 873186055]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/CalendarViewModel.swift": [1752481231, 815775535]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/ProfileViewModel.swift": [1752483404, 106855937]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/ViewModels/ScannerViewModel.swift": [1752483575, 740638156]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift": [1752459859, 428369649]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift": [1752459501, 674298974]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift": [1752141383, 29861654]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GedaCard.swift": [1752473496, 491599658]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GradientButton.swift": [1752144633, 874526774]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift": [1752461285, 39302180]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskCard.swift": [1752472988, 449721638]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskRowView.swift": [1752473160, 951500474]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift": [1752459969, 139866326]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift": [1752139916, 667879831]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift": [1752139873, 909344756]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift": [1752481444, 499522381]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/FocusTimerView.swift": [1752462317, 497662705]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift": [1752474376, 692061739]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/PlanView.swift": [1752473852, 207387264]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift": [1752483374, 469119736]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift": [1752479938, 661979159]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift": [1752483569, 326578968]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskDetailView.swift": [1752474154, 598551502]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/TaskEditView.swift": [1752461602, 117222443]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/ModalPresentationModifier.swift": [1752459342, 581047047]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Modals/NewTaskModal.swift": [1752474514, 444963610]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift"
  : [1752482070, 535221383]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift"
  : [1752482070, 540307944]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift"
  : [1752482070, 541057691]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift"
  : [1752482070, 554052093]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift"
  : [1752482070, 554692758]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift"
  : [1752482070, 537690830]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift"
  : [1752482070, 538973450]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift"
  : [1752482070, 555238047]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift"
  : [1752482070, 555910919]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift"
  : [1752482070, 539222657]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift"
  : [1752482070, 539569781]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/build/Geda.build/Release-iphoneos/Geda.build/DerivedSources/GeneratedAssetSymbols.swift"
  : [1752482070, 259123151]
