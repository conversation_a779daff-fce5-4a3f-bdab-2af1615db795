<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Geda.momd/Geda.mom</key>
		<data>
		bx/py7OifTD6lSpnRFDseFPlHhw=
		</data>
		<key>Geda.momd/Geda.omo</key>
		<data>
		qqx77QBYftFt6hZUFHhic7761Hc=
		</data>
		<key>Geda.momd/VersionInfo.plist</key>
		<data>
		yvqURpYLWp3UDA9+wmywYVqkDAU=
		</data>
		<key>Info.plist</key>
		<data>
		G+uqI0B6alEaRSue9rEz/sXtIB0=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>README.md</key>
		<data>
		Rg3Qxw7qfpdJZGueFPaY/kcl0Uw=
		</data>
		<key>embedded.mobileprovision</key>
		<data>
		X8m8GPQIJ+tELAPEONamCjJV/Wg=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Geda.momd/Geda.mom</key>
		<dict>
			<key>hash2</key>
			<data>
			MajholZ0N98rxFAGRTwXa8pnRktrnVUVddRa4GpDpDE=
			</data>
		</dict>
		<key>Geda.momd/Geda.omo</key>
		<dict>
			<key>hash2</key>
			<data>
			r8OF0TiorldykfGkoiEcsyQSuBBHgtdC77sYa2Gbr5w=
			</data>
		</dict>
		<key>Geda.momd/VersionInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			nvxrJiDmpYObe33QnP2H/cWDF4OrcpQITwK3n0eFVKU=
			</data>
		</dict>
		<key>README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			942CT9yV4wYlpSbu+z36lRnn105M8usgB2l8K+diu14=
			</data>
		</dict>
		<key>embedded.mobileprovision</key>
		<dict>
			<key>hash2</key>
			<data>
			GGhgUFBJkmdajSP0jw7Y01b1/QEQo9KgTpIFOLo6Ax8=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
