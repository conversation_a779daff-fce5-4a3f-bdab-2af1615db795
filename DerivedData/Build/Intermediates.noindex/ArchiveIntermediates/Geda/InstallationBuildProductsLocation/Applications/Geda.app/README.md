# Geda iOS App

## 项目概述

Geda是一个基于SwiftUI开发的iOS应用程序，专注于任务管理和时间跟踪功能。该应用采用现代iOS开发最佳实践，包括MVVM架构模式、Core Data数据持久化和响应式编程。

## 技术栈

- **开发语言**: Swift 5
- **UI框架**: SwiftUI
- **架构模式**: MVVM (Model-View-ViewModel)
- **数据持久化**: Core Data
- **最低支持版本**: iOS 18.2
- **开发工具**: Xcode 16

## 项目结构

```
Geda/
├── App/                    # 应用程序入口
│   ├── GedaApp.swift      # 主应用程序文件
│   └── ContentView.swift  # 根视图
├── Models/                # 数据模型
│   ├── CoreData/          # Core Data模型
│   ├── ViewModels/        # 视图模型
│   └── Enums.swift        # 枚举定义
├── Views/                 # 用户界面
│   ├── Main/              # 主要视图
│   └── Components/        # 可复用组件
├── Services/              # 服务层
│   ├── Repositories/      # 数据仓库
│   └── CoreDataManager.swift
└── Utils/                 # 工具类
    ├── Extensions/        # 扩展
    ├── DesignTokens.swift # 设计令牌
    └── Constants.swift    # 常量定义
```

## 核心功能

### 已实现功能
- ✅ 项目基础架构搭建
- ✅ Core Data数据模型设计
- ✅ MVVM架构实现
- ✅ 基础UI组件库
- ✅ 设计系统和主题
- ✅ 数据仓库模式

### 计划功能
- 📋 任务管理 (创建、编辑、删除、完成)
- ⏰ 番茄钟计时器
- 📊 数据统计和分析
- 📅 日历视图
- 🔍 任务搜索和过滤
- 👤 用户配置文件

## 数据模型

应用程序使用Core Data管理以下实体：

- **User**: 用户信息
- **Task**: 任务实体
- **Subtask**: 子任务
- **Plan**: 计划
- **PomodoroSession**: 番茄钟会话

## 设计系统

应用程序采用统一的设计系统，包括：

- **颜色系统**: 主色调、次要色调、背景色等
- **字体系统**: 标题、正文、说明文字等层级
- **间距系统**: 统一的边距和间距规范
- **组件库**: 可复用的UI组件

## 开发状态

### 当前状态
- ✅ 项目编译成功
- ✅ 应用程序可以在iOS模拟器中运行
- ✅ 基础架构完整
- ✅ Core Data集成完成

### 下一步计划
1. 实现任务管理核心功能
2. 添加番茄钟计时器
3. 完善用户界面
4. 添加数据统计功能
5. 实现用户设置

## 构建和运行

### 环境要求
- macOS 14.0+
- Xcode 16.0+
- iOS 18.2+ (模拟器或真机)

### 构建步骤
1. 克隆项目到本地
2. 使用Xcode打开 `Geda.xcodeproj`
3. 选择目标设备 (iPhone模拟器或真机)
4. 点击运行按钮或使用 `Cmd+R`

### 命令行构建
```bash
# 编译项目
xcodebuild -project Geda.xcodeproj -scheme Geda -destination 'platform=iOS Simulator,name=iPhone 16' build

# 安装到模拟器
xcodebuild -project Geda.xcodeproj -scheme Geda -destination 'platform=iOS Simulator,name=iPhone 16' install

# 启动应用
xcrun simctl launch "iPhone 16" com.ryan.Geda
```

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

项目维护者: Ryan Yang
- Email: [<EMAIL>]
- GitHub: [your-github-username]

---

**注意**: 这是一个正在开发中的项目，某些功能可能尚未完全实现。
