{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>": {"is-command-timestamp": true}, "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>": {"is-command-timestamp": true}, "<TRIGGER: SetOwnerAndGroup yangruiguang:staff /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>": {"is-command-timestamp": true}, "<TRIGGER: Strip /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/_CodeSignature", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<target-Geda-****************************************************************--begin-scanning>", "<target-Geda-****************************************************************--end>", "<target-Geda-****************************************************************--linker-inputs-ready>", "<target-Geda-****************************************************************--modules-ready>", "<workspace-Release-iphonesimulator18.2-iphonesimulator--stale-file-removal>"], "outputs": ["<all>"]}, "<target-Geda-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/_CodeSignature", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Assets.car", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/README.md", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda.momd", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM/Contents/Resources/DWARF/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/PkgInfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-project-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/empty-Geda.plist"], "roots": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath"], "outputs": ["<target-Geda-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>"]}, "<workspace-Release-iphonesimulator18.2-iphonesimulator--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Release-iphonesimulator18.2-iphonesimulator--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<ClangStatCache /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda.xcodeproj", "signature": "f5fbfa902918391b2eaf8640ab66c08c"}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator"]}, "P0:::Gate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM-target-Geda-****************************************************************-": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM/Contents/Resources/DWARF/Geda", "<GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM/Contents/Resources/DWARF/Geda>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM/"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-Geda-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Metadata.appintents>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList"], "outputs": ["<target-Geda-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-ChangePermissions>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-StripSymbols>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<SetMode /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "<SetOwner /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-GenerateStubAPI>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-CodeSign>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-Validate>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-CopyAside>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<Strip /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda>"], "outputs": ["<target-Geda-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-Geda-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--GeneratedFilesTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ProductStructureTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-Geda-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--RealityAssetsTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-project-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.hmap"], "outputs": ["<target-Geda-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/PkgInfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/empty-Geda.plist"], "outputs": ["<target-Geda-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--HeadermapTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleMapTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Geda-****************************************************************--InfoPlistTaskProducer>", "<target-Geda-****************************************************************--VersionPlistTaskProducer>", "<target-Geda-****************************************************************--SanitizerTaskProducer>", "<target-Geda-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Geda-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Geda-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Geda-****************************************************************--StubBinaryTaskProducer>", "<target-Geda-****************************************************************--TestTargetTaskProducer>", "<target-Geda-****************************************************************--TestHostTaskProducer>", "<target-Geda-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Geda-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Geda-****************************************************************--DocumentationTaskProducer>", "<target-Geda-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--start>", "<target-Geda-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app"], "outputs": ["<target-Geda-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--VersionPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--VersionPlistTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-Geda-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Assets.car", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/README.md", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda.momd", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json"], "outputs": ["<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-Geda-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-Geda-****************************************************************--generated-headers>"]}, "P0:::Gate target-Geda-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h"], "outputs": ["<target-Geda-****************************************************************--swift-generated-headers>"]}, "P0:target-Geda-****************************************************************-:Release:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Metadata.appintents>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Info.plist", "--temp-dir-path", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/ssu", "--bundle-id", "com.ryan.Geda", "--product-path", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "--extracted-metadata-path", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Metadata.appintents", "--deployment-postprocessing", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "97946f0fb22d7109735c5b75a622d974"}, "P0:target-Geda-****************************************************************-:Release:CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Info.plist/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/Enums.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/BaseViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/FocusTimerViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/HomeViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/PlanViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/ReviewViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/TaskViewModel.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/README.md/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/CoreDataManager.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PlanRepository.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/TaskRepository.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/UserRepository.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/DesignTokens.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GradientButton.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift/", "<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>", "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "<TRIGGER: Strip /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/_CodeSignature", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"]}, "P0:target-Geda-****************************************************************-:Release:CompileAssetCatalog /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalog /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Assets.car"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "control-enabled": false, "deps": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/assetcatalog_dependencies"], "deps-style": "dependency-info", "signature": "39169ab8874858831adb8bd371ec1baf"}, "P0:target-Geda-****************************************************************-:Release:CopySwiftLibs /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"], "deps": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-Geda-****************************************************************-:Release:CpResource /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/README.md /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/README.md": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/README.md /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/README.md", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/README.md/", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/README.md"]}, "P0:target-Geda-****************************************************************-:Release:DataModelCodegen /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld": {"tool": "shell", "description": "DataModelCodegen /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld/", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/momc", "--action", "generate", "--swift-version", "5.0", "--sdk<PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "--iphonesimulator-deployment-target", "18.2", "--module", "<PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "efe45cbb43ab8886f63990f7e1bdedff"}, "P0:target-Geda-****************************************************************-:Release:DataModelCompile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/ /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld": {"tool": "shell", "description": "DataModelCompile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/ /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda.momd"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/momc", "--sdk<PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "--iphonesimulator-deployment-target", "18.2", "--module", "<PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/CoreData/Geda.xcdatamodeld", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "control-enabled": false, "signature": "17148af9a46aea7954e92fd0f70b5b5a"}, "P0:target-Geda-****************************************************************-:Release:ExtractAppIntentsMetadata": {"tool": "appintents-metadata", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/BaseViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/FocusTimerViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/HomeViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/PlanViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/ReviewViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/TaskViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/Enums.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PlanRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/TaskRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/UserRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/CoreDataManager.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/DesignTokens.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GradientButton.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Metadata.appintents>"]}, "P0:target-Geda-****************************************************************-:Release:Gate target-Geda-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>"], "outputs": ["<target-Geda-****************************************************************--begin-compiling>"]}, "P0:target-Geda-****************************************************************-:Release:Gate target-Geda-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>"], "outputs": ["<target-Geda-****************************************************************--begin-linking>"]}, "P0:target-Geda-****************************************************************-:Release:Gate target-Geda-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--begin-scanning>"]}, "P0:target-Geda-****************************************************************-:Release:Gate target-Geda-****************************************************************--end": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--entry>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Assets.car", "<CopySwiftStdlib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/README.md", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda.momd", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Metadata.appintents>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "<GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM/Contents/Resources/DWARF/Geda>", "<GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM/Contents/Resources/DWARF/Geda>", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/PkgInfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "<SetMode /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "<SetOwner /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "<Strip /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app", "<Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "<Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-project-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/empty-Geda.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM/", "<target-Geda-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Geda-****************************************************************--Barrier-ChangePermissions>", "<target-Geda-****************************************************************--Barrier-CodeSign>", "<target-Geda-****************************************************************--Barrier-CopyAside>", "<target-Geda-****************************************************************--Barrier-GenerateStubAPI>", "<target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Geda-****************************************************************--Barrier-RegisterProduct>", "<target-Geda-****************************************************************--Barrier-StripSymbols>", "<target-Geda-****************************************************************--Barrier-Validate>", "<target-Geda-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Geda-****************************************************************--DocumentationTaskProducer>", "<target-Geda-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Geda-****************************************************************--GeneratedFilesTaskProducer>", "<target-Geda-****************************************************************--HeadermapTaskProducer>", "<target-Geda-****************************************************************--InfoPlistTaskProducer>", "<target-Geda-****************************************************************--ModuleMapTaskProducer>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Geda-****************************************************************--ProductStructureTaskProducer>", "<target-Geda-****************************************************************--RealityAssetsTaskProducer>", "<target-Geda-****************************************************************--SanitizerTaskProducer>", "<target-Geda-****************************************************************--StubBinaryTaskProducer>", "<target-Geda-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Geda-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Geda-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Geda-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Geda-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Geda-****************************************************************--TestHostTaskProducer>", "<target-Geda-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-Geda-****************************************************************--TestTargetTaskProducer>", "<target-Geda-****************************************************************--VersionPlistTaskProducer>", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--generated-headers>", "<target-Geda-****************************************************************--swift-generated-headers>"], "outputs": ["<target-Geda-****************************************************************--end>"]}, "P0:target-Geda-****************************************************************-:Release:Gate target-Geda-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--entry>"]}, "P0:target-Geda-****************************************************************-:Release:Gate target-Geda-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Release-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator>"], "outputs": ["<target-Geda-****************************************************************--immediate>"]}, "P0:target-Geda-****************************************************************-:Release:Gate target-Geda-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList"], "outputs": ["<target-Geda-****************************************************************--linker-inputs-ready>"]}, "P0:target-Geda-****************************************************************-:Release:Gate target-Geda-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h"], "outputs": ["<target-Geda-****************************************************************--modules-ready>"]}, "P0:target-Geda-****************************************************************-:Release:Gate target-Geda-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Assets.car", "<CopySwiftStdlib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda.momd", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Metadata.appintents>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "<GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM/Contents/Resources/DWARF/Geda>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "<target-Geda-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-Geda-****************************************************************--unsigned-product-ready>"]}, "P0:target-Geda-****************************************************************-:Release:Gate target-Geda-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-Geda-****************************************************************--will-sign>"]}, "P0:target-Geda-****************************************************************-:Release:GenerateAssetSymbols /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets/", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets", "--bundle-identifier", "com.ryan.Geda", "--generate-swift-asset-symbols", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "control-enabled": false, "signature": "c97f1b94fae5084956c8f8cc9a0003d5"}, "P0:target-Geda-****************************************************************-:Release:GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda": {"tool": "shell", "description": "GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Info.plist", "<Linked Binary /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM/Contents/Resources/DWARF/Geda", "<GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM/Contents/Resources/DWARF/Geda>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/dsymutil", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda", "-o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "2904e2c7ed7d1673a7c0fa8aa23b98ea"}, "P0:target-Geda-****************************************************************-:Release:MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "inputs": ["<target-Geda-****************************************************************--start>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "<TRIGGER: MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"]}, "P0:target-Geda-****************************************************************-:Release:ProcessInfoPlistFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Info.plist /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/empty-Geda.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Info.plist /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/empty-Geda.plist", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/empty-Geda.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/PkgInfo"]}, "P0:target-Geda-****************************************************************-:Release:ProcessProductPackaging  /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist", "<target-Geda-****************************************************************--ProductStructureTaskProducer>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent"]}, "P0:target-Geda-****************************************************************-:Release:ProcessProductPackagingDER /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "<target-Geda-****************************************************************--ProductStructureTaskProducer>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "-o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "380aa8b26d11b0f1be0db07b2e9f4412"}, "P0:target-Geda-****************************************************************-:Release:RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "<target-Geda-****************************************************************--Barrier-CodeSign>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"]}, "P0:target-Geda-****************************************************************-:Release:SetMode u+w,go-w,a+rX /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app": {"tool": "shell", "description": "SetMode u+w,go-w,a+rX /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "inputs": ["<SetOwner /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "<target-Geda-****************************************************************--Barrier-StripSymbols>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>", "<TRIGGER: SetOwnerAndGroup yangruiguang:staff /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"], "outputs": ["<SetMode /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "<TRIGGER: SetMode u+w,go-w,a+rX /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"], "args": ["/bin/chmod", "-RH", "u+w,go-w,a+rX", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "4f7250a5bd260f25b5953f1eac8ff78e"}, "P0:target-Geda-****************************************************************-:Release:SetOwnerAndGroup yangruiguang:staff /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app": {"tool": "shell", "description": "SetOwnerAndGroup yang<PERSON><PERSON><PERSON>:staff /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "inputs": ["<target-Geda-****************************************************************--Barrier-StripSymbols>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>", "<TRIGGER: MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"], "outputs": ["<SetOwner /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>", "<TRIGGER: SetOwnerAndGroup yangruiguang:staff /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"], "args": ["/usr/sbin/chown", "-RH", "ya<PERSON><PERSON><PERSON><PERSON>:staff", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "1e038c48168f5366b358f8ed12ab5e22"}, "P0:target-Geda-****************************************************************-:Release:Strip /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda": {"tool": "shell", "description": "Strip /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda", "inputs": ["<GenerateDSYMFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app.dSYM/Contents/Resources/DWARF/Geda>", "<target-Geda-****************************************************************--Barrier-CopyAside>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda normal>"], "outputs": ["<Strip /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda>", "<TRIGGER: Strip /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip", "-D", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "bd4d4ef2a18ca77af6e5ecf65a580de3"}, "P0:target-Geda-****************************************************************-:Release:SwiftDriver Compilation Geda normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation Geda normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/BaseViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/FocusTimerViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/HomeViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/PlanViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/ReviewViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/TaskViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/Enums.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PlanRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/TaskRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/UserRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/CoreDataManager.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/DesignTokens.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GradientButton.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished"]}, "P0:target-Geda-****************************************************************-:Release:SymLink /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app ../../InstallationBuildProductsLocation/Applications/Geda.app": {"tool": "symlink", "description": "SymLink /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app ../../InstallationBuildProductsLocation/Applications/Geda.app", "inputs": ["<target-Geda-****************************************************************--start>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.app"], "contents": "../../InstallationBuildProductsLocation/Applications/Geda.app", "repair-via-ownership-analysis": true}, "P0:target-Geda-****************************************************************-:Release:Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app": {"tool": "shell", "description": "Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "<target-Geda-****************************************************************--Barrier-Validate>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "5e3882dbd651adfe6168b7d6804c2527"}, "P0:target-Geda-****************************************************************-:Release:Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Info.plist", "<target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"], "outputs": ["<Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app>"]}, "P0:target-Geda-****************************************************************-:Release:ValidateDevelopmentAssets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content", "<target-Geda-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-Geda-****************************************************************-:Release:Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json/", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json"]}, "P2:target-Geda-****************************************************************-:Release:Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc/", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc"]}, "P2:target-Geda-****************************************************************-:Release:Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule/", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule"]}, "P2:target-Geda-****************************************************************-:Release:Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda normal", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator", "<target-Geda-****************************************************************--generated-headers>", "<target-Geda-****************************************************************--swift-generated-headers>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda", "<Linked Binary /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-<PERSON><PERSON>", "-L/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "-L/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator", "-F/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/EagerLinkingTBDs/Release-iphonesimulator", "-F/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Release-iphonesimulator", "-filelist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-final_output", "-<PERSON><PERSON><PERSON>", "/Applications/Geda.app/Geda", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "-o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/Applications/Geda.app/Geda"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "deps": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat"], "deps-style": "dependency-info", "signature": "019067b2ac9b13a5ff520e9dd82ef473"}, "P2:target-Geda-****************************************************************-:Release:SwiftDriver Compilation Requirements Geda normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements Geda normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/BaseViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/FocusTimerViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/HomeViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/PlanViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/ReviewViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/TaskViewModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/Enums.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PlanRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/TaskRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/UserRepository.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/CoreDataManager.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/DesignTokens.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GradientButton.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BaseViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/FocusTimerViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskViewModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Enums.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PlanRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSessionRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/RepositoryProtocols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/SubtaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/UserRepository.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CoreDataManager.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Typography+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/DesignTokens.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CircularProgressView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GradientButton.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Plan+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/PomodoroSession+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Subtask+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Task+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataClass.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/User+CoreDataProperties.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda+CoreDataModel.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-master.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc"]}, "P2:target-Geda-****************************************************************-:Release:SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-non-framework-target-headers.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-non-framework-target-headers.hmap"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-target-headers.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-all-target-headers.hmap"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-generated-files.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-generated-files.hmap"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-own-target-headers.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-own-target-headers.hmap"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-project-headers.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda-project-headers.hmap"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Geda.hmap"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json"]}, "P2:target-Geda-****************************************************************-:Release:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/empty-Geda.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/empty-Geda.plist", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/empty-Geda.plist"]}}}