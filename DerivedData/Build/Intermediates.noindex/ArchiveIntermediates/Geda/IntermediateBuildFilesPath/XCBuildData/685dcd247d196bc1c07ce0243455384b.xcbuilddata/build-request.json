{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "a96b0e99fac1a0c806861643310f480eced4c9a705f3849a1271a99aa188f012"}], "containerPath": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "install", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.2", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Release", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "install", "ASSET_PACK_FOLDER_PATH": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation/OnDemandResources", "ASSETCATALOG_COMPILER_FLATTENED_APP_ICON_PATH": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/ProductIcon.png", "COLOR_DIAGNOSTICS": "YES", "DEPLOYMENT_LOCATION": "YES", "DEPLOYMENT_POSTPROCESSING": "YES", "diagnostic_message_length": "80", "DSTROOT": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/InstallationBuildProductsLocation", "EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE": "NO", "ENABLE_PREVIEWS": "NO", "ENABLE_SIGNATURE_AGGREGATION": "YES", "ENABLE_XOJIT_PREVIEWS": "YES", "INDEX_ENABLE_DATA_STORE": "NO", "MESSAGES_APPLICATION_EXTENSION_SUPPORT_FOLDER_PATH": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/MessagesApplicationExtensionSupport", "MESSAGES_APPLICATION_SUPPORT_FOLDER_PATH": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/MessagesApplicationSupport", "OBJROOT": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath", "ONLY_ACTIVE_ARCH": "YES", "SHARED_PRECOMPS_DIR": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/PrecompiledHeaders", "SIGNATURE_METADATA_FOLDER_PATH": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/Signatures", "SWIFT_STDLIB_TOOL_UNSIGNED_DESTINATION_DIR": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/SwiftSupport", "SYMROOT": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath", "WATCHKIT_2_SUPPORT_FOLDER_PATH": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/BuildProductsPath/WatchKitSupport2"}}}}, "schemeCommand": "archive", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}