version: "Apple Swift version 6.0.3 (swiftlang-*******.10 clang-1600.0.30.1)"
options: "e6a12ce9bb021450f1f0c1fcc03cd44816c6da4882aef566b3fc7017fd54a559"
build_start_time: [1752198146, 100761000]
build_end_time: [1752198148, 553374000]
inputs:
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Geda+CoreDataModel.swift"
  : [1752197414, 342064892]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataClass.swift"
  : [1752197414, 347918046]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Plan+CoreDataProperties.swift"
  : [1752197414, 348314606]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataClass.swift"
  : [1752197414, 343687213]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/PomodoroSession+CoreDataProperties.swift"
  : [1752197414, 344150227]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataClass.swift"
  : [1752197414, 345419944]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Subtask+CoreDataProperties.swift"
  : [1752197414, 345714843]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataClass.swift"
  : [1752197414, 344826979]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/Task+CoreDataProperties.swift"
  : [1752197414, 345146960]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataClass.swift"
  : [1752197414, 346134235]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/CoreDataGenerated/Geda/User+CoreDataProperties.swift"
  : [1752197414, 346418135]
  ? "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/ArchiveIntermediates/Geda/IntermediateBuildFilesPath/Geda.build/Release-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift"
  : [1752197414, 116903485]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift": [1752198089, 338828276]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift": [1752142228, 266480371]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/Enums.swift": [1752145117, 575937770]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/BaseViewModel.swift": [1752143886, 129491185]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/FocusTimerViewModel.swift": [1752143945, 742949633]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/HomeViewModel.swift": [1752143971, 839830536]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/PlanViewModel.swift": [1752145262, 926846425]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/ReviewViewModel.swift": [1752145240, 139860650]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Models/ViewModels/TaskViewModel.swift": [1752145131, 757695056]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/CoreDataManager.swift": [1752142218, 297720787]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PlanRepository.swift": [1752143027, 276221922]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/PomodoroSessionRepository.swift": [1752143103, 134363632]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryManager.swift": [1752143134, 975517747]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/RepositoryProtocols.swift": [1752142979, 169109784]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/SubtaskRepository.swift": [1752143076, 655848369]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/TaskRepository.swift": [1752143056, 651149197]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Services/Repositories/UserRepository.swift": [1752142997, 751118050]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift": [1752139835, 318907138]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/DesignTokens.swift": [1752144560, 629723234]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift": [1752144418, 529413559]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift": [1752144444, 806594729]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Typography+Geda.swift": [1752144478, 479556948]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift": [1752144517, 713967182]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift": [1752139972, 143414519]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CircularProgressView.swift": [1752197116, 567307769]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift": [1752141383, 29861654]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/GradientButton.swift": [1752144633, 874526774]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift": [1752144880, 735901685]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift": [1752145608, 733228200]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift": [1752139916, 667879831]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift": [1752139873, 909344756]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift": [1752145822, 666006684]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift": [1752145215, 570684018]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift": [1752145731, 74564888]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift": [1752145787, 385056072]
  "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift": [1752145809, 170645069]
