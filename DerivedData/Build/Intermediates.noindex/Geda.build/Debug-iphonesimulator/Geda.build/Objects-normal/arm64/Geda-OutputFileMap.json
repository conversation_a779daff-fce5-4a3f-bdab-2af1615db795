{"": {"diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-master.swiftdeps"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView~partial.swiftmodule"}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift": {"const-values": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.d", "diagnostics": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.dia", "index-unit-output-path": "/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "llvm-bc": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.bc", "object": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "swift-dependencies": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView~partial.swiftmodule"}}