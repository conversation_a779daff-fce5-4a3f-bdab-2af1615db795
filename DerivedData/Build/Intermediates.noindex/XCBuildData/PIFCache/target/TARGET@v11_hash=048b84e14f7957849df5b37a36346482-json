{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"Geda/Preview Content\"", "DEVELOPMENT_TEAM": "5E82E74R94", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.ryan.Geda", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "a96b0e99fac1a0c806861643310f480ec6ad93a8762c1393de25e1bf1d186ec7", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"Geda/Preview Content\"", "DEVELOPMENT_TEAM": "5E82E74R94", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.ryan.Geda", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "a96b0e99fac1a0c806861643310f480e8e39fe02ea211e39ef72964e96d6d24e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "a96b0e99fac1a0c806861643310f480eb071422c3b59d35584abc6a90e5a4d43", "guid": "a96b0e99fac1a0c806861643310f480e29430fd4cd1064081ad7305bc3a81540"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e4b6640f89b4d067c9b913db91a85b4e5", "guid": "a96b0e99fac1a0c806861643310f480efc153bfe31209da3499a8d1b8e879ad7"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e7ff9c50fe10a7cbf2339c00755de8994", "guid": "a96b0e99fac1a0c806861643310f480ea6cb2c2d4cdcbad53dd6e5290cf9d132"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e111bf2d36edc0db29b23d7de118ce2da", "guid": "a96b0e99fac1a0c806861643310f480e1b2b18a43df74537606dad1d93944c20"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ec608c33aab3beec7d3acf2ce3aec1960", "guid": "a96b0e99fac1a0c806861643310f480e80275a296a270c7290b7f371096a0815"}, {"fileReference": "a96b0e99fac1a0c806861643310f480eaab270e9bd0b13aa42d0167c6eaa442f", "guid": "a96b0e99fac1a0c806861643310f480e94483e07f2fe4e71319a81714b378434"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e1c39f37c99fa1655efa1c9eb42cfb6f0", "guid": "a96b0e99fac1a0c806861643310f480eda5985decd4c158b285d3d74e1c11c8d"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e1a048e4ef0860ad7ee64429ef7fcfea0", "guid": "a96b0e99fac1a0c806861643310f480e404dfa3036b8609c49e0c92b54676ef3"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e3559111968e84f7efeed264f3575bb6d", "guid": "a96b0e99fac1a0c806861643310f480efe3dbfdefd8fb69f51f321a93953437b"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e0e172e9417a3a552e68cfe3ebdc0fe99", "guid": "a96b0e99fac1a0c806861643310f480e774926420779a908ca142a228d225a31"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e6e71673dca9b9bb9ee7f43215c416797", "guid": "a96b0e99fac1a0c806861643310f480e0e52de8430ad1cbe425ecdd20e74c952"}, {"fileReference": "a96b0e99fac1a0c806861643310f480efc007c25403e0ce53270a02576984963", "guid": "a96b0e99fac1a0c806861643310f480ec1835586d23a2a63e641c217a1db5fde"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ea484b4899bc5715793f7e648dc590960", "guid": "a96b0e99fac1a0c806861643310f480e17a5b138f86f2526453dc72acc9c1d87"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e5aac894a86fe068c7cc1283a0a30afd8", "guid": "a96b0e99fac1a0c806861643310f480e203b5ff3b81f1831c02ddb26eb62f82a"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e47f42144e64fb1985f62e6ffd13bf295", "guid": "a96b0e99fac1a0c806861643310f480e6c3755b50da13fa9afe4556cce4a5260"}, {"fileReference": "a96b0e99fac1a0c806861643310f480efe1d083fefadc5125df163bb9e91a8d6", "guid": "a96b0e99fac1a0c806861643310f480eec20b7beb7ac28b359ac9c764bddc34b"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e2bbcf23e7e98ba263cbc5855f86f5df8", "guid": "a96b0e99fac1a0c806861643310f480e0f45d6da9313245e5c32432deecda675"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ee4f346f3cf33b95476ad61db9977a0d6", "guid": "a96b0e99fac1a0c806861643310f480ea8d36ab24481798d12940bb3506d7309"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e4d0d2465d49992b2f9b089c89f71241d", "guid": "a96b0e99fac1a0c806861643310f480e54bcc6ed7d2285d54d2a0cd04eec7a1c"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e8e75ac70564cf94813d2dfb35235c667", "guid": "a96b0e99fac1a0c806861643310f480e66ab08bee93f1071a8bc90c3cbb033ff"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ee790266d8956d4413a248dac293cbef2", "guid": "a96b0e99fac1a0c806861643310f480e7489826c56a644b5032138adbc096d9d"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ecec0b4297fe25b271e25c621721ff0ba", "guid": "a96b0e99fac1a0c806861643310f480e27b9ae5505150b47105b0ed81acd9f74"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e90aeb47360fa5e2e8a8b36765746353f", "guid": "a96b0e99fac1a0c806861643310f480edb365b5ab19969298e4bf851f98e5787"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e95b37d6b0451c8c02beedde85f4156fc", "guid": "a96b0e99fac1a0c806861643310f480e8575f7cb35899263d2522b4b33bd9b87"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e5b89eb16e104b5304cd3e7598a60de0f", "guid": "a96b0e99fac1a0c806861643310f480eb76b2a05c7f32a426a84ec595461db40"}, {"fileReference": "a96b0e99fac1a0c806861643310f480eabe17dc6caa2c1a2be065f7fac26d382", "guid": "a96b0e99fac1a0c806861643310f480ea3d365c3ad03c940544ba13256cc171a"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e4899bf901bea7be1ce4985d94fd56527", "guid": "a96b0e99fac1a0c806861643310f480e758cfe944b97a5cfac4a6c24aa7543b8"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ee4e08750799c8564038edcd14538f5eb", "guid": "a96b0e99fac1a0c806861643310f480e3234f74d68c05212e30cb53e6f219718"}, {"fileReference": "a96b0e99fac1a0c806861643310f480eb906ee48302884eb58a4579c7d12b075", "guid": "a96b0e99fac1a0c806861643310f480e7a3261c6d60a6e93897104df280f673a"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e21e3eb5f00711d65d2713c8234cde9d7", "guid": "a96b0e99fac1a0c806861643310f480e2c9173f286c2d73db50b89695143c7aa"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e05e4bf95ae1e81cfd4d6a8d280ca4ea6", "guid": "a96b0e99fac1a0c806861643310f480e526ae9fbe40eebaaa7b63d093eedcaca"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e51d68f9fe276e5bf1db567615b8c7921", "guid": "a96b0e99fac1a0c806861643310f480e524491e5247cb59f180c5fe2ff1b4b89"}, {"fileReference": "a96b0e99fac1a0c806861643310f480eab61b02a2f61dc91cac3205c5505e1ff", "guid": "a96b0e99fac1a0c806861643310f480ef2d119699cef87f42941071d03f78689"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e1a9448a6fa58e5bdc5bd3920bd86c4da", "guid": "a96b0e99fac1a0c806861643310f480e9c252c111bd3489e21d44f0b066cab09"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e0aaa2f2ac3c97d3c9a0abfb61d70e000", "guid": "a96b0e99fac1a0c806861643310f480e00526ae6014ab5dacf248d6e4c9d407a"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ebb4d4632eadfe3ab1563da5ce74c56d4", "guid": "a96b0e99fac1a0c806861643310f480e1bcb8ae188be9f36184c46d9e97a24a7"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e9226ef328651175efb4a9f5a80024985", "guid": "a96b0e99fac1a0c806861643310f480ec63835449dcabd10f5b32d3bba0ee55c"}], "guid": "a96b0e99fac1a0c806861643310f480e0222f3d5eab8945c48429e50b63a11b0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "a96b0e99fac1a0c806861643310f480e877e46c51de92623e0402d6747562b72", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "a96b0e99fac1a0c806861643310f480e9c969628ed332a2a07b8fc29ed2b8135", "guid": "a96b0e99fac1a0c806861643310f480e3d7d7567f00b0ce65a8372ddf004c311"}, {"fileReference": "a96b0e99fac1a0c806861643310f480edb9613d2d7d804075b4dbfe0d320108e", "guid": "a96b0e99fac1a0c806861643310f480eb75c46749b2e3f8edfe948080129dba5"}], "guid": "a96b0e99fac1a0c806861643310f480ec084e48ada6966ade76e43c35dbafb91", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "a96b0e99fac1a0c806861643310f480eced4c9a705f3849a1271a99aa188f012", "name": "<PERSON><PERSON>", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "a96b0e99fac1a0c806861643310f480e873dc48c9ecae62eff0e2e5346547d68", "name": "Geda.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}