{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"Geda/Preview Content\"", "DEVELOPMENT_TEAM": "5E82E74R94", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.ryan.Geda", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "a96b0e99fac1a0c806861643310f480ec6ad93a8762c1393de25e1bf1d186ec7", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"Geda/Preview Content\"", "DEVELOPMENT_TEAM": "5E82E74R94", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.ryan.Geda", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "a96b0e99fac1a0c806861643310f480e8e39fe02ea211e39ef72964e96d6d24e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "a96b0e99fac1a0c806861643310f480e9487ba7a98861c09bcb75663baec19d1", "guid": "a96b0e99fac1a0c806861643310f480e87346d9bd919daa1d97bbefb2184155e"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ea4ab880aa24036276951a7acf8f63d7e", "guid": "a96b0e99fac1a0c806861643310f480efbc8742d1b281f3d18d8f02be2c456fc"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e6c2fef6049ba1e4ad2c1e13c2c20bf7a", "guid": "a96b0e99fac1a0c806861643310f480e40464c37129a4e70eec0051081cfdabc"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e5bb9ca881c28c3ab3e21041de911f435", "guid": "a96b0e99fac1a0c806861643310f480e3ca0d2c5d54b1a3aa806e3b779d8c845"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e0420e158cadc4ade12f85f5ca51f4b18", "guid": "a96b0e99fac1a0c806861643310f480e5f313095c6c136056391ffe3afc9a339"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e0f91d92673aa21280eefba5d806294a5", "guid": "a96b0e99fac1a0c806861643310f480e8f8cd0f265957575c1776394fb2691c8"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ea40bac4084548132a8d7a170fe689ded", "guid": "a96b0e99fac1a0c806861643310f480e1fe788f6dd02eaf689d89fcb7122a77e"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ecfc3e65d3ad79ae7b32a3db5049e8ffa", "guid": "a96b0e99fac1a0c806861643310f480eb4e375628baceba8d316f4759f0da9aa"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e588785d48fb800ffe1866ea5316eecc4", "guid": "a96b0e99fac1a0c806861643310f480ed8045c97da4b9e13de690e8b072ca072"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e84cb58536eeb70ae9e202bfb9c98eae9", "guid": "a96b0e99fac1a0c806861643310f480eea6b7d1b314c8f5657aa2c6fb33f018b"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ed5ddea38a4646980db6d5035c5237950", "guid": "a96b0e99fac1a0c806861643310f480e18d95a2511a60e25e3cba702eef90369"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e7380e179cf1714111d33c9c1799dbf1f", "guid": "a96b0e99fac1a0c806861643310f480ed1dd61634139cbf2b78824075e429aa5"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ee7161da18451f3295fac162d99ceaee2", "guid": "a96b0e99fac1a0c806861643310f480edbebb8130063b98702d4ea153a853355"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ea06294a5999cf648c0a1dec76b13405b", "guid": "a96b0e99fac1a0c806861643310f480e49ea944ecbeb559c8f150ee97587451c"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e7028e07943a76323e504bf6a55f66b53", "guid": "a96b0e99fac1a0c806861643310f480e64df63e9f2af7341eb170840a5b4dbb9"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e7f796fe6f3da333bb42668b4fa12060b", "guid": "a96b0e99fac1a0c806861643310f480ed4fbb7a6cce68e2757b9e0bcf7d5a5ba"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e68576b0b599530b4bbed5a06289d0911", "guid": "a96b0e99fac1a0c806861643310f480e409f2d73139f2301da394fe6e0db7da0"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e188803d2cf3382c9c3ea4a2209d05739", "guid": "a96b0e99fac1a0c806861643310f480ebf0896b5ae1620e745021246e89a57c3"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e60a3cf6e65bbeeefdbd71324e641ca7d", "guid": "a96b0e99fac1a0c806861643310f480eba528732042917540dbd7c17871e56a7"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e429c33858dfcdeaef8e9cea95e2dc816", "guid": "a96b0e99fac1a0c806861643310f480e738bc1a7757222d014c5c1e5ec44f25c"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ed25453120f8194343530d3a0b38ba328", "guid": "a96b0e99fac1a0c806861643310f480e066679c74a7a7df8b3b319075885f536"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e9af5eaec5478a864741d32d251d1e450", "guid": "a96b0e99fac1a0c806861643310f480e0ea4a081e4c6cd3a56827dbb038fc874"}, {"fileReference": "a96b0e99fac1a0c806861643310f480efbc33bb6eb37b563b00333bb19243690", "guid": "a96b0e99fac1a0c806861643310f480e0db4252085d3a2b7e67cc89b2c2b41b0"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e90ddf2ff6cb3b040b35c8e581c70713c", "guid": "a96b0e99fac1a0c806861643310f480e761c0f2fa82c48697f9e3a8ca4c1934a"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e5f881c85868e73b2ef668e964a3f46a6", "guid": "a96b0e99fac1a0c806861643310f480e6978958379b01e60c1903239ce485fbd"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ecf273b48d52d2cd6b3a068f061d6f009", "guid": "a96b0e99fac1a0c806861643310f480e46b3dffa2e0334c01a7e944a7ab4327b"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e5fdacea4aafbd6417c8254b7579a3744", "guid": "a96b0e99fac1a0c806861643310f480e4a824a45928fc5a630369202fba29d9b"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e357caaa9098dbbe9befe19395202ab37", "guid": "a96b0e99fac1a0c806861643310f480e611578306e141751108a3af2111312b0"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e864670e9f109cf1215b33985dbda0df2", "guid": "a96b0e99fac1a0c806861643310f480edfafdccb434416875e574c476e3a257f"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e02d03fc437661bc9caed2aa9805e573a", "guid": "a96b0e99fac1a0c806861643310f480e8bcb8c3e93f824d9052833f3b5858b7e"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e83019e5d71262dd8e6b5693e6ad33f5d", "guid": "a96b0e99fac1a0c806861643310f480e0f0e2214c40d8413aee8458327dde5a6"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e25ded20501fe16a35e293de457efd106", "guid": "a96b0e99fac1a0c806861643310f480ef9af4844fb5f1d93382a355fdaae3dac"}, {"fileReference": "a96b0e99fac1a0c806861643310f480eefc4414c5af486fb28d0956f52f465dd", "guid": "a96b0e99fac1a0c806861643310f480ed0558da4a22b618da343c0fd82eeb7a4"}, {"fileReference": "a96b0e99fac1a0c806861643310f480eb827cf206939c1ac199562ba80d0272c", "guid": "a96b0e99fac1a0c806861643310f480e803114617ca94a7e43357ead230d49fb"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e7423aefcfff865af1bf40e7d70977d80", "guid": "a96b0e99fac1a0c806861643310f480e76e7278b6d01918231c8f9d8b8215ecc"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ea7cf4de42accfc280d829afea54881ef", "guid": "a96b0e99fac1a0c806861643310f480e7dbcbfee4443cb96e66e7cdcab024bd1"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e1297cdf09378ab541dc71d64662c3477", "guid": "a96b0e99fac1a0c806861643310f480e44b86e80c68ddf2a44c70bf074c85fe0"}], "guid": "a96b0e99fac1a0c806861643310f480e0222f3d5eab8945c48429e50b63a11b0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "a96b0e99fac1a0c806861643310f480e877e46c51de92623e0402d6747562b72", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "a96b0e99fac1a0c806861643310f480ed9fc2e5d8def7dba7101a16997933dc7", "guid": "a96b0e99fac1a0c806861643310f480e38edfeaf2b1f67b3b10f971277e10645"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ed03848f81bd643293380bbefa9250bed", "guid": "a96b0e99fac1a0c806861643310f480edbcfd91a9dfe765725b79ef18fff6ef9"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e3df7aad6f3775e42b35d3174f2a296a5", "guid": "a96b0e99fac1a0c806861643310f480e9e2ee3ad873de6313dcb0c3117f564aa"}], "guid": "a96b0e99fac1a0c806861643310f480ec084e48ada6966ade76e43c35dbafb91", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "a96b0e99fac1a0c806861643310f480eced4c9a705f3849a1271a99aa188f012", "name": "<PERSON><PERSON>", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "a96b0e99fac1a0c806861643310f480e873dc48c9ecae62eff0e2e5346547d68", "name": "Geda.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}