{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"Geda/Preview Content\"", "DEVELOPMENT_TEAM": "5E82E74R94", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.ryan.Geda", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "a96b0e99fac1a0c806861643310f480ec6ad93a8762c1393de25e1bf1d186ec7", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_ASSET_PATHS": "\"Geda/Preview Content\"", "DEVELOPMENT_TEAM": "5E82E74R94", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation": "YES", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.ryan.Geda", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "a96b0e99fac1a0c806861643310f480e8e39fe02ea211e39ef72964e96d6d24e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "a96b0e99fac1a0c806861643310f480e4142adfd3b550d4777f76af288979176", "guid": "a96b0e99fac1a0c806861643310f480e51a117f173749a87e983ceb814eba61b"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e2abdef7676863389e216855de9a18408", "guid": "a96b0e99fac1a0c806861643310f480e96a8c12bcba8bc3a0232a23e187c2a62"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ee0a7a4c0448de7e55428506eb5123a30", "guid": "a96b0e99fac1a0c806861643310f480eb5edc4590f47b65844668c5982e7febb"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ea2126c83db6039386dc47b880c9b8764", "guid": "a96b0e99fac1a0c806861643310f480edde59ab25d055fba7b00ba834c5134e9"}, {"fileReference": "a96b0e99fac1a0c806861643310f480eaedb0eb4de4c079001afb7ea342f6f9a", "guid": "a96b0e99fac1a0c806861643310f480ebda6fcc4164f433b96e42739eb30e932"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e03791696e1da79a66af261d2ab1ff872", "guid": "a96b0e99fac1a0c806861643310f480e84d56dfe1ebe585dbf168996591c1d3b"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e93c2a385ac8fb1a3c74011a9dc9324cd", "guid": "a96b0e99fac1a0c806861643310f480ee03ddfaa4be8e35413d933a4b0b13d6b"}, {"fileReference": "a96b0e99fac1a0c806861643310f480eca17c33e963db05ddcd80c91c4398172", "guid": "a96b0e99fac1a0c806861643310f480e7df240b807ffeb80073fc5c36f5b47b9"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e510a68faac8f3a34386f2eca8b3e87cf", "guid": "a96b0e99fac1a0c806861643310f480e99c526e247e90b7fea342b62127e6e1c"}, {"fileReference": "a96b0e99fac1a0c806861643310f480eb755a2187aee789454be23a3c4c57849", "guid": "a96b0e99fac1a0c806861643310f480ee5285fcecb32d51932881b502801bcb8"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e7274ea433c861f72880657cdde70ff54", "guid": "a96b0e99fac1a0c806861643310f480e1a4533dbfbfdc9606af80d125336cb2f"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ebd03b4687b1d1b1f77891a4bbe381b7c", "guid": "a96b0e99fac1a0c806861643310f480e136a8d6c8756dcb2b6fb7aff1ed54a86"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e427c8e4325ced4ab252313299044060b", "guid": "a96b0e99fac1a0c806861643310f480eef117060146b8b39244fa316dd6a345c"}, {"fileReference": "a96b0e99fac1a0c806861643310f480eafc5803a6d43649f0155cc95d1870fef", "guid": "a96b0e99fac1a0c806861643310f480e62aba2671ed13e147147217f78d73393"}, {"fileReference": "a96b0e99fac1a0c806861643310f480edbbdd5a8e2235f5394af98d65b3ab59b", "guid": "a96b0e99fac1a0c806861643310f480e1083cc38e8c636185a89841d1970f11d"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e5b43271b7af1da1416129400961996d8", "guid": "a96b0e99fac1a0c806861643310f480e90441a205b472c64040d91c46f0f3368"}, {"fileReference": "a96b0e99fac1a0c806861643310f480ec07ecffc32b00803ec34f3ce1dcc2747", "guid": "a96b0e99fac1a0c806861643310f480e51e2b9dcbc20e9a9972c3ef7ba6fc6aa"}], "guid": "a96b0e99fac1a0c806861643310f480e0222f3d5eab8945c48429e50b63a11b0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "a96b0e99fac1a0c806861643310f480e877e46c51de92623e0402d6747562b72", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "a96b0e99fac1a0c806861643310f480e0eeb9f228a4b4054a7d4e4ce41f1c685", "guid": "a96b0e99fac1a0c806861643310f480eed53e2c476063ee3eca3c6280e2317f6"}, {"fileReference": "a96b0e99fac1a0c806861643310f480e225e3204e44156022bcf2b747ea984d6", "guid": "a96b0e99fac1a0c806861643310f480ee6cd4a2d765eed6506aaeda21280f2b5"}], "guid": "a96b0e99fac1a0c806861643310f480ec084e48ada6966ade76e43c35dbafb91", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "a96b0e99fac1a0c806861643310f480eced4c9a705f3849a1271a99aa188f012", "name": "<PERSON><PERSON>", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "a96b0e99fac1a0c806861643310f480e873dc48c9ecae62eff0e2e5346547d68", "name": "Geda.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}