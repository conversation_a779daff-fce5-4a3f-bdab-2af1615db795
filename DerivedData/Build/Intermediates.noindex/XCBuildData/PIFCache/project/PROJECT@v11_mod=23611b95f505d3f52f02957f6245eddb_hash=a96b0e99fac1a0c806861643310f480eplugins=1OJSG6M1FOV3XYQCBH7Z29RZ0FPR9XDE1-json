{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "a96b0e99fac1a0c806861643310f480e51a26ea4bc78b6474ee0c55e8222952b", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "a96b0e99fac1a0c806861643310f480e6bcebdef059cbe1ecf736722bb257445", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e9487ba7a98861c09bcb75663baec19d1", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ea4ab880aa24036276951a7acf8f63d7e", "path": "GedaApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e51617f90aeb58ed7fc61c6e0be8e8bb0", "name": "App", "path": "App", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "wrapper.xcdatamodel", "guid": "a96b0e99fac1a0c806861643310f480ee0b2f37715e6cf48e05570885c1d0d66", "path": "Geda.xcdatamodel", "sourceTree": "<group>", "type": "file"}], "fileType": "wrapper.xcdatamodeld", "guid": "a96b0e99fac1a0c806861643310f480e6c2fef6049ba1e4ad2c1e13c2c20bf7a", "name": "Geda.xcdatamodeld", "path": "Geda.xcdatamodeld", "sourceTree": "<group>", "type": "versionGroup"}], "guid": "a96b0e99fac1a0c806861643310f480e756fd4517f33a8fd2773a78ef74fa75e", "name": "CoreData", "path": "CoreData", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e5bb9ca881c28c3ab3e21041de911f435", "path": "BaseViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e0420e158cadc4ade12f85f5ca51f4b18", "path": "FocusTimerViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e0f91d92673aa21280eefba5d806294a5", "path": "HomeViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ea40bac4084548132a8d7a170fe689ded", "path": "PlanViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ecfc3e65d3ad79ae7b32a3db5049e8ffa", "path": "ReviewViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e588785d48fb800ffe1866ea5316eecc4", "path": "TaskViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e4b1e4cc2ea8d3b901b4ee3d92dcfc103", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e84cb58536eeb70ae9e202bfb9c98eae9", "path": "En<PERSON>.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e078739c5468096ad4a923a90a0bdfe26", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "a96b0e99fac1a0c806861643310f480ed9fc2e5d8def7dba7101a16997933dc7", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480eb40233ca0c02fe97967d5dab4c484594", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ed5ddea38a4646980db6d5035c5237950", "path": "PlanRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e7380e179cf1714111d33c9c1799dbf1f", "path": "PomodoroSessionRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ee7161da18451f3295fac162d99ceaee2", "path": "RepositoryManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ea06294a5999cf648c0a1dec76b13405b", "path": "RepositoryProtocols.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e7028e07943a76323e504bf6a55f66b53", "path": "SubtaskRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e7f796fe6f3da333bb42668b4fa12060b", "path": "TaskRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e68576b0b599530b4bbed5a06289d0911", "path": "UserRepository.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e1967e25fc30698ba0bc081637248a609", "name": "Repositories", "path": "Repositories", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e188803d2cf3382c9c3ea4a2209d05739", "path": "CoreDataManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e4b10c19d39a00e6e47f9e07896ac477a", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e60a3cf6e65bbeeefdbd71324e641ca7d", "path": "Color+Geda.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e429c33858dfcdeaef8e9cea95e2dc816", "path": "LinearGradient+Geda.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ed25453120f8194343530d3a0b38ba328", "path": "Typography+Geda.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e9af5eaec5478a864741d32d251d1e450", "path": "View+Extensions.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e3fd879df7cea3fa94ba6a6f493c4873e", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480efbc33bb6eb37b563b00333bb19243690", "path": "Constants.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e90ddf2ff6cb3b040b35c8e581c70713c", "path": "DesignTokens.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e38e98f4988f710f651dde72c2ea8d0e7", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e5f881c85868e73b2ef668e964a3f46a6", "path": "BottomActionBar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ecf273b48d52d2cd6b3a068f061d6f009", "path": "CircularProgressView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e5fdacea4aafbd6417c8254b7579a3744", "path": "CompactCalendar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e357caaa9098dbbe9befe19395202ab37", "path": "GradientButton.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e864670e9f109cf1215b33985dbda0df2", "path": "StackedTaskCards.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e02d03fc437661bc9caed2aa9805e573a", "path": "TaskSectionHeader.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e83019e5d71262dd8e6b5693e6ad33f5d", "path": "TodayReminderCard.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e25ded20501fe16a35e293de457efd106", "path": "TopNavigationSwitch.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480ec8a1bc5a07456b9ac0612c5cdadd131f", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480eefc4414c5af486fb28d0956f52f465dd", "path": "CalendarView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480eb827cf206939c1ac199562ba80d0272c", "path": "HomeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e7423aefcfff865af1bf40e7d70977d80", "path": "ProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ea7cf4de42accfc280d829afea54881ef", "path": "ReviewView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e1297cdf09378ab541dc71d64662c3477", "path": "ScannerView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e56c8a28549f1ed37d9d4485e3890dee6", "name": "Main", "path": "Main", "sourceTree": "<group>", "type": "group"}], "guid": "a96b0e99fac1a0c806861643310f480e7c52a8d99e2cc9a1b00c00f120cae85a", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "a96b0e99fac1a0c806861643310f480ed03848f81bd643293380bbefa9250bed", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "a96b0e99fac1a0c806861643310f480e3df7aad6f3775e42b35d3174f2a296a5", "path": "README.md", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e3100b58548a0573d695283b41db3e33a", "name": "<PERSON><PERSON>", "path": "<PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}, {"guid": "a96b0e99fac1a0c806861643310f480eadaaee36ddc6392bc162272bfc70f8df", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "a96b0e99fac1a0c806861643310f480eaebe6dd2f5283aeca298914d18309adc", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "a96b0e99fac1a0c806861643310f480e", "path": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "targets": ["TARGET@v11_hash=a04fb485f9d41f964db5e5a7472b9edb"]}