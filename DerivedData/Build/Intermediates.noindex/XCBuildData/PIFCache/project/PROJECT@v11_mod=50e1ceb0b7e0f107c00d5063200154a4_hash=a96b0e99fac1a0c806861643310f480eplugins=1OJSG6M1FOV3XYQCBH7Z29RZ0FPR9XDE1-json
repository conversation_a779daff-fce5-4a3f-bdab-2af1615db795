{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "a96b0e99fac1a0c806861643310f480e51a26ea4bc78b6474ee0c55e8222952b", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "a96b0e99fac1a0c806861643310f480e6bcebdef059cbe1ecf736722bb257445", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e4142adfd3b550d4777f76af288979176", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e2abdef7676863389e216855de9a18408", "path": "GedaApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e20a809c42e21e462f98c637bf29f6c3b", "name": "App", "path": "App", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "a96b0e99fac1a0c806861643310f480e0eeb9f228a4b4054a7d4e4ce41f1c685", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e1f49a367e2663209984d3d95156c3b4d", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ee0a7a4c0448de7e55428506eb5123a30", "path": "Color+Geda.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ea2126c83db6039386dc47b880c9b8764", "path": "LinearGradient+Geda.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480eaedb0eb4de4c079001afb7ea342f6f9a", "path": "View+Extensions.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e6405e098924603d7f8a636aa5258b4b2", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e03791696e1da79a66af261d2ab1ff872", "path": "Constants.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e79fbbfb0c9688b49807947232578c131", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e93c2a385ac8fb1a3c74011a9dc9324cd", "path": "BottomActionBar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480eca17c33e963db05ddcd80c91c4398172", "path": "CompactCalendar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e510a68faac8f3a34386f2eca8b3e87cf", "path": "StackedTaskCards.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480eb755a2187aee789454be23a3c4c57849", "path": "TaskSectionHeader.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e7274ea433c861f72880657cdde70ff54", "path": "TodayReminderCard.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ebd03b4687b1d1b1f77891a4bbe381b7c", "path": "TopNavigationSwitch.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e9c3ea8a45fcbb836cc28eccb4f52f55c", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e427c8e4325ced4ab252313299044060b", "path": "CalendarView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480eafc5803a6d43649f0155cc95d1870fef", "path": "HomeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480edbbdd5a8e2235f5394af98d65b3ab59b", "path": "ProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e5b43271b7af1da1416129400961996d8", "path": "ReviewView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ec07ecffc32b00803ec34f3ce1dcc2747", "path": "ScannerView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e6a171fbd4cc792445f6bf92c9ec88314", "name": "Main", "path": "Main", "sourceTree": "<group>", "type": "group"}], "guid": "a96b0e99fac1a0c806861643310f480e7be3b02f31c22348dedc3f0e5f3cae8f", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "a96b0e99fac1a0c806861643310f480e225e3204e44156022bcf2b747ea984d6", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e3100b58548a0573d695283b41db3e33a", "name": "<PERSON><PERSON>", "path": "<PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}, {"guid": "a96b0e99fac1a0c806861643310f480eadaaee36ddc6392bc162272bfc70f8df", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "a96b0e99fac1a0c806861643310f480eaebe6dd2f5283aeca298914d18309adc", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "a96b0e99fac1a0c806861643310f480e", "path": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "targets": ["TARGET@v11_hash=560cdb2555d5338cf89d197b88db73bf"]}