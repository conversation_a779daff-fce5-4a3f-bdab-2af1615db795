{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "a96b0e99fac1a0c806861643310f480e51a26ea4bc78b6474ee0c55e8222952b", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "a96b0e99fac1a0c806861643310f480e6bcebdef059cbe1ecf736722bb257445", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480eb071422c3b59d35584abc6a90e5a4d43", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e4b6640f89b4d067c9b913db91a85b4e5", "path": "GedaApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e0960670daa265135916712a992558d83", "name": "App", "path": "App", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "wrapper.xcdatamodel", "guid": "a96b0e99fac1a0c806861643310f480e2fc4b33b4a748a457ee3b7e17602e273", "path": "Geda.xcdatamodel", "sourceTree": "<group>", "type": "file"}], "fileType": "wrapper.xcdatamodeld", "guid": "a96b0e99fac1a0c806861643310f480e7ff9c50fe10a7cbf2339c00755de8994", "name": "Geda.xcdatamodeld", "path": "Geda.xcdatamodeld", "sourceTree": "<group>", "type": "versionGroup"}], "guid": "a96b0e99fac1a0c806861643310f480e2b68ce5335fe51f15cf81ffecd1f4c9e", "name": "CoreData", "path": "CoreData", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e111bf2d36edc0db29b23d7de118ce2da", "path": "BaseViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ec608c33aab3beec7d3acf2ce3aec1960", "path": "FocusTimerViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480eaab270e9bd0b13aa42d0167c6eaa442f", "path": "HomeViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e1c39f37c99fa1655efa1c9eb42cfb6f0", "path": "PlanViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e1a048e4ef0860ad7ee64429ef7fcfea0", "path": "ReviewViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e3559111968e84f7efeed264f3575bb6d", "path": "TaskViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480eb5e8fbfbb98f61715ca1bebe17ceb190", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e0e172e9417a3a552e68cfe3ebdc0fe99", "path": "En<PERSON>.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e5517ec2b75c71084d22b54199ce80cc4", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "a96b0e99fac1a0c806861643310f480e9c969628ed332a2a07b8fc29ed2b8135", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e1e272f363ccdcfbdfed3dd2917dcdb70", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e6e71673dca9b9bb9ee7f43215c416797", "path": "PlanRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480efc007c25403e0ce53270a02576984963", "path": "PomodoroSessionRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ea484b4899bc5715793f7e648dc590960", "path": "RepositoryManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e5aac894a86fe068c7cc1283a0a30afd8", "path": "RepositoryProtocols.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e47f42144e64fb1985f62e6ffd13bf295", "path": "SubtaskRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480efe1d083fefadc5125df163bb9e91a8d6", "path": "TaskRepository.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e2bbcf23e7e98ba263cbc5855f86f5df8", "path": "UserRepository.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e72a49fb9fb72aaefe47bb47d9337aba4", "name": "Repositories", "path": "Repositories", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ee4f346f3cf33b95476ad61db9977a0d6", "path": "CoreDataManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480ea870c1248b868a5171ecc45c967d00f0", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e4d0d2465d49992b2f9b089c89f71241d", "path": "Color+Geda.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e8e75ac70564cf94813d2dfb35235c667", "path": "LinearGradient+Geda.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ee790266d8956d4413a248dac293cbef2", "path": "Typography+Geda.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ecec0b4297fe25b271e25c621721ff0ba", "path": "View+Extensions.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480eb41baa158f4482b79be30cb48b6f0191", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e90aeb47360fa5e2e8a8b36765746353f", "path": "Constants.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e95b37d6b0451c8c02beedde85f4156fc", "path": "DesignTokens.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e94011cc9a973dfaa6ffab9531e611f27", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e5b89eb16e104b5304cd3e7598a60de0f", "path": "BottomActionBar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480eabe17dc6caa2c1a2be065f7fac26d382", "path": "CircularProgressView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e4899bf901bea7be1ce4985d94fd56527", "path": "CompactCalendar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ee4e08750799c8564038edcd14538f5eb", "path": "GradientButton.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480eb906ee48302884eb58a4579c7d12b075", "path": "StackedTaskCards.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e21e3eb5f00711d65d2713c8234cde9d7", "path": "TaskSectionHeader.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e05e4bf95ae1e81cfd4d6a8d280ca4ea6", "path": "TodayReminderCard.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e51d68f9fe276e5bf1db567615b8c7921", "path": "TopNavigationSwitch.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480ef28e57d0608c7c0d7b116f53b75a0392", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480eab61b02a2f61dc91cac3205c5505e1ff", "path": "CalendarView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e1a9448a6fa58e5bdc5bd3920bd86c4da", "path": "HomeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e0aaa2f2ac3c97d3c9a0abfb61d70e000", "path": "ProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480ebb4d4632eadfe3ab1563da5ce74c56d4", "path": "ReviewView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a96b0e99fac1a0c806861643310f480e9226ef328651175efb4a9f5a80024985", "path": "ScannerView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e8d385c9398fc4b9a5b298512c68bd222", "name": "Main", "path": "Main", "sourceTree": "<group>", "type": "group"}], "guid": "a96b0e99fac1a0c806861643310f480e73b2c10c10e3dd2f793bf664fe793bc6", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "a96b0e99fac1a0c806861643310f480edb9613d2d7d804075b4dbfe0d320108e", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "a96b0e99fac1a0c806861643310f480e3100b58548a0573d695283b41db3e33a", "name": "<PERSON><PERSON>", "path": "<PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}, {"guid": "a96b0e99fac1a0c806861643310f480eadaaee36ddc6392bc162272bfc70f8df", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "a96b0e99fac1a0c806861643310f480eaebe6dd2f5283aeca298914d18309adc", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "a96b0e99fac1a0c806861643310f480e", "path": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "targets": ["TARGET@v11_hash=048b84e14f7957849df5b37a36346482"]}