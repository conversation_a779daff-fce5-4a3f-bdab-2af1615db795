{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib": {"is-mutated": true}, "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/_CodeSignature", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<Linked Binary Debug Dylib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib>", "<target-Geda-****************************************************************--begin-scanning>", "<target-Geda-****************************************************************--end>", "<target-Geda-****************************************************************--linker-inputs-ready>", "<target-Geda-****************************************************************--modules-ready>", "<workspace-Debug-iphonesimulator18.2-iphonesimulator--stale-file-removal>"], "outputs": ["<all>"]}, "<target-Geda-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/_CodeSignature", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Assets.car", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_signature", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/PkgInfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-project-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/empty-Geda.plist"], "roots": ["/tmp/Geda.dst", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products"], "outputs": ["<target-Geda-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>"]}, "<workspace-Debug-iphonesimulator18.2-iphonesimulator--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Debug-iphonesimulator18.2-iphonesimulator--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<ClangStatCache /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda.xcodeproj", "signature": "f5fbfa902918391b2eaf8640ab66c08c"}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-Geda-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Metadata.appintents>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList"], "outputs": ["<target-Geda-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-ChangePermissions>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-StripSymbols>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib>", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib>"], "outputs": ["<target-Geda-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-GenerateStubAPI>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-CodeSign>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-Validate>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-CopyAside>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-Geda-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-Geda-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--GeneratedFilesTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ProductStructureTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-Geda-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--RealityAssetsTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-project-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.hmap"], "outputs": ["<target-Geda-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/PkgInfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/empty-Geda.plist"], "outputs": ["<target-Geda-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--HeadermapTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleMapTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Geda-****************************************************************--InfoPlistTaskProducer>", "<target-Geda-****************************************************************--VersionPlistTaskProducer>", "<target-Geda-****************************************************************--SanitizerTaskProducer>", "<target-Geda-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Geda-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Geda-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Geda-****************************************************************--StubBinaryTaskProducer>", "<target-Geda-****************************************************************--TestTargetTaskProducer>", "<target-Geda-****************************************************************--TestHostTaskProducer>", "<target-Geda-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Geda-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Geda-****************************************************************--DocumentationTaskProducer>", "<target-Geda-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--start>", "<target-Geda-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>"], "outputs": ["<target-Geda-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--VersionPlistTaskProducer": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--VersionPlistTaskProducer>"]}, "P0:::Gate target-Geda-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-Geda-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Assets.car", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_signature", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json"], "outputs": ["<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-Geda-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-Geda-****************************************************************--generated-headers>"]}, "P0:::Gate target-Geda-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h"], "outputs": ["<target-Geda-****************************************************************--swift-generated-headers>"]}, "P0:target-Geda-****************************************************************-:Debug:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Metadata.appintents>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Info.plist", "--temp-dir-path", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/ssu", "--bundle-id", "com.ryan.Geda", "--product-path", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "--extracted-metadata-path", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Metadata.appintents", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "de4e7539492002294d31abe4be2f5bd0"}, "P0:target-Geda-****************************************************************-:Debug:CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Info.plist/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift/", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib>", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib>", "<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda normal>", "<TRIGGER: MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/_CodeSignature", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>"]}, "P0:target-Geda-****************************************************************-:Debug:CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Info.plist/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift/", "<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib>"]}, "P0:target-Geda-****************************************************************-:Debug:CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Info.plist/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift/", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib>", "<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib>"]}, "P0:target-Geda-****************************************************************-:Debug:CompileAssetCatalogVariant thinned /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned/"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--filter-for-thinning-device-configuration", "iPhone17,3", "--filter-for-device-os-version", "18.3.1", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "control-enabled": false, "signature": "414144e880d29412243c51d34773d91c"}, "P0:target-Geda-****************************************************************-:Debug:CompileAssetCatalogVariant unthinned /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned/"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "control-enabled": false, "signature": "1f2763d234c62b211fefa2eacd25b475"}, "P0:target-Geda-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>"], "deps": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-Geda-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "appintents-metadata", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Metadata.appintents>"]}, "P0:target-Geda-****************************************************************-:Debug:Gate target-Geda-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Geda.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-Geda-****************************************************************--begin-compiling>"]}, "P0:target-Geda-****************************************************************-:Debug:Gate target-Geda-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Geda.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-Geda-****************************************************************--begin-linking>"]}, "P0:target-Geda-****************************************************************-:Debug:Gate target-Geda-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Geda.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--begin-scanning>"]}, "P0:target-Geda-****************************************************************-:Debug:Gate target-Geda-****************************************************************--end": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--entry>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib>", "<CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned/", "<CopySwiftStdlib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Metadata.appintents>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Assets.car", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_signature", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned>", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/PkgInfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>", "<Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-project-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/empty-Geda.plist", "<target-Geda-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-Geda-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-Geda-****************************************************************--Barrier-ChangePermissions>", "<target-Geda-****************************************************************--Barrier-CodeSign>", "<target-Geda-****************************************************************--Barrier-CopyAside>", "<target-Geda-****************************************************************--Barrier-GenerateStubAPI>", "<target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Geda-****************************************************************--Barrier-RegisterProduct>", "<target-Geda-****************************************************************--Barrier-StripSymbols>", "<target-Geda-****************************************************************--Barrier-Validate>", "<target-Geda-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-Geda-****************************************************************--DocumentationTaskProducer>", "<target-Geda-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-Geda-****************************************************************--GeneratedFilesTaskProducer>", "<target-Geda-****************************************************************--HeadermapTaskProducer>", "<target-Geda-****************************************************************--InfoPlistTaskProducer>", "<target-Geda-****************************************************************--ModuleMapTaskProducer>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--ProductPostprocessingTaskProducer>", "<target-Geda-****************************************************************--ProductStructureTaskProducer>", "<target-Geda-****************************************************************--RealityAssetsTaskProducer>", "<target-Geda-****************************************************************--SanitizerTaskProducer>", "<target-Geda-****************************************************************--StubBinaryTaskProducer>", "<target-Geda-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-Geda-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-Geda-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-Geda-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-Geda-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-Geda-****************************************************************--TestHostTaskProducer>", "<target-Geda-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-Geda-****************************************************************--TestTargetTaskProducer>", "<target-Geda-****************************************************************--VersionPlistTaskProducer>", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-Geda-****************************************************************--generated-headers>", "<target-Geda-****************************************************************--swift-generated-headers>"], "outputs": ["<target-Geda-****************************************************************--end>"]}, "P0:target-Geda-****************************************************************-:Debug:Gate target-Geda-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Geda.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-Geda-****************************************************************--begin-compiling>"], "outputs": ["<target-Geda-****************************************************************--entry>"]}, "P0:target-Geda-****************************************************************-:Debug:Gate target-Geda-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************-Debug-iphonesimulator--arm64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Geda.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-Geda-****************************************************************--immediate>"]}, "P0:target-Geda-****************************************************************-:Debug:Gate target-Geda-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-ExecutorLinkFileList-normal-arm64.txt", "<Linked Binary /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList"], "outputs": ["<target-Geda-****************************************************************--linker-inputs-ready>"]}, "P0:target-Geda-****************************************************************-:Debug:Gate target-Geda-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h"], "outputs": ["<target-Geda-****************************************************************--modules-ready>"]}, "P0:target-Geda-****************************************************************-:Debug:Gate target-Geda-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned/", "<CopySwiftStdlib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Metadata.appintents>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Assets.car", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_signature", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "<target-Geda-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-Geda-****************************************************************--unsigned-product-ready>"]}, "P0:target-Geda-****************************************************************-:Debug:Gate target-Geda-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-Geda-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-Geda-****************************************************************--will-sign>"]}, "P0:target-Geda-****************************************************************-:Debug:GenerateAssetSymbols /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets/", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets", "--bundle-identifier", "com.ryan.Geda", "--generate-swift-asset-symbols", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "control-enabled": false, "signature": "d52364d8e6a2a6e13dd7f84c80fde2d2"}, "P0:target-Geda-****************************************************************-:Debug:LinkAssetCatalog /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content/Preview Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Assets.xcassets/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_signature", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Assets.car"], "deps": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_dependencies"}, "P0:target-Geda-****************************************************************-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-Geda-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/thinned>"]}, "P0:target-Geda-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_output/unthinned>"]}, "P0:target-Geda-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "inputs": ["<target-Geda-****************************************************************--start>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "<MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>", "<TRIGGER: MkDir /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>"]}, "P0:target-Geda-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Info.plist /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/empty-Geda.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Info.plist /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/empty-Geda.plist", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/empty-Geda.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/assetcatalog_generated_info.plist", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Info.plist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/PkgInfo"]}, "P0:target-Geda-****************************************************************-:Debug:ProcessProductPackaging  /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist", "<target-Geda-****************************************************************--ProductStructureTaskProducer>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent"]}, "P0:target-Geda-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "<target-Geda-****************************************************************--ProductStructureTaskProducer>", "<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "-o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "170d256ff5d5b57a243a2567f10d9d15"}, "P0:target-Geda-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "<target-Geda-****************************************************************--Barrier-CodeSign>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>"]}, "P0:target-Geda-****************************************************************-:Debug:SwiftDriver Compilation Geda normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation Geda normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-Geda-****************************************************************--generated-headers>", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.swiftconstvalues", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-Geda-****************************************************************-:Debug:Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app": {"tool": "shell", "description": "Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "<target-Geda-****************************************************************--Barrier-Validate>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "98eb5eb136e1544ea9246c59d82dd93d"}, "P0:target-Geda-****************************************************************-:Debug:Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Info.plist", "<target-Geda-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-Geda-****************************************************************--will-sign>", "<target-Geda-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>"], "outputs": ["<Validate /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app>"]}, "P0:target-Geda-****************************************************************-:Debug:ValidateDevelopmentAssets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Preview Content", "<target-Geda-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda-a96b0e99fac1a0c806861643310f480e-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-Geda-****************************************************************-:Debug:ConstructStubExecutorLinkFileList /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-ExecutorLinkFileList-normal-arm64.txt": {"tool": "construct-stub-executor-input-file-list", "description": "ConstructStubExecutorLinkFileList /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-ExecutorLinkFileList-normal-arm64.txt", "inputs": ["/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-ExecutorLinkFileList-normal-arm64.txt"]}, "P2:target-Geda-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo/", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo"]}, "P2:target-Geda-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json/", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.abi.json"]}, "P2:target-Geda-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc/", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftdoc"]}, "P2:target-Geda-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule/", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.swiftmodule/arm64-apple-ios-simulator.swiftmodule"]}, "P2:target-Geda-****************************************************************-:Debug:Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda normal", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibPath-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibInstallName-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-ExecutorLinkFileList-normal-arm64.txt", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda", "<Linked Binary /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda>", "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-O0", "-L/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-e", "___debug_blank_executor_main", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_dylib", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibPath-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_instlnm", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibInstallName-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-filelist", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-ExecutorLinkFileList-normal-arm64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib", "-o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "b08decc7cb5acd83aaeb63587fd50630"}, "P2:target-Geda-****************************************************************-:Debug:Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib normal", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GedaApp.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Color+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/LinearGradient+Geda.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/View+Extensions.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Constants.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/BottomActionBar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CompactCalendar.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/StackedTaskCards.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TaskSectionHeader.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TodayReminderCard.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/TopNavigationSwitch.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/CalendarView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/HomeView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ProfileView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ReviewView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/ScannerView.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator", "<target-Geda-****************************************************************--generated-headers>", "<target-Geda-****************************************************************--swift-generated-headers>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib", "<Linked Binary Debug Dylib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-O0", "-L/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-L/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator", "-filelist", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "-install_name", "@rpath/Geda.debug.dylib", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_lto.o", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "-<PERSON><PERSON><PERSON>", "-alias", "-<PERSON><PERSON><PERSON>", "_main", "-<PERSON><PERSON><PERSON>", "___debug_main_executable_dylib_entry_point", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat", "-o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/Geda.debug.dylib"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "deps": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_dependency_info.dat"], "deps-style": "dependency-info", "signature": "b7c021da144bfb9e7d87633af9aeb13e"}, "P2:target-Geda-****************************************************************-:Debug:Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib normal", "inputs": ["<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib>", "<TRIGGER: Ld /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-ios18.2-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-O0", "-L/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator", "-install_name", "@rpath/Geda.debug.dylib", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.app-Simulated.xcent.der", "-o", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Products/Debug-iphonesimulator/Geda.app/__preview.dylib"], "env": {}, "working-directory": "/Users/<USER>/Desktop/workspace/07-project/ios/Geda", "signature": "ea4154e14a3d6d48f409c88fbc3f963b"}, "P2:target-Geda-****************************************************************-:Debug:SwiftDriver Compilation Requirements Geda normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements Geda normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/ContentView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/App/GedaApp.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/Color+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/LinearGradient+Geda.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Extensions/View+Extensions.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Utils/Constants.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/BottomActionBar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/CompactCalendar.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/StackedTaskCards.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TaskSectionHeader.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TodayReminderCard.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Components/TopNavigationSwitch.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/CalendarView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/HomeView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ProfileView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ReviewView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/Geda/Views/Main/ScannerView.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-generated-files.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-own-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-target-headers.hmap", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-Geda-****************************************************************--copy-headers-completion>", "<target-Geda-****************************************************************--ModuleVerifierTaskProducer>", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftmodule", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftsourceinfo", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.abi.json", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.swiftdoc"]}, "P2:target-Geda-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "inputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-Swift.h", "<target-Geda-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Geda-Swift.h"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibInstallName-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibInstallName-normal-arm64.txt", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibInstallName-normal-arm64.txt"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibPath-normal-arm64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibPath-normal-arm64.txt", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-DebugDylibPath-normal-arm64.txt"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-non-framework-target-headers.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-non-framework-target-headers.hmap"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-target-headers.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-all-target-headers.hmap"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-generated-files.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-generated-files.hmap"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-own-target-headers.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-own-target-headers.hmap"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-project-headers.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda-project-headers.hmap"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.DependencyMetadataFileList"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.hmap", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Geda.hmap"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda-OutputFileMap.json"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.LinkFileList"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftConstValuesFileList"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda.SwiftFileList"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/Objects-normal/arm64/Geda_const_extract_protocols.json"]}, "P2:target-Geda-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/empty-Geda.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/empty-Geda.plist", "inputs": ["<target-Geda-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/workspace/07-project/ios/Geda/DerivedData/Build/Intermediates.noindex/Geda.build/Debug-iphonesimulator/Geda.build/empty-Geda.plist"]}}}