//
//  iOSCompatibilityTests.swift
//  CompatibilityTests
//
//  Created by AI Assistant on 2025/7/19.
//

import XCTest
import SwiftUI
@testable import Geda

/// iOS版本兼容性测试
/// 测试应用在不同iOS版本上的功能完整性和API兼容性
final class iOSCompatibilityTests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        app.launchArguments = ["--compatibility-testing"]
        app.launch()
    }
    
    override func tearDownWithError() throws {
        app = nil
    }
    
    // MARK: - iOS 18.2+ 新功能兼容性测试
    
    func testSwiftUI6_0Features() throws {
        // Given: 应用使用SwiftUI 6.0特性
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // When: 测试新的SwiftUI特性
        // 测试新的动画API
        let animatedElement = app.otherElements["AnimatedProgressRing"]
        if animatedElement.exists {
            // 验证动画效果正常
            XCTAssertTrue(animatedElement.isHittable, "SwiftUI 6.0动画组件应该正常工作")
        }
        
        // 测试新的布局系统
        let adaptiveLayout = app.otherElements["AdaptiveLayout"]
        if adaptiveLayout.exists {
            XCTAssertTrue(adaptiveLayout.exists, "SwiftUI 6.0布局系统应该正常工作")
        }
    }
    
    func testCoreDataiOS18_2Features() throws {
        // Given: 应用使用Core Data iOS 18.2特性
        
        // When: 测试Core Data新特性
        let expectation = XCTestExpectation(description: "Core Data iOS 18.2兼容性测试")
        
        DispatchQueue.global(qos: .background).async {
            // 测试数据模型兼容性
            let coreDataManager = CoreDataManager.shared
            
            // 验证数据模型加载正常
            XCTAssertNotNil(coreDataManager.viewContext, "Core Data上下文应该正常初始化")
            
            // 测试iOS 18.2的新查询功能
            do {
                let tasks = try coreDataManager.viewContext.fetch(Task.fetchRequest())
                XCTAssertNotNil(tasks, "Core Data查询应该正常工作")
                expectation.fulfill()
            } catch {
                XCTFail("Core Data查询失败: \(error)")
            }
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    func testUserNotificationsiOS18_2() throws {
        // Given: 应用使用iOS 18.2通知API
        
        // When: 测试通知API兼容性
        let expectation = XCTestExpectation(description: "通知API兼容性测试")
        
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            // Then: 验证通知设置API正常
            XCTAssertNotNil(settings, "通知设置应该可以获取")
            
            // 测试iOS 18.2的新通知特性
            if #available(iOS 18.2, *) {
                // 验证新的通知特性可用
                XCTAssertTrue(true, "iOS 18.2通知特性应该可用")
            }
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 3.0)
    }
    
    // MARK: - 向后兼容性测试
    
    func testBackwardCompatibility() throws {
        // Given: 应用需要向后兼容
        
        // When: 测试在最低支持版本上的功能
        if #available(iOS 18.2, *) {
            // 当前测试环境支持目标版本
            XCTAssertTrue(true, "当前环境支持iOS 18.2+")
        } else {
            XCTFail("测试环境版本过低，无法测试目标iOS版本")
        }
        
        // 测试基础功能在所有支持版本上都能工作
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5), "基础UI应该在所有支持版本上正常显示")
        
        // 测试Core Data基础功能
        let taskCreation = app.buttons["创建任务"]
        if taskCreation.exists {
            taskCreation.tap()
            
            let taskModal = app.otherElements["TaskCreationView"]
            XCTAssertTrue(taskModal.waitForExistence(timeout: 3), "任务创建功能应该在所有版本上正常工作")
            
            // 取消创建
            if app.buttons["取消"].exists {
                app.buttons["取消"].tap()
            }
        }
    }
    
    // MARK: - API可用性测试
    
    func testAPIAvailability() throws {
        // Given: 应用使用多个系统API
        
        // When & Then: 测试各个API的可用性
        
        // Speech Framework
        if #available(iOS 18.2, *) {
            XCTAssertTrue(true, "Speech Framework应该在iOS 18.2+可用")
        }
        
        // AVFoundation
        if #available(iOS 18.2, *) {
            XCTAssertTrue(true, "AVFoundation应该在iOS 18.2+可用")
        }
        
        // UserNotifications
        if #available(iOS 18.2, *) {
            XCTAssertTrue(true, "UserNotifications应该在iOS 18.2+可用")
        }
        
        // SwiftUI
        if #available(iOS 18.2, *) {
            XCTAssertTrue(true, "SwiftUI应该在iOS 18.2+可用")
        }
        
        // Combine
        if #available(iOS 18.2, *) {
            XCTAssertTrue(true, "Combine应该在iOS 18.2+可用")
        }
    }
    
    // MARK: - 权限系统兼容性测试
    
    func testPermissionSystemCompatibility() throws {
        // Given: 应用使用多种系统权限
        
        // When: 测试权限系统兼容性
        app.tabBars.buttons["个人中心"].tap()
        
        let profileView = app.otherElements["ProfileView"]
        XCTAssertTrue(profileView.waitForExistence(timeout: 3))
        
        // 测试相机权限兼容性
        let avatarButton = app.buttons["头像"]
        if avatarButton.exists {
            avatarButton.tap()
            
            // 验证权限系统正常工作
            let cameraOption = app.buttons["拍照"]
            let photoOption = app.buttons["从相册选择"]
            
            let hasPermissionOptions = cameraOption.exists || photoOption.exists
            XCTAssertTrue(hasPermissionOptions, "权限相关功能应该在iOS 18.2+正常显示")
            
            // 取消操作
            if app.buttons["取消"].exists {
                app.buttons["取消"].tap()
            }
        }
        
        // 测试通知权限兼容性
        let notificationSettings = app.cells["通知设置"]
        if notificationSettings.exists {
            notificationSettings.tap()
            
            // 验证通知权限界面正常
            let notificationView = app.otherElements["NotificationSettingsView"]
            if notificationView.waitForExistence(timeout: 2) {
                XCTAssertTrue(true, "通知权限设置应该正常工作")
                
                // 返回
                if app.buttons["返回"].exists {
                    app.buttons["返回"].tap()
                }
            }
        }
    }
    
    // MARK: - 系统集成兼容性测试
    
    func testSystemIntegrationCompatibility() throws {
        // Given: 应用与系统深度集成
        
        // When: 测试系统集成功能
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // 测试后台任务兼容性
        XCUIDevice.shared.press(.home)
        sleep(1)
        app.activate()
        
        // Then: 验证从后台恢复正常
        XCTAssertTrue(homeView.waitForExistence(timeout: 5), "从后台恢复应该在iOS 18.2+正常工作")
        
        // 测试多任务兼容性
        if UIDevice.current.userInterfaceIdiom == .pad {
            // iPad特定的多任务测试
            // 这里可以添加Split View相关测试
            XCTAssertTrue(true, "iPad多任务功能应该兼容")
        }
    }
    
    // MARK: - 性能兼容性测试
    
    func testPerformanceCompatibility() throws {
        // Given: 应用需要在不同iOS版本上保持性能
        
        // When & Then: 测试性能兼容性
        measure(metrics: [XCTApplicationLaunchMetric()]) {
            let testApp = XCUIApplication()
            testApp.launch()
        }
        
        // 测试内存使用兼容性
        measure(metrics: [XCTMemoryMetric()]) {
            // 执行一系列操作
            app.tabBars.buttons["计划管理"].tap()
            app.tabBars.buttons["统计分析"].tap()
            app.tabBars.buttons["个人中心"].tap()
            app.tabBars.buttons["今日任务"].tap()
        }
    }
    
    // MARK: - 数据格式兼容性测试
    
    func testDataFormatCompatibility() throws {
        // Given: 应用使用Core Data数据模型
        
        // When: 测试数据格式兼容性
        let expectation = XCTestExpectation(description: "数据格式兼容性测试")
        
        DispatchQueue.global(qos: .background).async {
            let coreDataManager = CoreDataManager.shared
            
            // 测试数据模型版本兼容性
            do {
                // 创建测试数据
                let context = coreDataManager.viewContext
                let task = Task(context: context)
                task.title = "兼容性测试任务"
                task.createdAt = Date()
                task.priority = Priority.medium.rawValue
                
                try context.save()
                
                // 验证数据保存和读取正常
                let fetchRequest = Task.fetchRequest()
                let tasks = try context.fetch(fetchRequest)
                
                XCTAssertTrue(tasks.contains { $0.title == "兼容性测试任务" }, "数据格式应该兼容")
                
                expectation.fulfill()
            } catch {
                XCTFail("数据格式兼容性测试失败: \(error)")
            }
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    // MARK: - 网络兼容性测试
    
    func testNetworkCompatibility() throws {
        // Given: 应用使用网络功能
        
        // When: 测试网络API兼容性
        let expectation = XCTestExpectation(description: "网络兼容性测试")
        
        // 测试URLSession兼容性
        let url = URL(string: "https://www.apple.com")!
        let task = URLSession.shared.dataTask(with: url) { data, response, error in
            // Then: 验证网络请求正常
            if let error = error {
                print("网络请求错误（可能是网络问题）: \(error)")
            } else {
                XCTAssertNotNil(data, "网络请求应该返回数据")
                XCTAssertNotNil(response, "网络请求应该返回响应")
            }
            expectation.fulfill()
        }
        task.resume()
        
        wait(for: [expectation], timeout: 10.0)
    }
    
    // MARK: - 安全性兼容性测试
    
    func testSecurityCompatibility() throws {
        // Given: 应用使用安全相关功能
        
        // When: 测试安全API兼容性
        
        // 测试Keychain兼容性
        let keychainQuery: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: "test_account",
            kSecValueData as String: "test_data".data(using: .utf8)!
        ]
        
        let status = SecItemAdd(keychainQuery as CFDictionary, nil)
        
        // Then: 验证Keychain操作兼容
        XCTAssertTrue(status == errSecSuccess || status == errSecDuplicateItem, 
                     "Keychain操作应该在iOS 18.2+正常工作")
        
        // 清理测试数据
        SecItemDelete(keychainQuery as CFDictionary)
    }
}