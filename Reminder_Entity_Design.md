# Reminder实体设计方案

## 1. Core Data模型定义

```xml
<entity name="Reminder" representedClassName="Reminder" syncable="YES" codeGenerationType="class">
    <attribute name="id" attributeType="String"/>
    <attribute name="title" attributeType="String"/>
    <attribute name="reminderDescription" optional="YES" attributeType="String"/>
    <attribute name="reminderDate" attributeType="Date" usesScalarValueType="NO"/>
    <attribute name="reminderTime" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
    <attribute name="isCompleted" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
    <attribute name="isRecurring" attributeType="Boolean" defaultValueString="NO" usesScalarValueType="YES"/>
    <attribute name="recurringPattern" optional="YES" attributeType="String"/>
    <attribute name="priority" attributeType="String" defaultValueString="medium"/>
    <attribute name="category" optional="YES" attributeType="String"/>
    <attribute name="isNotificationEnabled" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
    <attribute name="notificationOffset" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
    <attribute name="createdAt" attributeType="Date" usesScalarValueType="NO"/>
    <attribute name="updatedAt" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
    <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="User" inverseName="reminders" inverseEntity="User"/>
</entity>
```

## 2. User实体关系更新

```xml
<!-- 在User实体中添加 -->
<relationship name="reminders" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Reminder" inverseName="user" inverseEntity="Reminder"/>
```

## 3. 字段说明

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | String | 唯一标识符 |
| title | String | 提醒标题 |
| reminderDescription | String? | 详细描述（可选）|
| reminderDate | Date | 提醒日期 |
| reminderTime | Date? | 具体提醒时间（可选，默认全天）|
| isCompleted | Bool | 是否已完成 |
| isRecurring | Bool | 是否重复提醒 |
| recurringPattern | String? | 重复模式（daily/weekly/monthly/yearly）|
| priority | String | 优先级（high/medium/low）|
| category | String? | 分类（生日/考试/会议等）|
| isNotificationEnabled | Bool | 是否启用通知 |
| notificationOffset | Int32 | 提前通知时间（分钟）|

## 4. 与Task的区别

| 特性 | Task | Reminder |
|------|------|----------|
| 用途 | 需要专注时间完成的工作 | 简单的时间提醒通知 |
| 番茄钟 | ✅ 必须设置番茄钟数量 | ❌ 无需番茄钟 |
| 执行性 | ✅ 可执行，有"开始"按钮 | ❌ 不可执行，只是提醒 |
| 时间属性 | startTime + endTime | reminderDate + reminderTime |
| 子任务 | ✅ 支持子任务拆解 | ❌ 无子任务概念 |
| 计划归属 | ✅ 可归属于Plan | ❌ 独立存在，不归属计划 |
| 重复性 | ❌ 一次性任务为主 | ✅ 支持重复提醒 |

## 5. UI显示区别

### 在HomeView今日列表中的显示：

**Task显示:**
```
[📋 任务卡片]
  标题: 复习第一章
  时间: 09:00-11:00
  番茄钟: 🍅 x 3
  [▶️ 开始] 按钮
```

**Reminder显示:**
```
[🔔 提醒条目]
  标题: 张同学生日
  时间: 今天 全天
  分类: 生日提醒
  [无操作按钮，仅显示]
```