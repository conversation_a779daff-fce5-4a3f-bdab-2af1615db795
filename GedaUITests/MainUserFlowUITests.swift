//
//  MainUserFlowUITests.swift
//  GedaUITests
//
//  Created by AI Assistant on 2025/7/19.
//

import XCTest

/// 主要用户流程UI测试
/// 测试应用的核心用户交互路径
final class MainUserFlowUITests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        
        // 设置测试环境
        app.launchArguments = ["--uitesting"]
        app.launchEnvironment["ANIMATION_SPEED"] = "0" // 禁用动画以提高测试速度
        app.launch()
    }
    
    override func tearDownWithError() throws {
        app = nil
    }
    
    // MARK: - 应用启动流程测试
    
    func testAppLaunch() throws {
        // Given: 应用已启动
        
        // When: 检查主要UI元素
        let homeView = app.otherElements["HomeView"]
        let tabBar = app.tabBars.firstMatch
        
        // Then: 验证关键界面元素存在
        XCTAssertTrue(homeView.exists, "主页应该显示")
        XCTAssertTrue(tabBar.exists, "底部标签栏应该显示")
        
        // 验证标签栏包含所有主要功能
        XCTAssertTrue(app.tabBars.buttons["今日任务"].exists)
        XCTAssertTrue(app.tabBars.buttons["计划管理"].exists)
        XCTAssertTrue(app.tabBars.buttons["统计分析"].exists)
        XCTAssertTrue(app.tabBars.buttons["个人中心"].exists)
    }
    
    // MARK: - 任务管理流程测试
    
    func testCreateTaskFlow() throws {
        // Given: 在主页
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // When: 点击创建任务按钮
        let createTaskButton = app.buttons["创建任务"]
        if createTaskButton.exists {
            createTaskButton.tap()
        } else {
            // 如果是浮动按钮
            app.buttons["+"].tap()
        }
        
        // Then: 应该显示任务创建界面
        let taskCreationView = app.otherElements["TaskCreationView"]
        XCTAssertTrue(taskCreationView.waitForExistence(timeout: 3))
        
        // When: 填写任务信息
        let titleField = app.textFields["任务标题"]
        if titleField.exists {
            titleField.tap()
            titleField.typeText("UI测试任务")
        }
        
        // 设置时间（简化处理）
        if app.buttons["设置时间"].exists {
            app.buttons["设置时间"].tap()
            // 选择时间后确认
            if app.buttons["确认"].exists {
                app.buttons["确认"].tap()
            }
        }
        
        // When: 保存任务
        let saveButton = app.buttons["保存"]
        if saveButton.exists {
            saveButton.tap()
        }
        
        // Then: 应该返回主页并显示新任务
        XCTAssertTrue(homeView.waitForExistence(timeout: 3))
        
        // 验证任务列表中有新创建的任务
        let taskItem = app.cells.containing(.staticText, identifier: "UI测试任务").firstMatch
        XCTAssertTrue(taskItem.waitForExistence(timeout: 2), "新创建的任务应该在列表中显示")
    }
    
    func testTaskCompletionFlow() throws {
        // Given: 确保有任务存在
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // 查找第一个任务项
        let firstTaskCell = app.cells.firstMatch
        if !firstTaskCell.exists {
            // 如果没有任务，先创建一个
            try testCreateTaskFlow()
        }
        
        // When: 点击任务的完成按钮
        let taskCell = app.cells.firstMatch
        XCTAssertTrue(taskCell.exists, "应该存在至少一个任务")
        
        // 查找完成按钮（可能是复选框或按钮）
        let completeButton = taskCell.buttons["完成"].firstMatch
        if completeButton.exists {
            completeButton.tap()
        } else {
            // 如果是复选框形式
            let checkbox = taskCell.buttons["checkbox"].firstMatch
            if checkbox.exists {
                checkbox.tap()
            }
        }
        
        // Then: 验证任务状态已更新
        // 任务应该显示为已完成状态（可能有视觉变化）
        let completedIndicator = taskCell.images["checkmark"]
        XCTAssertTrue(completedIndicator.waitForExistence(timeout: 2), "已完成的任务应该显示完成标识")
    }
    
    // MARK: - 标签页导航测试
    
    func testTabBarNavigation() throws {
        // Given: 应用已启动
        let tabBar = app.tabBars.firstMatch
        XCTAssertTrue(tabBar.exists)
        
        // When & Then: 测试每个标签页切换
        
        // 计划管理
        app.tabBars.buttons["计划管理"].tap()
        let planView = app.otherElements["PlanView"]
        XCTAssertTrue(planView.waitForExistence(timeout: 3), "计划管理页面应该显示")
        
        // 统计分析
        app.tabBars.buttons["统计分析"].tap()
        let analyticsView = app.otherElements["AnalyticsView"]
        XCTAssertTrue(analyticsView.waitForExistence(timeout: 3), "统计分析页面应该显示")
        
        // 个人中心
        app.tabBars.buttons["个人中心"].tap()
        let profileView = app.otherElements["ProfileView"]
        XCTAssertTrue(profileView.waitForExistence(timeout: 3), "个人中心页面应该显示")
        
        // 返回今日任务
        app.tabBars.buttons["今日任务"].tap()
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 3), "今日任务页面应该显示")
    }
    
    // MARK: - 计划管理流程测试
    
    func testPlanCreationFlow() throws {
        // Given: 导航到计划管理页面
        app.tabBars.buttons["计划管理"].tap()
        let planView = app.otherElements["PlanView"]
        XCTAssertTrue(planView.waitForExistence(timeout: 3))
        
        // When: 创建新计划
        let createPlanButton = app.buttons["创建计划"]
        if createPlanButton.exists {
            createPlanButton.tap()
        } else {
            app.buttons["+"].tap()
        }
        
        // Then: 应该显示计划创建界面
        let planCreationView = app.otherElements["PlanCreationView"]
        XCTAssertTrue(planCreationView.waitForExistence(timeout: 3))
        
        // When: 填写计划信息
        let planTitleField = app.textFields["计划名称"]
        if planTitleField.exists {
            planTitleField.tap()
            planTitleField.typeText("UI测试计划")
        }
        
        let planDescField = app.textViews["计划描述"]
        if planDescField.exists {
            planDescField.tap()
            planDescField.typeText("这是一个UI测试创建的计划")
        }
        
        // When: 保存计划
        let saveButton = app.buttons["保存"]
        if saveButton.exists {
            saveButton.tap()
        }
        
        // Then: 应该返回计划列表并显示新计划
        XCTAssertTrue(planView.waitForExistence(timeout: 3))
        
        let planItem = app.cells.containing(.staticText, identifier: "UI测试计划").firstMatch
        XCTAssertTrue(planItem.waitForExistence(timeout: 2), "新创建的计划应该在列表中显示")
    }
    
    // MARK: - 设置和配置测试
    
    func testSettingsAccess() throws {
        // Given: 导航到个人中心
        app.tabBars.buttons["个人中心"].tap()
        let profileView = app.otherElements["ProfileView"]
        XCTAssertTrue(profileView.waitForExistence(timeout: 3))
        
        // When: 查找并点击设置按钮
        let settingsButton = app.buttons["设置"]
        if settingsButton.exists {
            settingsButton.tap()
            
            // Then: 应该显示设置界面
            let settingsView = app.otherElements["SettingsView"]
            XCTAssertTrue(settingsView.waitForExistence(timeout: 3), "设置页面应该显示")
            
            // 验证主要设置选项存在
            XCTAssertTrue(app.cells["通知设置"].exists, "通知设置选项应该存在")
            XCTAssertTrue(app.cells["主题设置"].exists || app.cells["外观设置"].exists, "主题设置选项应该存在")
        }
    }
    
    // MARK: - 数据同步和刷新测试
    
    func testPullToRefresh() throws {
        // Given: 在主页
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // When: 执行下拉刷新
        let firstCell = app.cells.firstMatch
        if firstCell.exists {
            let start = firstCell.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 0.5))
            let finish = firstCell.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 1.5))
            start.press(forDuration: 0, thenDragTo: finish)
        } else {
            // 如果没有cell，在整个视图上执行下拉
            let start = homeView.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 0.3))
            let finish = homeView.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 0.7))
            start.press(forDuration: 0, thenDragTo: finish)
        }
        
        // Then: 应该触发刷新（可能有loading指示器）
        let refreshIndicator = app.activityIndicators["刷新中"]
        if refreshIndicator.exists {
            // 等待刷新完成
            XCTAssertFalse(refreshIndicator.waitForExistence(timeout: 5), "刷新应该在5秒内完成")
        }
    }
    
    // MARK: - 性能测试
    
    func testAppLaunchPerformance() throws {
        // 测试应用启动性能
        measure(metrics: [XCTApplicationLaunchMetric()]) {
            XCUIApplication().launch()
        }
    }
    
    func testScrollPerformance() throws {
        // Given: 确保在有内容的页面
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // When & Then: 测试滚动性能
        measure(metrics: [XCTOSSignpostMetric.scrollingAndDecelerationMetric]) {
            // 执行滚动操作
            app.swipeUp()
            app.swipeDown()
            app.swipeUp()
            app.swipeDown()
        }
    }
    
    // MARK: - 错误处理测试
    
    func testOfflineMode() throws {
        // 注意：这个测试需要模拟网络状态
        // 在实际测试中可能需要网络条件模拟工具
        
        // Given: 应用处于离线状态（需要在测试配置中设置）
        app.launchEnvironment["NETWORK_STATUS"] = "offline"
        app.launch()
        
        // When: 尝试加载数据
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // Then: 应该显示适当的离线提示或缓存数据
        // 具体实现取决于应用的离线策略
        let offlineIndicator = app.staticTexts["离线模式"] 
        let cachedDataIndicator = app.staticTexts["显示缓存数据"]
        
        let hasOfflineHandling = offlineIndicator.exists || cachedDataIndicator.exists
        XCTAssertTrue(hasOfflineHandling, "应用应该有离线状态处理")
    }
}