//
//  IntegrationTests.swift
//  GedaUITests
//
//  Created by AI Assistant on 2025/7/19.
//

import XCTest
import UserNotifications
import AVFoundation
import Speech

/// 集成测试类
/// 测试通知、语音、相机等系统功能的集成
final class IntegrationTests: XCTestCase {
    
    var app: XCUIApplication!
    
    override func setUpWithError() throws {
        continueAfterFailure = false
        app = XCUIApplication()
        app.launchArguments = ["--integration-testing"]
        app.launch()
    }
    
    override func tearDownWithError() throws {
        app = nil
    }
    
    // MARK: - 通知功能集成测试
    
    func testNotificationPermissionRequest() throws {
        // Given: 首次启动应用
        
        // When: 导航到需要通知权限的功能
        app.tabBars.buttons["个人中心"].tap()
        
        let notificationSettings = app.cells["通知设置"]
        if notificationSettings.exists {
            notificationSettings.tap()
            
            // Then: 应该请求通知权限
            let enableNotificationButton = app.buttons["开启通知"]
            if enableNotificationButton.exists {
                enableNotificationButton.tap()
                
                // 系统权限弹窗会出现（无法直接测试，但可以验证按钮状态变化）
                // 等待一段时间让权限请求完成
                sleep(2)
                
                // 验证按钮状态或界面变化
                let notificationStatus = app.staticTexts["通知已开启"] 
                let notificationDenied = app.staticTexts["通知已拒绝"]
                
                let hasNotificationStatus = notificationStatus.exists || notificationDenied.exists
                XCTAssertTrue(hasNotificationStatus, "应该显示通知权限状态")
            }
        }
    }
    
    func testTaskReminderNotification() throws {
        // Given: 创建一个带提醒的任务
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // 创建任务
        let createTaskButton = app.buttons["创建任务"] 
        if createTaskButton.exists {
            createTaskButton.tap()
        } else {
            app.buttons["+"].tap()
        }
        
        let taskCreationView = app.otherElements["TaskCreationView"]
        XCTAssertTrue(taskCreationView.waitForExistence(timeout: 3))
        
        // When: 设置任务提醒
        let titleField = app.textFields["任务标题"]
        if titleField.exists {
            titleField.tap()
            titleField.typeText("提醒测试任务")
        }
        
        let reminderSwitch = app.switches["启用提醒"]
        if reminderSwitch.exists {
            if reminderSwitch.value as? String != "1" {
                reminderSwitch.tap()
            }
        }
        
        // 设置提醒时间为几秒后（用于测试）
        let reminderTimeButton = app.buttons["设置提醒时间"]
        if reminderTimeButton.exists {
            reminderTimeButton.tap()
            // 选择一个很近的时间用于测试
            // 具体实现取决于时间选择器的UI
        }
        
        // 保存任务
        app.buttons["保存"].tap()
        
        // Then: 验证提醒已设置
        XCTAssertTrue(homeView.waitForExistence(timeout: 3))
        
        let taskWithReminder = app.cells.containing(.staticText, identifier: "提醒测试任务").firstMatch
        XCTAssertTrue(taskWithReminder.exists, "带提醒的任务应该显示")
        
        // 验证提醒图标
        let reminderIcon = taskWithReminder.images["bell"]
        XCTAssertTrue(reminderIcon.exists, "任务应该显示提醒图标")
    }
    
    // MARK: - 语音功能集成测试
    
    func testVoiceInputPermission() throws {
        // Given: 导航到支持语音输入的界面
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // 创建任务以访问语音输入
        let createTaskButton = app.buttons["创建任务"]
        if createTaskButton.exists {
            createTaskButton.tap()
        } else {
            app.buttons["+"].tap()
        }
        
        let taskCreationView = app.otherElements["TaskCreationView"]
        XCTAssertTrue(taskCreationView.waitForExistence(timeout: 3))
        
        // When: 尝试使用语音输入
        let voiceInputButton = app.buttons["语音输入"]
        if voiceInputButton.exists {
            voiceInputButton.tap()
            
            // Then: 应该请求麦克风权限
            sleep(2) // 等待权限请求
            
            // 验证语音输入状态
            let voiceRecording = app.staticTexts["正在录音"]
            let voicePermissionDenied = app.staticTexts["麦克风权限已拒绝"]
            let voiceReady = app.buttons["开始录音"]
            
            let hasVoiceStatus = voiceRecording.exists || voicePermissionDenied.exists || voiceReady.exists
            XCTAssertTrue(hasVoiceStatus, "应该显示语音输入状态")
            
            // 如果正在录音，停止录音
            if voiceRecording.exists {
                app.buttons["停止录音"].tap()
            }
        }
        
        // 取消创建
        if app.buttons["取消"].exists {
            app.buttons["取消"].tap()
        }
    }
    
    func testVoiceCommandRecognition() throws {
        // 注意：这个测试在实际CI环境中可能无法完全执行
        // 因为需要真实的音频输入
        
        // Given: 在支持语音命令的界面
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // When: 激活语音命令（如果有全局语音功能）
        let voiceCommandButton = app.buttons["语音助手"]
        if voiceCommandButton.exists {
            voiceCommandButton.tap()
            
            // Then: 应该显示语音识别界面
            let voiceRecognitionView = app.otherElements["VoiceRecognitionView"]
            XCTAssertTrue(voiceRecognitionView.waitForExistence(timeout: 3), "语音识别界面应该显示")
            
            // 验证语音识别提示
            let listeningIndicator = app.staticTexts["正在听取指令"]
            XCTAssertTrue(listeningIndicator.exists || app.staticTexts["请说出指令"].exists, "应该显示语音识别提示")
            
            // 模拟语音识别完成（在实际环境中这会通过音频输入）
            // 这里只能测试UI响应
            sleep(1)
            
            // 取消语音识别
            if app.buttons["取消"].exists {
                app.buttons["取消"].tap()
            }
        }
    }
    
    // MARK: - 相机功能集成测试
    
    func testCameraPermissionRequest() throws {
        // Given: 导航到需要相机的功能
        app.tabBars.buttons["个人中心"].tap()
        let profileView = app.otherElements["ProfileView"]
        XCTAssertTrue(profileView.waitForExistence(timeout: 3))
        
        // When: 尝试更换头像（需要相机权限）
        let avatarButton = app.buttons["头像"]
        if avatarButton.exists {
            avatarButton.tap()
            
            let cameraOption = app.buttons["拍照"]
            if cameraOption.exists {
                cameraOption.tap()
                
                // Then: 应该请求相机权限
                sleep(2) // 等待权限请求
                
                // 验证相机相关状态
                let cameraView = app.otherElements["相机界面"]
                let cameraPermissionDenied = app.alerts["相机权限"]
                let cameraReady = app.buttons["拍照按钮"]
                
                let hasCameraResponse = cameraView.exists || cameraPermissionDenied.exists || cameraReady.exists
                XCTAssertTrue(hasCameraResponse, "应该有相机权限相关响应")
                
                // 如果有权限弹窗，处理它
                if cameraPermissionDenied.exists {
                    app.alerts.buttons["确定"].tap()
                }
                
                // 如果相机界面打开，关闭它
                if cameraView.exists || cameraReady.exists {
                    app.buttons["取消"].tap()
                }
            }
        }
    }
    
    func testPhotoLibraryAccess() throws {
        // Given: 在需要访问相册的界面
        app.tabBars.buttons["个人中心"].tap()
        let profileView = app.otherElements["ProfileView"]
        XCTAssertTrue(profileView.waitForExistence(timeout: 3))
        
        // When: 尝试从相册选择照片
        let avatarButton = app.buttons["头像"]
        if avatarButton.exists {
            avatarButton.tap()
            
            let photoLibraryOption = app.buttons["从相册选择"]
            if photoLibraryOption.exists {
                photoLibraryOption.tap()
                
                // Then: 应该打开相册或请求相册权限
                sleep(2)
                
                let photoLibraryView = app.otherElements["照片选择器"]
                let photoPermissionAlert = app.alerts["照片权限"]
                let photosGrid = app.collectionViews.firstMatch
                
                let hasPhotoLibraryResponse = photoLibraryView.exists || photoPermissionAlert.exists || photosGrid.exists
                XCTAssertTrue(hasPhotoLibraryResponse, "应该打开相册或显示权限请求")
                
                // 如果有权限弹窗，处理它
                if photoPermissionAlert.exists {
                    app.alerts.buttons["确定"].tap()
                }
                
                // 如果相册打开，关闭它
                if photoLibraryView.exists || photosGrid.exists {
                    app.buttons["取消"].tap()
                }
            }
        }
    }
    
    // MARK: - 数据持久化集成测试
    
    func testDataPersistence() throws {
        // Given: 创建一些测试数据
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // 创建一个任务
        let createTaskButton = app.buttons["创建任务"]
        if createTaskButton.exists {
            createTaskButton.tap()
        } else {
            app.buttons["+"].tap()
        }
        
        let taskCreationView = app.otherElements["TaskCreationView"]
        XCTAssertTrue(taskCreationView.waitForExistence(timeout: 3))
        
        let titleField = app.textFields["任务标题"]
        if titleField.exists {
            titleField.tap()
            titleField.typeText("持久化测试任务")
        }
        
        app.buttons["保存"].tap()
        XCTAssertTrue(homeView.waitForExistence(timeout: 3))
        
        // When: 重启应用
        app.terminate()
        app.launch()
        
        // Then: 数据应该仍然存在
        let persistentHomeView = app.otherElements["HomeView"]
        XCTAssertTrue(persistentHomeView.waitForExistence(timeout: 5))
        
        let persistedTask = app.cells.containing(.staticText, identifier: "持久化测试任务").firstMatch
        XCTAssertTrue(persistedTask.waitForExistence(timeout: 3), "重启后任务数据应该仍然存在")
    }
    
    // MARK: - 网络集成测试
    
    func testNetworkConnectivity() throws {
        // Given: 应用已启动
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // When: 触发需要网络的操作（如数据同步）
        // 下拉刷新通常会触发网络请求
        let firstCell = app.cells.firstMatch
        if firstCell.exists {
            let start = firstCell.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 0.5))
            let finish = firstCell.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 1.5))
            start.press(forDuration: 0, thenDragTo: finish)
        }
        
        // Then: 应该有网络活动指示
        let networkIndicator = app.activityIndicators["网络加载中"]
        let refreshIndicator = app.activityIndicators["刷新中"]
        let syncCompleted = app.staticTexts["同步完成"]
        let syncFailed = app.staticTexts["网络连接失败"]
        
        let hasNetworkActivity = networkIndicator.exists || refreshIndicator.exists || 
                                syncCompleted.waitForExistence(timeout: 10) || 
                                syncFailed.waitForExistence(timeout: 10)
        
        XCTAssertTrue(hasNetworkActivity, "应该有网络活动或结果提示")
    }
    
    // MARK: - 系统集成测试
    
    func testBackgroundModeHandling() throws {
        // Given: 应用在前台运行
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // When: 模拟应用进入后台
        XCUIDevice.shared.press(.home)
        sleep(2)
        
        // 重新激活应用
        app.activate()
        
        // Then: 应用应该正确恢复状态
        XCTAssertTrue(homeView.waitForExistence(timeout: 5), "从后台恢复后应该显示主页")
        
        // 验证数据状态没有丢失
        if app.cells.count > 0 {
            XCTAssertTrue(app.cells.firstMatch.exists, "后台恢复后数据应该保持")
        }
    }
    
    func testMemoryWarningHandling() throws {
        // 注意：内存警告模拟在实际测试中比较困难
        // 这里主要测试应用在内存压力下的基本功能
        
        // Given: 应用正常运行
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // When: 执行一些内存密集型操作
        // 快速在不同页面间切换，模拟内存压力
        for _ in 0..<5 {
            app.tabBars.buttons["计划管理"].tap()
            app.tabBars.buttons["统计分析"].tap()
            app.tabBars.buttons["个人中心"].tap()
            app.tabBars.buttons["今日任务"].tap()
        }
        
        // Then: 应用应该仍然响应正常
        XCTAssertTrue(homeView.exists, "在内存压力下应用应该仍然正常工作")
        
        // 验证基本功能仍然可用
        let tabBar = app.tabBars.firstMatch
        XCTAssertTrue(tabBar.isHittable, "标签栏应该仍然可交互")
    }
    
    // MARK: - 性能集成测试
    
    func testDataLoadingPerformance() throws {
        // Given: 准备测试大量数据加载
        
        // When & Then: 测试数据加载性能
        measure(metrics: [XCTCPUMetric(), XCTMemoryMetric()]) {
            // 触发数据加载
            let homeView = app.otherElements["HomeView"]
            XCTAssertTrue(homeView.waitForExistence(timeout: 5))
            
            // 切换到可能有大量数据的页面
            app.tabBars.buttons["统计分析"].tap()
            let analyticsView = app.otherElements["AnalyticsView"]
            XCTAssertTrue(analyticsView.waitForExistence(timeout: 5))
            
            // 返回主页
            app.tabBars.buttons["今日任务"].tap()
            XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        }
    }
    
    func testUIResponsivenessUnderLoad() throws {
        // Given: 应用正常运行
        let homeView = app.otherElements["HomeView"]
        XCTAssertTrue(homeView.waitForExistence(timeout: 5))
        
        // When & Then: 测试UI在负载下的响应性
        measure(metrics: [XCTOSSignpostMetric.navigationTransitionMetric]) {
            // 快速执行UI操作
            app.tabBars.buttons["计划管理"].tap()
            app.tabBars.buttons["统计分析"].tap()
            app.tabBars.buttons["个人中心"].tap()
            app.tabBars.buttons["今日任务"].tap()
        }
    }
}