<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咯嗒应用 - 计划管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-50">
    <div class="app-shell rounded-3xl">
        <div class="page">
            <!-- 状态栏 -->
            <header class="flex-shrink-0 px-6 pt-3 pb-2">
                <div class="flex justify-between items-center text-xs font-bold text-gray-800">
                    <span>12:00</span>
                    <div class="flex items-center space-x-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M5 12.55a11 11 0 0 1 14.08 0"></path>
                            <path d="M1.42 9a16 16 0 0 1 21.16 0"></path>
                            <path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path>
                            <path d="M12 20h.01"></path>
                        </svg>
                        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18.667 6.002a2.333 2.333 0 0 1 2.333 2.333v7.333a2.333 2.333 0 0 1-2.333-2.333H5.333a2.333 2.333 0 0 1-2.333-2.333V8.335a2.333 2.333 0 0 1 2.333-2.333h13.334zM22.167 8.335a1.167 1.167 0 0 0-1.167-1.167h-.833v9.667h.833a1.167 1.167 0 0 0 1.167-1.167V8.335z"></path>
                        </svg>
                    </div>
                </div>
            </header>

            <!-- 主内容区 -->
            <div class="flex-grow overflow-y-auto main-content pb-6">
                <!-- 顶部导航 -->
                <div class="px-6 pt-3 flex justify-between items-center">
                    <div class="bg-gray-100 p-1 rounded-full flex items-center">
                        <button class="px-5 py-2 text-base font-bold text-gray-500 hover:text-gray-800 transition-all duration-300" onclick="window.location.href='home.html'">日程</button>
                        <button class="px-5 py-2 text-base font-bold bg-white text-indigo-600 rounded-full shadow-md transition-all duration-300">计划</button>
                    </div>
                </div>

                <!-- 导入和探索按钮 -->
                <div class="px-6 mt-4 grid grid-cols-2 gap-4">
                    <button type="button" class="flex items-center justify-center space-x-2 bg-indigo-50 text-indigo-600 font-semibold py-3 rounded-xl hover:bg-indigo-100 transition-all duration-200">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                        </svg>
                        <span>导入分享码</span>
                    </button>
                    <button class="flex items-center justify-center space-x-2 bg-indigo-50 text-indigo-600 font-semibold py-3 rounded-xl hover:bg-indigo-100 transition-all duration-200">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0l2-2m-2 2l-2-2" />
                        </svg>
                        <span>探索官方模板</span>
                    </button>
                </div>

                <!-- 进行中的计划 -->
                <div class="px-6 mt-8">
                    <h3 class="text-lg font-bold text-gray-800">进行中</h3>
                    <div class="mt-4 space-y-4">
                        <div class="custom-card p-4 cursor-pointer hover:shadow-lg transition-shadow" onclick="window.location.href='plan-detail-1.html'">
                            <div class="flex justify-between items-center">
                                <span class="font-bold text-gray-800">考研冲刺</span>
                                <button class="text-gray-400 hover:text-gray-600" onclick="event.stopPropagation(); showShareMenu()">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                    </svg>
                                </button>
                            </div>
                            <div class="mt-3 flex items-center space-x-3">
                                <span class="text-xs text-gray-500">进度</span>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="h-2.5 rounded-full gradient-button" style="width: 65%"></div>
                                </div>
                                <span class="text-xs font-semibold text-gray-500">65%</span>
                            </div>
                        </div>

                        <div class="custom-card p-4 cursor-pointer hover:shadow-lg transition-shadow" onclick="window.location.href='plan-detail-2.html'">
                            <div class="flex justify-between items-center">
                                <span class="font-bold text-gray-800">健身增肌计划</span>
                                <button class="text-gray-400 hover:text-gray-600" onclick="event.stopPropagation(); showShareMenu()">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                    </svg>
                                </button>
                            </div>
                            <div class="mt-3 flex items-center space-x-3">
                                <span class="text-xs text-gray-500">进度</span>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="h-2.5 rounded-full gradient-button" style="width: 30%"></div>
                                </div>
                                <span class="text-xs font-semibold text-gray-500">30%</span>
                            </div>
                        </div>

                        <!-- 更多进行中计划 -->
                        <div id="more-in-progress" class="hidden space-y-4">
                            <div class="custom-card p-4 cursor-pointer hover:shadow-lg transition-shadow" onclick="window.location.href='plan-detail-3.html'">
                                <div class="flex justify-between items-center">
                                    <span class="font-bold text-gray-800">阅读名著100本</span>
                                    <button class="text-gray-400 hover:text-gray-600" onclick="event.stopPropagation(); showShareMenu()">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                    </button>
                                </div>
                                <div class="mt-3 flex items-center space-x-3">
                                    <span class="text-xs text-gray-500">进度</span>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div class="h-2.5 rounded-full gradient-button" style="width: 15%"></div>
                                    </div>
                                    <span class="text-xs font-semibold text-gray-500">15%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 展开/收起按钮 -->
                    <div class="flex justify-center my-3">
                        <button id="toggle-in-progress-btn" class="text-gray-400 hover:text-gray-600 p-1">
                            <svg id="toggle-in-progress-icon" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 已搁置的计划 -->
                <div class="px-6 mt-4">
                    <h3 class="text-lg font-bold text-gray-800">已搁置</h3>
                    <div class="mt-4">
                        <div id="toggle-shelved-btn" class="custom-card p-4 flex justify-between items-center cursor-pointer hover:shadow-lg">
                            <span class="font-semibold text-gray-800">搁置的计划 (1)</span>
                            <button class="p-1">
                                <svg id="toggle-shelved-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                </svg>
                            </button>
                        </div>

                        <div id="more-shelved" class="mt-4 hidden space-y-4">
                            <div class="custom-card p-4 cursor-pointer hover:shadow-lg opacity-70" onclick="window.location.href='plan-detail-4.html'">
                                <div class="flex justify-between items-center">
                                    <span class="font-bold text-gray-800">Side Project X</span>
                                    <button class="text-gray-400 hover:text-gray-600" onclick="event.stopPropagation(); showShareMenu()">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                    </button>
                                </div>
                                <div class="mt-3 flex items-center space-x-3">
                                    <span class="text-xs text-gray-500">进度</span>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div class="h-2.5 rounded-full bg-gray-400" style="width: 40%"></div>
                                    </div>
                                    <span class="text-xs font-semibold text-gray-500">40%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 已完成的计划 -->
                <div class="px-6 mt-4">
                    <h3 class="text-lg font-bold text-gray-800">已完成</h3>
                    <div class="mt-4">
                        <div class="custom-card p-4 flex justify-between items-center cursor-pointer" onclick="window.location.href='plan-detail-completed.html'">
                            <span class="font-semibold text-gray-800 line-through">CPA经济法通关</span>
                            <div class="flex items-center space-x-3">
                                <button class="text-gray-400 hover:text-gray-600" onclick="event.stopPropagation(); showShareMenu()">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                    </svg>
                                </button>
                                <button id="toggle-completed-btn" class="p-1">
                                    <svg id="toggle-completed-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 设置展开/收起功能
            function setupToggle(btnId, iconId, sectionId) {
                const toggleBtn = document.getElementById(btnId);
                const toggleIcon = document.getElementById(iconId);
                const section = document.getElementById(sectionId);
                
                if (!toggleBtn || !section) return;

                const use180degRotate = btnId.includes('in-progress'); 

                toggleBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    section.classList.toggle('hidden');
                    
                    if (use180degRotate) {
                        toggleIcon.classList.toggle('rotate-180');
                    } else {
                        toggleIcon.classList.toggle('rotate-90');
                    }
                });
            }

            // 初始化所有展开/收起功能
            setupToggle('toggle-in-progress-btn', 'toggle-in-progress-icon', 'more-in-progress');
            setupToggle('toggle-shelved-btn', 'toggle-shelved-icon', 'more-shelved');
            setupToggle('toggle-completed-btn', 'toggle-completed-icon', 'more-completed');

            // 分享功能
            window.showShareMenu = function() {
                alert('分享功能：\n1. 生成分享码\n2. 生成带二维码的图片');
            };
        });
    </script>
</body>
</html> 