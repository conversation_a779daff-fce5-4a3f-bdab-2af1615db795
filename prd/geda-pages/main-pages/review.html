<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据复盘 - 咯嗒</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-50">
    <div class="app-shell rounded-3xl">
        <div class="page">
        <!-- 顶部导航 -->
        <header class="sticky top-0 z-40 bg-white border-b border-gray-100">
            <div class="flex items-center justify-between p-4">
                <div class="flex items-center space-x-3">
                    <button onclick="window.history.back()" class="p-2 hover:bg-gray-100 rounded-full transition-colors">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                        </svg>
                    </button>
                    <h1 class="text-xl font-bold text-gray-800">数据复盘</h1>
                </div>
                
                <div class="flex items-center space-x-2">
                    <!-- 时间范围选择 -->
                    <select id="timeRange" class="text-sm border border-gray-200 rounded-lg px-3 py-2 bg-white">
                        <option value="week">本周</option>
                        <option value="month" selected>本月</option>
                        <option value="quarter">本季度</option>
                        <option value="year">今年</option>
                    </select>
                </div>
            </div>
        </header>

        <!-- 主要统计卡片 -->
        <section class="p-4 space-y-4">
            <!-- 总览卡片 -->
            <div class="bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl p-6 text-white">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-bold">本月学习总览</h2>
                    <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </div>
                </div>
                
                <div class="grid grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold">47.5</div>
                        <div class="text-sm text-white/80">学习小时</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold">95</div>
                        <div class="text-sm text-white/80">番茄钟</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold">23</div>
                        <div class="text-sm text-white/80">学习天数</div>
                    </div>
                </div>

                <!-- 进度条 -->
                <div class="mt-4">
                    <div class="flex justify-between text-sm mb-2">
                        <span>本月目标进度</span>
                        <span>76%</span>
                    </div>
                    <div class="w-full bg-white/20 rounded-full h-2">
                        <div class="bg-white h-2 rounded-full" style="width: 76%"></div>
                    </div>
                </div>
            </div>

            <!-- 统计指标网格 -->
            <div class="grid grid-cols-2 gap-4">
                <!-- 平均专注时长 -->
                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="text-2xl font-bold text-gray-800 mb-1">2.1小时</div>
                    <div class="text-sm text-gray-500">平均专注时长</div>
                    <div class="text-xs text-green-600 mt-1">↗ +12% vs 上月</div>
                </div>

                <!-- 完成率 -->
                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="text-2xl font-bold text-gray-800 mb-1">87%</div>
                    <div class="text-sm text-gray-500">任务完成率</div>
                    <div class="text-xs text-blue-600 mt-1">↗ +5% vs 上月</div>
                </div>

                <!-- 专注效率 -->
                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="text-2xl font-bold text-gray-800 mb-1">92%</div>
                    <div class="text-sm text-gray-500">专注效率</div>
                    <div class="text-xs text-yellow-600 mt-1">↗ +3% vs 上月</div>
                </div>

                <!-- 连续天数 -->
                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="text-2xl font-bold text-gray-800 mb-1">15天</div>
                    <div class="text-sm text-gray-500">最长连续</div>
                    <div class="text-xs text-red-600 mt-1">🔥 当前连续7天</div>
                </div>
            </div>
        </section>

        <!-- 成就系统 -->
        <section class="p-4">
            <h2 class="text-lg font-bold text-gray-800 mb-4">成就墙</h2>
            <div class="grid grid-cols-3 gap-3">
                <!-- 已获得成就 -->
                <div class="bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl p-4 text-white text-center">
                    <div class="text-2xl mb-2">🏆</div>
                    <div class="text-xs font-medium">专注大师</div>
                    <div class="text-xs opacity-80">连续学习30天</div>
                </div>

                <div class="bg-gradient-to-br from-green-400 to-blue-500 rounded-xl p-4 text-white text-center">
                    <div class="text-2xl mb-2">⚡</div>
                    <div class="text-xs font-medium">效率达人</div>
                    <div class="text-xs opacity-80">完成率90%+</div>
                </div>

                <div class="bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl p-4 text-white text-center">
                    <div class="text-2xl mb-2">🎯</div>
                    <div class="text-xs font-medium">目标猎手</div>
                    <div class="text-xs opacity-80">完成100个任务</div>
                </div>

                <!-- 待解锁成就 -->
                <div class="bg-gray-100 rounded-xl p-4 text-gray-400 text-center">
                    <div class="text-2xl mb-2">🔒</div>
                    <div class="text-xs font-medium">时间管理师</div>
                    <div class="text-xs">学习500小时</div>
                </div>

                <div class="bg-gray-100 rounded-xl p-4 text-gray-400 text-center">
                    <div class="text-2xl mb-2">🔒</div>
                    <div class="text-xs font-medium">番茄达人</div>
                    <div class="text-xs">完成1000个番茄钟</div>
                </div>

                <div class="bg-gray-100 rounded-xl p-4 text-gray-400 text-center">
                    <div class="text-2xl mb-2">🔒</div>
                    <div class="text-xs font-medium">坚持之星</div>
                    <div class="text-xs">连续学习100天</div>
                </div>
            </div>
        </section>

        <!-- 学习趋势图 -->
        <section class="p-4">
            <h2 class="text-lg font-bold text-gray-800 mb-4">学习趋势</h2>
            <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                <!-- 简化的趋势图 -->
                <div class="flex items-end justify-between h-32 mb-4">
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-purple-200 rounded-t" style="height: 40%"></div>
                        <div class="text-xs text-gray-500 mt-2">周一</div>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-purple-300 rounded-t" style="height: 60%"></div>
                        <div class="text-xs text-gray-500 mt-2">周二</div>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-purple-400 rounded-t" style="height: 80%"></div>
                        <div class="text-xs text-gray-500 mt-2">周三</div>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-purple-500 rounded-t" style="height: 95%"></div>
                        <div class="text-xs text-gray-500 mt-2">周四</div>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-purple-400 rounded-t" style="height: 70%"></div>
                        <div class="text-xs text-gray-500 mt-2">周五</div>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-purple-300 rounded-t" style="height: 45%"></div>
                        <div class="text-xs text-gray-500 mt-2">周六</div>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-purple-500 rounded-t" style="height: 85%"></div>
                        <div class="text-xs text-gray-500 mt-2">周日</div>
                    </div>
                </div>
                
                <div class="text-center">
                    <div class="text-sm text-gray-600 mb-1">本周平均每日学习时长</div>
                    <div class="text-xl font-bold text-purple-600">2.3小时</div>
                </div>
            </div>
        </section>

        <!-- 学科分布 -->
        <section class="p-4">
            <h2 class="text-lg font-bold text-gray-800 mb-4">学科时间分布</h2>
            <div class="space-y-3">
                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                            <span class="text-sm font-medium text-gray-700">数据结构与算法</span>
                        </div>
                        <span class="text-sm text-gray-500">18.5h</span>
                    </div>
                    <div class="w-full bg-gray-100 rounded-full h-2">
                        <div class="bg-blue-500 h-2 rounded-full" style="width: 38%"></div>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 rounded-full bg-green-500"></div>
                            <span class="text-sm font-medium text-gray-700">计算机网络</span>
                        </div>
                        <span class="text-sm text-gray-500">14.2h</span>
                    </div>
                    <div class="w-full bg-gray-100 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full" style="width: 30%"></div>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 rounded-full bg-yellow-500"></div>
                            <span class="text-sm font-medium text-gray-700">操作系统</span>
                        </div>
                        <span class="text-sm text-gray-500">10.8h</span>
                    </div>
                    <div class="w-full bg-gray-100 rounded-full h-2">
                        <div class="bg-yellow-500 h-2 rounded-full" style="width: 23%"></div>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 rounded-full bg-purple-500"></div>
                            <span class="text-sm font-medium text-gray-700">数据库原理</span>
                        </div>
                        <span class="text-sm text-gray-500">4.0h</span>
                    </div>
                    <div class="w-full bg-gray-100 rounded-full h-2">
                        <div class="bg-purple-500 h-2 rounded-full" style="width: 9%"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 每日学习热力图 -->
        <section class="p-4 pb-20">
            <h2 class="text-lg font-bold text-gray-800 mb-4">学习热力图</h2>
            <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="text-xs text-gray-500 mb-3">过去30天的学习活跃度</div>
                
                <!-- 热力图网格 -->
                <div class="grid grid-cols-7 gap-1">
                    <!-- 生成30天的热力图点 -->
                    <div class="w-4 h-4 rounded-sm bg-gray-100"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-200"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-300"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-400"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-500"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-300"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-200"></div>
                    
                    <div class="w-4 h-4 rounded-sm bg-purple-400"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-500"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-600"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-500"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-400"></div>
                    <div class="w-4 h-4 rounded-sm bg-gray-100"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-300"></div>
                    
                    <div class="w-4 h-4 rounded-sm bg-purple-500"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-400"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-600"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-500"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-300"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-400"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-200"></div>
                    
                    <div class="w-4 h-4 rounded-sm bg-purple-300"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-500"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-400"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-600"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-500"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-300"></div>
                    <div class="w-4 h-4 rounded-sm bg-purple-400"></div>
                    
                    <div class="w-4 h-4 rounded-sm bg-purple-200"></div>
                    <div class="w-4 h-4 rounded-sm bg-gray-100"></div>
                </div>
                
                <div class="flex items-center justify-between mt-3 text-xs text-gray-500">
                    <span>少</span>
                    <div class="flex items-center space-x-1">
                        <div class="w-3 h-3 rounded-sm bg-gray-100"></div>
                        <div class="w-3 h-3 rounded-sm bg-purple-200"></div>
                        <div class="w-3 h-3 rounded-sm bg-purple-400"></div>
                        <div class="w-3 h-3 rounded-sm bg-purple-600"></div>
                    </div>
                    <span>多</span>
                </div>
            </div>
        </section>


        </div>
    </div>

    <script>
        // 时间范围变化处理
        document.getElementById('timeRange').addEventListener('change', function(e) {
            const range = e.target.value;
            console.log('切换时间范围:', range);
            
            // 这里可以添加数据重新加载逻辑
            updateDataForRange(range);
        });

        // 模拟数据更新
        function updateDataForRange(range) {
            // 模拟不同时间范围的数据
            const data = {
                week: { hours: 16.5, pomodoros: 33, days: 7 },
                month: { hours: 47.5, pomodoros: 95, days: 23 },
                quarter: { hours: 142.8, pomodoros: 285, days: 65 },
                year: { hours: 520.5, pomodoros: 1041, days: 245 }
            };
            
            const current = data[range];
            if (current) {
                // 更新总览卡片数据
                document.querySelector('.bg-gradient-to-br .grid .text-2xl').textContent = current.hours;
                document.querySelectorAll('.bg-gradient-to-br .grid .text-2xl')[1].textContent = current.pomodoros;
                document.querySelectorAll('.bg-gradient-to-br .grid .text-2xl')[2].textContent = current.days;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据复盘页面已加载');
            
            // 可以在这里添加图表初始化、数据获取等逻辑
        });
    </script>
</body>
</html> 