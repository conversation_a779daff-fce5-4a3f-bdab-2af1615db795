<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咯嗒应用 - 任务详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-50">
    <div class="app-shell rounded-3xl">
        <div class="page">
            <!-- 状态栏 -->
            <header class="flex-shrink-0 px-6 pt-3 pb-2">
                <div class="flex justify-between items-center text-xs font-bold text-gray-800">
                    <span>12:00</span>
                    <div class="flex items-center space-x-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M5 12.55a11 11 0 0 1 14.08 0"></path>
                            <path d="M1.42 9a16 16 0 0 1 21.16 0"></path>
                            <path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path>
                            <path d="M12 20h.01"></path>
                        </svg>
                        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18.667 6.002a2.333 2.333 0 0 1 2.333 2.333v7.333a2.333 2.333 0 0 1-2.333-2.333H5.333a2.333 2.333 0 0 1-2.333-2.333V8.335a2.333 2.333 0 0 1 2.333-2.333h13.334zM22.167 8.335a1.167 1.167 0 0 0-1.167-1.167h-.833v9.667h.833a1.167 1.167 0 0 0 1.167-1.167V8.335z"></path>
                        </svg>
                    </div>
                </div>
            </header>

            <!-- 主内容区 -->
            <div class="flex-grow overflow-y-auto main-content pb-28">
                <!-- 顶部导航 -->
                <div class="px-6 pt-3 flex justify-between items-center">
                    <button class="p-2 -ml-2 text-gray-600" onclick="window.history.back()">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <div class="text-center">
                        <h1 class="text-lg font-bold text-gray-800">任务详情</h1>
                    </div>
                    <button class="p-2 text-gray-600 hover:text-gray-800">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                        </svg>
                    </button>
                </div>

                <!-- 任务信息卡片 -->
                <div class="px-6 mt-6">
                    <div class="custom-card p-6">
                        <!-- 任务标题和状态 -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-grow">
                                <h2 class="text-xl font-bold text-gray-800 mb-2">学习考研英语</h2>
                                <div class="flex items-center space-x-2">
                                    <span class="px-2 py-1 bg-orange-100 text-orange-600 text-xs font-medium rounded-full">中优先级</span>
                                    <span class="text-sm text-gray-500">来自: 考研冲刺</span>
                                </div>
                            </div>
                            <button class="p-2 text-gray-400 hover:text-gray-600">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z" />
                                </svg>
                            </button>
                        </div>

                        <!-- 进度条 -->
                        <div class="mb-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-600">完成进度</span>
                                <span class="text-sm font-semibold text-gray-800">25%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="h-3 rounded-full gradient-button" style="width: 25%;"></div>
                            </div>
                        </div>

                        <!-- 任务统计 -->
                        <div class="grid grid-cols-3 gap-4 pt-4 border-t border-gray-100">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-800">4</div>
                                <div class="text-xs text-gray-500">总番茄钟</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-indigo-600">1</div>
                                <div class="text-xs text-gray-500">已完成</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-400">3</div>
                                <div class="text-xs text-gray-500">剩余</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 时间安排 -->
                <div class="px-6 mt-6">
                    <div class="custom-card p-4">
                        <h3 class="font-bold text-gray-800 mb-3">时间安排</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">计划日期</span>
                                <span class="text-sm font-medium text-gray-800">2024年5月15日</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">时间段</span>
                                <span class="text-sm font-medium text-gray-800">09:00 - 11:00</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">重复设置</span>
                                <span class="text-sm font-medium text-gray-800">不重复</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">任务提醒</span>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="task-reminder-toggle" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 子任务 -->
                <div class="px-6 mt-6">
                    <div class="custom-card p-4">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="font-bold text-gray-800">子任务 (0/2)</h3>
                            <button id="add-subtask-btn" class="text-indigo-600 hover:text-indigo-700">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                            </button>
                        </div>
                        
                        <ul id="subtasks-list" class="space-y-3">
                            <li class="subtask-item flex items-center space-x-3">
                                <input type="checkbox" id="subtask-1" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                <label for="subtask-1" class="flex-grow text-sm text-gray-800">完成词汇练习</label>
                                <button class="text-gray-400 hover:text-red-500 opacity-0 hover:opacity-100 transition-opacity">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </li>
                            <li class="subtask-item flex items-center space-x-3">
                                <input type="checkbox" id="subtask-2" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                <label for="subtask-2" class="flex-grow text-sm text-gray-800">阅读理解练习</label>
                                <button class="text-gray-400 hover:text-red-500 opacity-0 hover:opacity-100 transition-opacity">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </li>
                        </ul>

                        <!-- 添加子任务输入框 -->
                        <div id="add-subtask-form" class="mt-4 hidden">
                            <div class="flex items-center space-x-2">
                                <input type="text" id="new-subtask-input" placeholder="输入子任务内容..." class="flex-grow p-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none">
                                <button id="confirm-add-subtask" class="px-3 py-2 bg-indigo-600 text-white text-sm rounded-lg hover:bg-indigo-700 transition-colors">添加</button>
                                <button id="cancel-add-subtask" class="p-2 text-gray-400 hover:text-gray-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 备注 -->
                <div class="px-6 mt-6">
                    <div class="custom-card p-4">
                        <h3 class="font-bold text-gray-800 mb-3">备注</h3>
                        <textarea id="task-notes" placeholder="添加任务备注..." class="w-full p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none" rows="3"></textarea>
                    </div>
                </div>
            </div>

            <!-- 底部操作栏 -->
            <footer class="absolute bottom-0 left-0 right-0 w-full max-w-[420px] mx-auto px-6 py-4 bg-white border-t border-gray-100">
                <div class="flex items-center space-x-3">
                    <button class="flex-1 flex items-center justify-center space-x-2 py-3 bg-gray-100 text-gray-700 font-medium rounded-xl hover:bg-gray-200 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        <span>删除</span>
                    </button>
                    <button class="flex-1 flex items-center justify-center space-x-2 py-3 text-white font-medium rounded-xl gradient-button hover:shadow-lg transition-all" onclick="window.location.href='focus-timer.html'">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                        <span>开始专注</span>
                    </button>
                </div>
            </footer>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 子任务相关元素
            const subtasksList = document.getElementById('subtasks-list');
            const addSubtaskBtn = document.getElementById('add-subtask-btn');
            const addSubtaskForm = document.getElementById('add-subtask-form');
            const newSubtaskInput = document.getElementById('new-subtask-input');
            const confirmAddBtn = document.getElementById('confirm-add-subtask');
            const cancelAddBtn = document.getElementById('cancel-add-subtask');
            const subtaskCounterTitle = document.querySelector('h3[class*="subtask"]');

            let subtaskCount = 2; // 当前子任务总数

            // 更新子任务计数
            function updateSubtaskCounter() {
                const total = subtasksList.children.length;
                const completed = subtasksList.querySelectorAll('.subtask-item input:checked').length;
                subtaskCounterTitle.textContent = `子任务 (${completed}/${total})`;
                
                // 更新主任务进度
                const progress = total > 0 ? (completed / total) * 100 : 0;
                document.querySelector('.gradient-button').style.width = `${progress}%`;
                document.querySelector('.flex.justify-between .text-sm.font-semibold').textContent = `${Math.round(progress)}%`;
            }

            // 添加子任务按钮点击
            addSubtaskBtn.addEventListener('click', function() {
                addSubtaskForm.classList.remove('hidden');
                newSubtaskInput.focus();
            });

            // 确认添加子任务
            confirmAddBtn.addEventListener('click', function() {
                const title = newSubtaskInput.value.trim();
                if (title) {
                    addSubtask(title);
                    newSubtaskInput.value = '';
                    addSubtaskForm.classList.add('hidden');
                }
            });

            // 取消添加子任务
            cancelAddBtn.addEventListener('click', function() {
                newSubtaskInput.value = '';
                addSubtaskForm.classList.add('hidden');
            });

            // 回车键添加子任务
            newSubtaskInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    confirmAddBtn.click();
                }
            });

            // 添加子任务函数
            function addSubtask(title) {
                subtaskCount++;
                const newId = `subtask-${subtaskCount}`;
                const li = document.createElement('li');
                li.className = 'subtask-item flex items-center space-x-3';
                li.innerHTML = `
                    <input type="checkbox" id="${newId}" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                    <label for="${newId}" class="flex-grow text-sm text-gray-800">${title}</label>
                    <button class="text-gray-400 hover:text-red-500 opacity-0 hover:opacity-100 transition-opacity delete-subtask">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </button>
                `;
                subtasksList.appendChild(li);
                updateSubtaskCounter();
            }

            // 子任务状态变化监听
            subtasksList.addEventListener('change', function(e) {
                if (e.target.type === 'checkbox') {
                    const item = e.target.closest('.subtask-item');
                    item.classList.toggle('completed', e.target.checked);
                    updateSubtaskCounter();
                }
            });

            // 删除子任务监听
            subtasksList.addEventListener('click', function(e) {
                if (e.target.closest('.delete-subtask')) {
                    const item = e.target.closest('.subtask-item');
                    item.style.transition = 'opacity 0.3s, transform 0.3s';
                    item.style.opacity = '0';
                    item.style.transform = 'translateX(-100%)';
                    setTimeout(() => {
                        item.remove();
                        updateSubtaskCounter();
                    }, 300);
                }
            });

            // 任务提醒开关
            document.getElementById('task-reminder-toggle').addEventListener('change', function() {
                if (this.checked) {
                    console.log('任务提醒已开启');
                } else {
                    console.log('任务提醒已关闭');
                }
            });

            // 备注自动保存
            let saveTimeout;
            document.getElementById('task-notes').addEventListener('input', function() {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    console.log('备注已自动保存:', this.value);
                }, 1000);
            });

            // 初始化计数器
            updateSubtaskCounter();
        });
    </script>
</body>
</html> 