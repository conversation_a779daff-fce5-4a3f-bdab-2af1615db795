<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咯嗒应用 - 日历</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-50">
    <div class="app-shell rounded-3xl">
        <div class="page">
            <!-- 状态栏 -->
            <header class="flex-shrink-0 px-6 pt-3 pb-2">
                <div class="flex justify-between items-center text-xs font-bold text-gray-800">
                    <span>12:00</span>
                    <div class="flex items-center space-x-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M5 12.55a11 11 0 0 1 14.08 0"></path>
                            <path d="M1.42 9a16 16 0 0 1 21.16 0"></path>
                            <path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path>
                            <path d="M12 20h.01"></path>
                        </svg>
                        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18.667 6.002a2.333 2.333 0 0 1 2.333 2.333v7.333a2.333 2.333 0 0 1-2.333-2.333H5.333a2.333 2.333 0 0 1-2.333-2.333V8.335a2.333 2.333 0 0 1 2.333-2.333h13.334zM22.167 8.335a1.167 1.167 0 0 0-1.167-1.167h-.833v9.667h.833a1.167 1.167 0 0 0 1.167-1.167V8.335z"></path>
                        </svg>
                    </div>
                </div>
            </header>

            <!-- 主内容区 -->
            <div class="flex-grow overflow-y-auto main-content pb-6">
                <!-- 顶部导航 -->
                <div class="px-6 pt-3 flex justify-between items-center">
                    <button class="p-2 -ml-2 text-gray-600" onclick="window.history.back()">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <div class="text-center">
                        <h1 class="text-lg font-bold text-gray-800">日历</h1>
                    </div>
                    <div class="w-10 h-10"></div>
                </div>

                <!-- 月份导航 -->
                <div class="px-6 mt-6">
                    <div class="flex justify-between items-center mb-4">
                        <button id="prev-month" class="p-2 text-gray-600 hover:text-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                        <h2 id="calendar-month-year" class="text-xl font-bold text-gray-800">2024年5月</h2>
                        <button id="next-month" class="p-2 text-gray-600 hover:text-gray-800">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>

                    <!-- 日历主体 -->
                    <div class="custom-card p-4">
                        <!-- 星期标题 -->
                        <div class="grid grid-cols-7 mb-2">
                            <div class="text-center text-sm font-medium text-gray-500 p-2">日</div>
                            <div class="text-center text-sm font-medium text-gray-500 p-2">一</div>
                            <div class="text-center text-sm font-medium text-gray-500 p-2">二</div>
                            <div class="text-center text-sm font-medium text-gray-500 p-2">三</div>
                            <div class="text-center text-sm font-medium text-gray-500 p-2">四</div>
                            <div class="text-center text-sm font-medium text-gray-500 p-2">五</div>
                            <div class="text-center text-sm font-medium text-gray-500 p-2">六</div>
                        </div>

                        <!-- 日历格子 -->
                        <div id="calendar-grid" class="grid grid-cols-7 gap-1">
                            <!-- 日历格子将通过JavaScript生成 -->
                        </div>
                    </div>
                </div>

                <!-- 选中日期信息 -->
                <div class="px-6 mt-6">
                    <div id="selected-date-info" class="custom-card p-4 hidden">
                        <h3 id="selected-date-title" class="font-bold text-gray-800 mb-3">5月15日 (今天)</h3>
                        
                        <!-- 今日任务 -->
                        <div class="mb-4">
                            <h4 class="text-sm font-semibold text-gray-600 mb-2">任务</h4>
                            <div id="date-tasks" class="space-y-2">
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                    <span class="text-sm text-gray-700">学习考研英语</span>
                                    <span class="text-xs text-gray-500">09:00-11:00</span>
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                    <span class="text-sm text-gray-700">复习长难句结构分析</span>
                                    <span class="text-xs text-gray-500">14:00-15:00</span>
                                </div>
                            </div>
                        </div>

                        <!-- 今日提醒 -->
                        <div>
                            <h4 class="text-sm font-semibold text-gray-600 mb-2">提醒</h4>
                            <div id="date-reminders" class="space-y-2">
                                <div class="flex items-center justify-between p-2 bg-yellow-50 rounded-lg">
                                    <span class="text-sm text-gray-700">CPA经济法报名</span>
                                    <span class="text-xs text-yellow-600">20:00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作按钮 -->
                <div class="px-6 mt-4">
                    <div class="grid grid-cols-2 gap-4">
                        <button class="flex items-center justify-center space-x-2 bg-blue-50 text-blue-600 font-semibold py-3 rounded-xl hover:bg-blue-100 transition-all duration-200" onclick="window.location.href='../modals/new-task-modal.html'">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            <span>新建任务</span>
                        </button>
                        <button class="flex items-center justify-center space-x-2 bg-yellow-50 text-yellow-600 font-semibold py-3 rounded-xl hover:bg-yellow-100 transition-all duration-200" onclick="window.location.href='../modals/new-reminder-modal.html'">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-5 5v-5zM4 19h1v-3a3 3 0 011-1h1m0 0V9a5 5 0 0110 0v6h1a3 3 0 011 1v3h1" />
                            </svg>
                            <span>新建提醒</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let currentDate = new Date();
            let selectedDate = new Date();
            
            const monthNames = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"];
            
            // DOM元素
            const calendarGrid = document.getElementById('calendar-grid');
            const monthYearElement = document.getElementById('calendar-month-year');
            const prevMonthBtn = document.getElementById('prev-month');
            const nextMonthBtn = document.getElementById('next-month');
            const selectedDateInfo = document.getElementById('selected-date-info');
            const selectedDateTitle = document.getElementById('selected-date-title');

            // 示例数据
            const taskData = {
                '2024-05-15': [
                    { title: '学习考研英语', time: '09:00-11:00' },
                    { title: '复习长难句结构分析', time: '14:00-15:00' },
                    { title: '背诵新概念英语第四册', time: '15:30-16:30' }
                ],
                '2024-05-16': [
                    { title: '数学练习', time: '10:00-12:00' }
                ]
            };

            const reminderData = {
                '2024-05-15': [
                    { title: 'CPA经济法报名', time: '20:00' }
                ],
                '2024-05-17': [
                    { title: '团队会议', time: '14:00' }
                ]
            };

            // 获取月份第一天和最后一天
            function getMonthInfo(date) {
                const year = date.getFullYear();
                const month = date.getMonth();
                const firstDay = new Date(year, month, 1);
                const lastDay = new Date(year, month + 1, 0);
                const firstDayOfWeek = firstDay.getDay();
                const daysInMonth = lastDay.getDate();
                
                return { year, month, firstDayOfWeek, daysInMonth };
            }

            // 检查日期是否有任务或提醒
            function hasEvents(dateStr) {
                return taskData[dateStr] || reminderData[dateStr];
            }

            // 格式化日期为字符串
            function formatDate(date) {
                const year = date.getFullYear();
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

            // 渲染日历
            function renderCalendar() {
                const { year, month, firstDayOfWeek, daysInMonth } = getMonthInfo(currentDate);
                
                // 更新月份年份显示
                monthYearElement.textContent = `${year}年${monthNames[month]}`;
                
                // 清空日历格子
                calendarGrid.innerHTML = '';
                
                // 添加空白格子（月份开始前的空白）
                for (let i = 0; i < firstDayOfWeek; i++) {
                    const emptyCell = document.createElement('div');
                    emptyCell.className = 'h-10';
                    calendarGrid.appendChild(emptyCell);
                }
                
                // 添加日期格子
                for (let day = 1; day <= daysInMonth; day++) {
                    const dayCell = document.createElement('div');
                    const cellDate = new Date(year, month, day);
                    const dateStr = formatDate(cellDate);
                    const isToday = dateStr === formatDate(new Date());
                    const isSelected = dateStr === formatDate(selectedDate);
                    const hasEvent = hasEvents(dateStr);
                    
                    dayCell.className = `h-10 flex items-center justify-center text-sm relative cursor-pointer rounded-lg transition-colors ${
                        isToday ? 'bg-indigo-600 text-white font-bold' : 
                        isSelected ? 'bg-indigo-100 text-indigo-600 font-semibold' :
                        'text-gray-700 hover:bg-gray-100'
                    }`;
                    
                    dayCell.textContent = day;
                    
                    // 添加事件指示器
                    if (hasEvent) {
                        const indicator = document.createElement('div');
                        indicator.className = 'absolute bottom-1 w-1 h-1 bg-orange-400 rounded-full';
                        dayCell.appendChild(indicator);
                    }
                    
                    // 点击事件
                    dayCell.addEventListener('click', () => {
                        selectedDate = new Date(cellDate);
                        renderCalendar();
                        showDateInfo(dateStr);
                    });
                    
                    calendarGrid.appendChild(dayCell);
                }
            }

            // 显示选中日期的信息
            function showDateInfo(dateStr) {
                const date = new Date(dateStr);
                const today = new Date();
                const isToday = dateStr === formatDate(today);
                const isTomorrow = dateStr === formatDate(new Date(today.getTime() + 24 * 60 * 60 * 1000));
                
                let titleText = `${date.getMonth() + 1}月${date.getDate()}日`;
                if (isToday) titleText += ' (今天)';
                else if (isTomorrow) titleText += ' (明天)';
                
                selectedDateTitle.textContent = titleText;
                
                // 显示任务
                const tasks = taskData[dateStr] || [];
                const tasksContainer = document.getElementById('date-tasks');
                tasksContainer.innerHTML = '';
                
                if (tasks.length > 0) {
                    tasks.forEach(task => {
                        const taskElement = document.createElement('div');
                        taskElement.className = 'flex items-center justify-between p-2 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100';
                        taskElement.innerHTML = `
                            <span class="text-sm text-gray-700">${task.title}</span>
                            <span class="text-xs text-gray-500">${task.time}</span>
                        `;
                        tasksContainer.appendChild(taskElement);
                    });
                } else {
                    tasksContainer.innerHTML = '<div class="text-sm text-gray-400 text-center py-2">当日无任务</div>';
                }
                
                // 显示提醒
                const reminders = reminderData[dateStr] || [];
                const remindersContainer = document.getElementById('date-reminders');
                remindersContainer.innerHTML = '';
                
                if (reminders.length > 0) {
                    reminders.forEach(reminder => {
                        const reminderElement = document.createElement('div');
                        reminderElement.className = 'flex items-center justify-between p-2 bg-yellow-50 rounded-lg cursor-pointer hover:bg-yellow-100';
                        reminderElement.innerHTML = `
                            <span class="text-sm text-gray-700">${reminder.title}</span>
                            <span class="text-xs text-yellow-600">${reminder.time}</span>
                        `;
                        remindersContainer.appendChild(reminderElement);
                    });
                } else {
                    remindersContainer.innerHTML = '<div class="text-sm text-gray-400 text-center py-2">当日无提醒</div>';
                }
                
                selectedDateInfo.classList.remove('hidden');
            }

            // 月份导航
            prevMonthBtn.addEventListener('click', () => {
                currentDate.setMonth(currentDate.getMonth() - 1);
                renderCalendar();
            });

            nextMonthBtn.addEventListener('click', () => {
                currentDate.setMonth(currentDate.getMonth() + 1);
                renderCalendar();
            });

            // 初始化
            selectedDate = new Date(); // 默认选中今天
            renderCalendar();
            showDateInfo(formatDate(selectedDate));
        });
    </script>
</body>
</html> 