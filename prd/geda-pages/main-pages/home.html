<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咯嗒应用 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-50">
    <div class="app-shell rounded-3xl">
        <div class="page">
            <!-- 状态栏 -->
            <header class="flex-shrink-0 px-6 pt-3 pb-2">
                <div class="flex justify-between items-center text-xs font-bold text-gray-800">
                    <span>12:00</span>
                    <div class="flex items-center space-x-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M5 12.55a11 11 0 0 1 14.08 0"></path>
                            <path d="M1.42 9a16 16 0 0 1 21.16 0"></path>
                            <path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path>
                            <path d="M12 20h.01"></path>
                        </svg>
                        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18.667 6.002a2.333 2.333 0 0 1 2.333 2.333v7.333a2.333 2.333 0 0 1-2.333-2.333H5.333a2.333 2.333 0 0 1-2.333-2.333V8.335a2.333 2.333 0 0 1 2.333-2.333h13.334zM22.167 8.335a1.167 1.167 0 0 0-1.167-1.167h-.833v9.667h.833a1.167 1.167 0 0 0 1.167-1.167V8.335z"></path>
                        </svg>
                    </div>
                </div>
            </header>

            <!-- 主内容区 -->
            <div class="flex-grow overflow-y-auto main-content pb-28">
                <!-- 顶部导航 -->
                <div class="px-6 pt-3 flex justify-between items-center">
                    <div class="bg-gray-100 p-1 rounded-full flex items-center">
                        <button class="px-5 py-2 text-base font-bold bg-white text-indigo-600 rounded-full shadow-md transition-all duration-300">日程</button>
                        <button class="px-5 py-2 text-base font-bold text-gray-500 hover:text-gray-800 transition-all duration-300" onclick="window.location.href='plan.html'">计划</button>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="review.html" class="text-gray-500 hover:text-indigo-400" title="复盘">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                                <path d="M12 11h4"/>
                                <path d="M12 16h4"/>
                                <path d="M8 11h.01"/>
                                <path d="M8 16h.01"/>
                                <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
                            </svg>
                        </a>
                        <a href="scan.html" class="text-gray-500 hover:text-indigo-400">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M3 7V5a2 2 0 0 1 2-2h2"/>
                                <path d="M17 3h2a2 2 0 0 1 2 2v2"/>
                                <path d="M21 17v2a2 2 0 0 1-2 2h-2"/>
                                <path d="M7 21H5a2 2 0 0 1-2-2v-2"/>
                                <path d="M7 12h10"/>
                            </svg>
                        </a>
                        <a href="profile.html">
                            <div class="w-9 h-9 bg-gray-200 rounded-full flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-500">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- 日历卡片 -->
                <div class="px-6 mt-6">
                    <div class="custom-card p-4">
                        <div class="flex justify-between items-center">
                            <span id="home-calendar-month" class="font-bold text-lg text-gray-800">5月</span>
                            <span class="text-xs text-gray-400">开始新的一天吧</span>
                        </div>
                        <div class="mt-4 grid grid-cols-7 text-center text-sm">
                            <div class="py-2 text-gray-400">日</div>
                            <div class="py-2 text-gray-400">一</div>
                            <div class="py-2 text-gray-400">二</div>
                            <div class="py-2 font-bold text-indigo-600 bg-indigo-50 rounded-full">三<br><span class="text-xs">15</span></div>
                            <div class="py-2 text-gray-400">四</div>
                            <div class="py-2 text-gray-400">五</div>
                            <div class="py-2 text-gray-400">六</div>
                        </div>
                    </div>
                </div>

                <!-- 展开日历按钮 -->
                <div class="flex justify-center mt-2">
                    <a href="calendar.html">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400">
                            <polyline points="6 9 12 15 18 9"></polyline>
                        </svg>
                    </a>
                </div>

                <!-- 今日提醒 -->
                <div class="px-6 mt-6">
                    <h2 class="font-bold text-gray-800 text-lg mb-4">今日提醒</h2>
                    <div class="custom-card p-4 flex items-center space-x-3">
                        <div class="w-10 h-10 bg-yellow-100 rounded-full flex-shrink-0 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-yellow-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path>
                                <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path>
                            </svg>
                        </div>
                        <p class="text-gray-700 font-medium text-sm flex-grow">晚上八点, CPA经济法报名?</p>
                        <div class="relative">
                            <button id="reminder-options-btn" class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 今日任务 -->
                <div class="mt-8 px-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="font-bold text-gray-800 text-lg">今日任务</h2>
                        <div class="flex items-center space-x-2">
                            <button id="sort-tasks-btn" class="flex items-center space-x-1 text-xs text-gray-500 font-medium p-1 rounded-md hover:bg-gray-100">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span>按开始时间排序</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <button id="view-toggle-btn" class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- 任务容器 -->
                    <div id="tasks-container" class="tasks-container">
                        <!-- 任务卡片3 -->
                        <div class="task-card task-card-3 custom-card flex items-center cursor-pointer">
                            <div class="flex-grow">
                                <p class="font-bold text-gray-800 text-lg">背诵新概念英语第四册</p>
                                <p class="text-sm text-gray-400 mt-1 flex items-center">
                                    <span>15:30 - 16:30</span>
                                    <span class="mx-2 font-bold">&middot;</span>
                                    <span>🍅 2 番茄</span>
                                </p>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-4">
                                    <div class="h-2 rounded-full gradient-button" style="width: 50%;"></div>
                                </div>
                            </div>
                            <div class="ml-4 flex-shrink-0">
                                <button class="w-14 h-14 bg-indigo-50 text-indigo-500 rounded-full flex items-center justify-center shadow-sm hover:bg-indigo-100 transition-colors">
                                    <svg class="w-8 h-8 ml-1" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8 5v14l11-7z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- 任务卡片2 -->
                        <div class="task-card task-card-2 custom-card flex items-center cursor-pointer">
                            <div class="flex-grow">
                                <p class="font-bold text-gray-800 text-lg">复习长难句结构分析</p>
                                <p class="text-sm text-gray-400 mt-1 flex items-center">
                                    <span>14:00 - 15:00</span>
                                    <span class="mx-2 font-bold">&middot;</span>
                                    <span>🍅 2 番茄</span>
                                </p>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-4">
                                    <div class="h-2 rounded-full gradient-button" style="width: 75%;"></div>
                                </div>
                            </div>
                            <div class="ml-4 flex-shrink-0">
                                <button class="w-14 h-14 bg-indigo-50 text-indigo-500 rounded-full flex items-center justify-center shadow-sm hover:bg-indigo-100 transition-colors">
                                    <svg class="w-8 h-8 ml-1" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8 5v14l11-7z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- 任务卡片1 -->
                        <div class="task-card task-card-1 custom-card flex items-center cursor-pointer">
                            <div class="flex-grow">
                                <p class="font-bold text-gray-800 text-lg">学习考研英语</p>
                                <p class="text-sm text-gray-400 mt-1 flex items-center">
                                    <span>09:00 - 11:00</span>
                                    <span class="mx-2 font-bold">&middot;</span>
                                    <span>🍅 4 番茄</span>
                                </p>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-4">
                                    <div class="h-2 rounded-full gradient-button" style="width: 25%;"></div>
                                </div>
                            </div>
                            <div class="ml-4 flex-shrink-0">
                                <button class="w-14 h-14 bg-indigo-50 text-indigo-500 rounded-full flex items-center justify-center shadow-sm hover:bg-indigo-100 transition-colors">
                                    <svg class="w-8 h-8 ml-1" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8 5v14l11-7z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 折叠按钮 -->
                    <div id="collapse-tasks-wrapper" class="flex justify-center -mt-1 opacity-0 pointer-events-none transition-opacity duration-300">
                        <button id="collapse-btn" class="text-gray-400 hover:text-indigo-500 p-1 rounded-full hover:bg-gray-100 transition-all">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M5 15l7-7 7 7" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 底部操作栏 -->
            <footer class="absolute bottom-4 left-0 right-0 w-full max-w-[420px] mx-auto px-6 z-40">
                <div class="bg-white/70 backdrop-blur-lg rounded-full flex items-center justify-between p-2 shadow-2xl shadow-gray-400/20">
                    <button id="ai-voice-btn" class="w-12 h-12 rounded-full flex items-center justify-center text-white gradient-button transform active:scale-90">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z"></path>
                        </svg>
                    </button>
                    <span class="text-sm text-gray-500 font-medium px-2 text-center">可以对我讲你的待办任务</span>
                    <div class="relative">
                        <button id="add-btn" class="w-12 h-12 rounded-full flex items-center justify-center text-white shadow-md gradient-button transform active:scale-90">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                        </button>
                        <div id="add-menu" class="popup-menu absolute right-0 bottom-16 w-40 bg-white rounded-lg shadow-xl p-2 z-50">
                            <a href="focus-timer.html" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-3 text-indigo-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                                    <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>
                                </svg>
                                <span class="font-semibold">立即专注</span>
                            </a>
                            <div class="border-t my-1 -mx-2"></div>
                            <a href="../modals/new-reminder-modal.html" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">新建提醒</a>
                            <a href="../modals/new-task-modal.html" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">新建任务</a>
                            <a href="../modals/new-plan-modal.html" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">新建计划</a>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <script>
        // 页面交互逻辑
        document.addEventListener('DOMContentLoaded', function() {
            const tasksContainer = document.getElementById('tasks-container');
            const addBtn = document.getElementById('add-btn');
            const addMenu = document.getElementById('add-menu');
            const collapseWrapper = document.getElementById('collapse-tasks-wrapper');
            const collapseBtn = document.getElementById('collapse-btn');

            // 任务容器点击展开
            tasksContainer.addEventListener('click', function(e) {
                if (e.target.closest('.task-card')) {
                    tasksContainer.classList.toggle('tasks-expanded');
                    const isExpanded = tasksContainer.classList.contains('tasks-expanded');
                    collapseWrapper.style.opacity = isExpanded ? '1' : '0';
                    collapseWrapper.style.pointerEvents = isExpanded ? 'auto' : 'none';
                }
            });

            // 折叠按钮
            collapseBtn?.addEventListener('click', function(e) {
                e.stopPropagation();
                tasksContainer.classList.remove('tasks-expanded');
                collapseWrapper.style.opacity = '0';
                collapseWrapper.style.pointerEvents = 'none';
            });

            // 添加按钮菜单
            addBtn?.addEventListener('click', function(e) {
                e.stopPropagation();
                addMenu.classList.toggle('active');
            });

            // AI语音按钮
            document.getElementById('ai-voice-btn')?.addEventListener('click', function() {
                alert('AI语音功能演示：\\n"明天上午9点提醒我开会"\\n"添加一个学习任务，时间2小时"');
            });

            // 点击外部关闭菜单
            document.addEventListener('click', function() {
                addMenu.classList.remove('active');
            });

            // 初始化当前日期
            const today = new Date();
            const month = today.getMonth() + 1;
            document.getElementById('home-calendar-month').textContent = month + '月';
        });
    </script>
</body>
</html> 