<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咯嗒应用 - 番茄钟专注</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-50">
    <div class="app-shell rounded-3xl">
        <div class="page bg-white">
            <!-- 状态栏 -->
            <header class="flex-shrink-0 px-4 pt-3 pb-2">
                <div class="flex justify-between items-center text-xs font-bold text-gray-800">
                    <span>12:00</span>
                    <div class="flex items-center space-x-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M5 12.55a11 11 0 0 1 14.08 0"></path>
                            <path d="M1.42 9a16 16 0 0 1 21.16 0"></path>
                            <path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path>
                            <path d="M12 20h.01"></path>
                        </svg>
                        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18.667 6.002a2.333 2.333 0 0 1 2.333 2.333v7.333a2.333 2.333 0 0 1-2.333-2.333H5.333a2.333 2.333 0 0 1-2.333-2.333V8.335a2.333 2.333 0 0 1 2.333-2.333h13.334zM22.167 8.335a1.167 1.167 0 0 0-1.167-1.167h-.833v9.667h.833a1.167 1.167 0 0 0 1.167-1.167V8.335z"></path>
                        </svg>
                    </div>
                </div>
            </header>

            <!-- 主内容区 -->
            <div class="flex-grow overflow-y-auto main-content">
                <!-- 顶部导航 -->
                <div class="px-5 pt-3 flex justify-between items-center">
                    <button class="p-2 -ml-2 text-gray-600" onclick="window.history.back()">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <div class="text-center">
                        <h1 class="text-lg font-bold text-gray-800">专注中</h1>
                    </div>
                    <div class="w-10 h-10"></div>
                </div>

                <!-- 番茄钟圆环 -->
                <div class="px-5 mt-8">
                    <div class="custom-card p-6">
                        <div class="relative w-48 h-48 mx-auto">
                            <svg class="w-full h-full" viewBox="0 0 200 200">
                                <circle cx="100" cy="100" r="90" fill="none" stroke="#eef2ff" stroke-width="12"></circle>
                                <circle id="timer-progress" cx="100" cy="100" r="90" fill="none" stroke="url(#gradient)" stroke-width="12" stroke-linecap="round" class="timer-circle-progress" transform="rotate(-90 100 100)"></circle>
                                <defs>
                                    <linearGradient id="gradient">
                                        <stop offset="0%" stop-color="var(--accent-start)" />
                                        <stop offset="100%" stop-color="var(--accent-end)" />
                                    </linearGradient>
                                </defs>
                            </svg>
                            <div class="absolute inset-0 flex flex-col items-center justify-center">
                                <span id="time-display" class="text-5xl font-bold text-gray-800 tracking-wider">25:00</span>
                                <span id="status-display" class="text-sm text-gray-400 mt-2">工作进行中</span>
                            </div>
                        </div>

                        <!-- 控制按钮 -->
                        <div class="flex justify-center items-center space-x-8 mt-8">
                            <button id="reset-btn" class="w-14 h-14 rounded-full flex items-center justify-center bg-gray-200/70 text-gray-600 shadow-sm hover:bg-gray-300/80 transition-all duration-150">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                                </svg>
                            </button>
                            <button id="play-pause-btn" class="w-20 h-20 rounded-full flex items-center justify-center text-white shadow-lg shadow-indigo-500/30 active:scale-95 transition-all duration-150 gradient-button">
                                <svg id="play-icon" xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 ml-1 hidden" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M4 3a1 1 0 00-1 1v12a1 1 0 001.555.832l10-6a1 1 0 000-1.664l-10-6A1 1 0 004 3z" />
                                </svg>
                                <svg id="pause-icon" xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M5 4h3v12H5V4zm7 0h3v12h-3V4z" />
                                </svg>
                            </button>
                            <button id="skip-btn" class="w-14 h-14 rounded-full flex items-center justify-center bg-gray-200/70 text-gray-600 shadow-sm hover:bg-gray-300/80 transition-all duration-150">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M5 4.5l10 7.5-10 7.5V4.5zM17 4.5v15h2V4.5h-2z" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 番茄设置 -->
                <div class="px-5 mt-6">
                    <h3 class="text-sm font-bold text-gray-500">番茄设置</h3>
                    <div class="custom-card p-4 space-y-3">
                        <div class="grid grid-cols-2 items-center">
                            <span class="text-gray-700 font-medium">工作时间</span>
                            <div class="flex items-center space-x-2 justify-self-end">
                                <button class="setting-btn w-8 h-8 rounded-md flex items-center justify-center bg-gray-200/80 text-gray-700 font-bold text-lg hover:bg-gray-300" data-target="work" data-amount="-1">-</button>
                                <span id="work-time-display" class="font-bold text-gray-800 w-10 text-center">25</span>
                                <button class="setting-btn w-8 h-8 rounded-md flex items-center justify-center bg-gray-200/80 text-gray-700 font-bold text-lg hover:bg-gray-300" data-target="work" data-amount="1">+</button>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 items-center">
                            <span class="text-gray-700 font-medium">休息时间</span>
                            <div class="flex items-center space-x-2 justify-self-end">
                                <button class="setting-btn w-8 h-8 rounded-md flex items-center justify-center bg-gray-200/80 text-gray-700 font-bold text-lg hover:bg-gray-300" data-target="rest" data-amount="-1">-</button>
                                <span id="rest-time-display" class="font-bold text-gray-800 w-10 text-center">5</span>
                                <button class="setting-btn w-8 h-8 rounded-md flex items-center justify-center bg-gray-200/80 text-gray-700 font-bold text-lg hover:bg-gray-300" data-target="rest" data-amount="1">+</button>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 items-center pt-3 border-t border-gray-200/70">
                            <span class="text-gray-700 font-medium">时钟归属</span>
                            <div class="justify-self-end">
                                <p id="clock-owner-text" class="font-semibold text-indigo-400">学习考研英语</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 番茄钟变量
            let workTime = 25;
            let restTime = 5;
            let currentTime = workTime * 60; // 秒
            let isRunning = false;
            let isWorkSession = true;
            let timerInterval = null;
            const circumference = 2 * Math.PI * 90; // 圆的周长

            // DOM 元素
            const timeDisplay = document.getElementById('time-display');
            const statusDisplay = document.getElementById('status-display');
            const workTimeDisplay = document.getElementById('work-time-display');
            const restTimeDisplay = document.getElementById('rest-time-display');
            const playPauseBtn = document.getElementById('play-pause-btn');
            const resetBtn = document.getElementById('reset-btn');
            const skipBtn = document.getElementById('skip-btn');
            const playIcon = document.getElementById('play-icon');
            const pauseIcon = document.getElementById('pause-icon');
            const timerProgress = document.getElementById('timer-progress');

            // 设置进度圆环
            timerProgress.style.strokeDasharray = circumference;
            timerProgress.style.strokeDashoffset = circumference;

            // 更新显示
            function updateDisplay() {
                const minutes = Math.floor(currentTime / 60);
                const seconds = currentTime % 60;
                timeDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                // 更新进度圆环
                const totalTime = isWorkSession ? workTime * 60 : restTime * 60;
                const progress = (totalTime - currentTime) / totalTime;
                const offset = circumference - (progress * circumference);
                timerProgress.style.strokeDashoffset = offset;
            }

            // 开始/暂停
            function toggleTimer() {
                if (isRunning) {
                    // 暂停
                    clearInterval(timerInterval);
                    isRunning = false;
                    playIcon.classList.remove('hidden');
                    pauseIcon.classList.add('hidden');
                } else {
                    // 开始
                    isRunning = true;
                    playIcon.classList.add('hidden');
                    pauseIcon.classList.remove('hidden');
                    
                    timerInterval = setInterval(() => {
                        if (currentTime > 0) {
                            currentTime--;
                            updateDisplay();
                        } else {
                            // 时间到
                            clearInterval(timerInterval);
                            isRunning = false;
                            playIcon.classList.remove('hidden');
                            pauseIcon.classList.add('hidden');
                            
                            // 切换会话
                            isWorkSession = !isWorkSession;
                            statusDisplay.textContent = isWorkSession ? "工作进行中" : "休息一下";
                            resetTimer();
                            
                            // 播放提示音或显示通知
                            alert(isWorkSession ? '休息结束，开始工作！' : '工作结束，休息一下！');
                        }
                    }, 1000);
                }
            }

            // 重置计时器
            function resetTimer() {
                clearInterval(timerInterval);
                isRunning = false;
                currentTime = isWorkSession ? workTime * 60 : restTime * 60;
                playIcon.classList.remove('hidden');
                pauseIcon.classList.add('hidden');
                updateDisplay();
            }

            // 跳过当前会话
            function skipSession() {
                clearInterval(timerInterval);
                isRunning = false;
                isWorkSession = !isWorkSession;
                statusDisplay.textContent = isWorkSession ? "工作进行中" : "休息一下";
                resetTimer();
            }

            // 更新时间设置
            function updateTimeSetting(target, amount) {
                if (isRunning) return; // 运行时不允许修改
                
                if (target === 'work') {
                    workTime = Math.max(1, workTime + amount);
                    workTimeDisplay.textContent = workTime;
                } else if (target === 'rest') {
                    restTime = Math.max(1, restTime + amount);
                    restTimeDisplay.textContent = restTime;
                }
                
                // 如果修改的是当前会话类型，重置时间
                if ((isWorkSession && target === 'work') || (!isWorkSession && target === 'rest')) {
                    resetTimer();
                }
            }

            // 事件监听器
            playPauseBtn.addEventListener('click', toggleTimer);
            resetBtn.addEventListener('click', resetTimer);
            skipBtn.addEventListener('click', skipSession);

            // 设置按钮事件
            document.querySelectorAll('.setting-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const target = btn.dataset.target;
                    const amount = parseInt(btn.dataset.amount);
                    updateTimeSetting(target, amount);
                });
            });

            // 初始化显示
            updateDisplay();
        });
    </script>
</body>
</html> 