<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扫描二维码 - 咯嗒</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../assets/common.css">
    <style>
        @keyframes scan {
            0% { transform: translateY(-100%); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateY(100%); opacity: 0; }
        }
        .animate-scan {
            animation: scan 2s infinite ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <div class="app-shell rounded-3xl">
        <div class="min-h-screen bg-black text-white flex flex-col p-4">
            <!-- Header -->
            <header class="flex-shrink-0 w-full">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold">扫描二维码</h2>
                    <button onclick="history.back()" class="text-3xl font-light text-gray-300 hover:text-white">&times;</button>
                </div>
            </header>
            
            <!-- Scan Area -->
            <div class="flex-grow flex items-center justify-center">
                <div class="w-64 h-64 relative">
                    <!-- Corner frames -->
                    <div class="absolute top-0 left-0 w-8 h-8 border-t-4 border-l-4 border-indigo-400 rounded-tl-lg"></div>
                    <div class="absolute top-0 right-0 w-8 h-8 border-t-4 border-r-4 border-indigo-400 rounded-tr-lg"></div>
                    <div class="absolute bottom-0 left-0 w-8 h-8 border-b-4 border-l-4 border-indigo-400 rounded-bl-lg"></div>
                    <div class="absolute bottom-0 right-0 w-8 h-8 border-b-4 border-r-4 border-indigo-400 rounded-br-lg"></div>
                    
                    <!-- Scanning line -->
                    <div class="absolute top-1/2 left-4 right-4 h-px bg-indigo-400/50 animate-scan"></div>
                </div>
            </div>
            
            <!-- Instructions -->
            <p class="text-center text-gray-400 pb-8">将二维码/条形码放入框内，即可自动扫描</p>
        </div>
    </div>

    <script>
        // JavaScript for handling scan functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add scan simulation or actual scanning logic here
            console.log('扫描页面已加载');
        });
    </script>
</body>
</html> 