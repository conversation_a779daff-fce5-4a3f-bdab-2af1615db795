/* 咯嗒应用 - 公共样式 */

/* CSS变量定义 */
:root {
    --bg-shell: #F7F8FC; 
    --bg-page: #FFFFFF;
    --text-primary: #1F2937;
    --text-secondary: #6B7280;
    --accent-start: #818cf8;
    --accent-end: #a78bfa;
    --card-shadow: 0 10px 35px -5px rgba(180, 190, 220, 0.2);
    --priority-high: #ef4444; /* red-500 */
    --priority-medium: #f97316; /* orange-500 */
    --priority-low: #22c55e; /* green-500 */
}

/* 基础样式 */
body { 
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    background-color: #FFFFFF;
    margin: 0;
    padding: 0;
}

/* 应用容器 */
.app-shell {
    width: 100%; 
    height: 100vh;
    max-width: 420px; 
    max-height: 880px;
    box-shadow: 0 20px 50px -10px rgba(0, 0, 0, 0.15);
    overflow: hidden; 
    position: relative;
    background-color: var(--bg-shell);
    margin: 0 auto;
}

/* 页面基础样式 */
.page {
    position: absolute; 
    top: 0; 
    left: 0; 
    width: 100%; 
    height: 100%;
    background-color: var(--bg-shell);
    display: flex; 
    flex-direction: column;
    z-index: 10;
    transition: transform 0.4s ease;
}

.page.hidden-right { 
    transform: translateX(100%); 
    z-index: 9; 
}

.page.hidden-left { 
    transform: translateX(-30%); 
    z-index: 9; 
}

/* 滚动条隐藏 */
.main-content::-webkit-scrollbar { 
    display: none; 
}

.main-content.noscroll { 
    overflow-y: hidden; 
}

/* 卡片样式 */
.custom-card {
    background-color: var(--bg-page);
    border-radius: 1.25rem;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
}

/* 任务容器样式 */
.tasks-container { 
    position: relative; 
    height: 128px; 
    transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1); 
}

.task-card { 
    position: absolute; 
    width: 100%; 
    height: 128px; 
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); 
    cursor: pointer; 
    padding: 20px; 
}

.task-card-1 { z-index: 30; }
.task-card-2 { 
    transform: scale(0.95) translateX(16px); 
    z-index: 20; 
    background-color: rgba(255, 255, 255, 0.8); 
}
.task-card-3 { 
    transform: scale(0.9) translateX(32px); 
    z-index: 10; 
    background-color: rgba(255, 255, 255, 0.6); 
}

.tasks-container.tasks-expanded { 
    height: 416px; 
}

.tasks-container.tasks-expanded .task-card { 
    transform: scale(1) translateX(0); 
    cursor: default; 
    background-color: white; 
}

.tasks-container.tasks-expanded .task-card-2 { 
    top: 144px; 
}

.tasks-container.tasks-expanded .task-card-3 { 
    top: 288px; 
}

.tasks-container.tasks-expanded + #collapse-tasks-wrapper {
    opacity: 1;
    pointer-events: auto;
}

/* 模态框样式 */
.modal-overlay {
    position: absolute; 
    top: 0; 
    left: 0; 
    right: 0; 
    bottom: 0;
    background-color: rgba(30, 41, 59, 0.5);
    backdrop-filter: blur(2px);
    display: flex; 
    align-items: center; 
    justify-content: center;
    opacity: 0; 
    pointer-events: none;
    transition: opacity 0.3s ease-in-out;
    z-index: 100;
}

.modal-overlay.active { 
    opacity: 1; 
    pointer-events: auto; 
}

.modal-content {
    background-color: white; 
    padding: 24px; 
    border-radius: 16px; 
    width: 90%; 
    max-width: 380px;
    box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1), 0 10px 10px -5px rgba(0,0,0,0.04);
    transform: scale(0.95); 
    transition: transform 0.3s ease-in-out;
}

.modal-overlay.active .modal-content { 
    transform: scale(1); 
}

/* 按钮样式 */
.priority-btn.active, 
.snooze-btn.active, 
.repeat-btn.active { 
    background-image: linear-gradient(to right, var(--accent-start), var(--accent-end)); 
    color: white; 
    border-color: transparent; 
}

.gradient-button { 
    background-image: linear-gradient(to right, var(--accent-start), var(--accent-end)); 
    transition: all 0.3s ease; 
}

.gradient-button:hover { 
    box-shadow: 0 10px 20px -5px rgba(129, 140, 248, 0.5); 
}

.gradient-text { 
    background-image: linear-gradient(to right, var(--accent-start), var(--accent-end)); 
    -webkit-background-clip: text; 
    color: transparent; 
}

/* 图表样式 */
.chart-bar { 
    transition: all 0.3s ease; 
}

.chart-bar:hover { 
    transform: translateY(-4px); 
    opacity: 0.8; 
}

/* 动画效果 */
@keyframes scan-line { 
    0% { top: 0; } 
    100% { top: 95%; } 
}

.animate-scan { 
    animation: scan-line 2.5s ease-in-out infinite alternate; 
}

/* 文本样式 */
.text-strikethrough {
    text-decoration: line-through;
    color: var(--text-secondary);
}

/* 计划相关样式 */
.plan-hidden-section {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease-in-out;
}

.plan-visible-section { 
    max-height: 500px; 
}

/* 分享面板样式 */
#share-action-sheet-overlay {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease-in-out;
}

#share-action-sheet-overlay.active {
    opacity: 1;
    pointer-events: auto;
}

#share-action-sheet-content {
    transform: translateY(100%);
    transition: transform 0.3s ease-out;
}

#share-action-sheet-overlay.active #share-action-sheet-content {
    transform: translateY(0);
}

/* 任务删除按钮 */
.task-delete-button {
    transition: all 0.2s ease-in-out;
}

.task-delete-button:hover {
    color: var(--priority-high);
    transform: scale(1.1);
}

/* 计划详情任务卡片 */
.plan-details-task-card {
    border-left-width: 4px;
    transition: all 0.3s ease;
}

/* 选择模式样式 */
.selection-mode-active .selection-checkbox {
    display: flex;
}

.selection-mode-active .task-delete-button {
    display: none;
}

/* 开关切换样式 */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-image: linear-gradient(to right, var(--accent-start), var(--accent-end));
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* 列表视图样式 */
.tasks-container.list-view {
    height: auto !important;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.tasks-container.list-view .task-card {
    position: static !important;
    transform: none !important;
    width: 100% !important;
    height: auto !important;
    background-color: white !important;
    opacity: 1 !important;
    z-index: 1 !important;
    padding: 16px;
}

.tasks-container.list-view .task-card p.text-lg { 
    font-size: 1rem; 
}

.tasks-container.list-view .task-card p.text-sm { 
    font-size: 0.75rem; 
}

.tasks-container.list-view + #collapse-tasks-wrapper { 
    display: none; 
}

/* 子任务样式 */
.subtask-item { 
    display: flex; 
    align-items: center; 
    padding: 8px 0; 
    border-bottom: 1px solid #f3f4f6; 
}

.subtask-item:last-child { 
    border-bottom: none; 
}

.subtask-item input[type="checkbox"] { 
    width: 1.15rem; 
    height: 1.15rem; 
    margin-right: 12px; 
}

.subtask-item.completed label { 
    text-decoration: line-through; 
    color: var(--text-secondary); 
}

/* 引导层样式 */
#onboarding-overlay {
    position: absolute; 
    inset: 0; 
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 200; 
    display: flex; 
    flex-direction: column; 
    justify-content: flex-end;
    opacity: 0; 
    pointer-events: none; 
    transition: opacity 0.3s ease;
}

#onboarding-overlay.active { 
    opacity: 1; 
    pointer-events: auto; 
}

@keyframes arrow-bounce { 
    0%, 100% { transform: translateY(0); } 
    50% { transform: translateY(-10px); } 
}

.onboarding-arrow { 
    animation: arrow-bounce 1.5s infinite; 
}

/* 空状态样式 */
.empty-state {
    display: none;
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.empty-state.visible { 
    display: block; 
}

/* 弹出菜单样式 */
.popup-menu {
    position: absolute;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    padding: 0.5rem;
    z-index: 50;
    transform: scale(0.95);
    opacity: 0;
    pointer-events: none;
    transition: all 0.15s ease-out;
}

.popup-menu.active {
    transform: scale(1);
    opacity: 1;
    pointer-events: auto;
}

/* 定时器圆环进度 */
.timer-circle-progress {
    stroke-dasharray: 565.48;
    stroke-dashoffset: 565.48;
    transition: stroke-dashoffset 1s linear;
}

/* 响应式设计 */
@media (max-width: 420px) {
    .app-shell {
        border-radius: 0;
        max-width: 100%;
        max-height: 100vh;
    }
} 