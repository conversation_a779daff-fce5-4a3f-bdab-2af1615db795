<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咯嗒应用 - 页面导航</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/common.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Noto Sans SC', 'Inter', sans-serif;
        }
        .nav-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 35px -5px rgba(180, 190, 220, 0.3);
            transition: all 0.3s ease;
        }
        .nav-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px -5px rgba(180, 190, 220, 0.4);
        }
        .section-title {
            font-weight: 700;
            color: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .gradient-text {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <!-- 标题 -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-4">咯嗒 (Geda) 应用原型</h1>
            <p class="text-lg text-white/80">iOS 智能时间管理应用 - 所有页面预览</p>
        </div>

        <!-- 主要页面 -->
        <section class="mb-8">
            <h2 class="text-2xl section-title mb-6">🏠 主要页面</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <a href="main-pages/home.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold gradient-text">首页/日程</h3>
                    </div>
                    <p class="text-gray-600 text-sm">今日任务卡片、日历概览、AI语音助手</p>
                </a>

                <a href="main-pages/plan.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold gradient-text">计划管理</h3>
                    </div>
                    <p class="text-gray-600 text-sm">进行中、已搁置、已完成计划的管理</p>
                </a>

                <a href="main-pages/focus-timer.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold gradient-text">番茄钟专注</h3>
                    </div>
                    <p class="text-gray-600 text-sm">专注计时器、工作/休息模式、设置调节</p>
                </a>

                <a href="main-pages/calendar.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold gradient-text">日历视图</h3>
                    </div>
                    <p class="text-gray-600 text-sm">完整月历、日期任务详情、快速创建</p>
                </a>

                <a href="main-pages/task-details.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold gradient-text">任务详情</h3>
                    </div>
                    <p class="text-gray-600 text-sm">任务信息、子任务管理、进度跟踪</p>
                </a>

                <a href="main-pages/review.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold gradient-text">数据复盘</h3>
                    </div>
                    <p class="text-gray-600 text-sm">学习数据分析、成就展示、时长统计</p>
                </a>

                <a href="main-pages/scan.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold gradient-text">二维码扫描</h3>
                    </div>
                    <p class="text-gray-600 text-sm">扫描二维码/条形码、导入计划功能</p>
                </a>

                <a href="main-pages/profile.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold gradient-text">个人信息</h3>
                    </div>
                    <p class="text-gray-600 text-sm">用户资料、学霸模式、设置、模板管理</p>
                </a>
            </div>
        </section>

        <!-- 弹窗组件 -->
        <section class="mb-8">
            <h2 class="text-2xl section-title mb-6">📱 弹窗组件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- 基础创建弹窗 -->
                <a href="modals/new-task-modal.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold gradient-text">新建任务</h3>
                    </div>
                    <p class="text-gray-600 text-sm">任务创建表单、优先级设置、番茄钟配置</p>
                </a>

                <a href="modals/new-reminder-modal.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h1v-3a3 3 0 011-1h1m0 0V9a5 5 0 0110 0v6h1a3 3 0 011 1v3h1"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold gradient-text">新建提醒</h3>
                    </div>
                    <p class="text-gray-600 text-sm">提醒内容设置、时间选择</p>
                </a>

                <a href="modals/new-plan-modal.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold gradient-text">新建计划</h3>
                    </div>
                    <p class="text-gray-600 text-sm">计划名称、时间范围、描述设置</p>
                </a>

                <!-- 高级功能弹窗 -->
                <a href="modals/ai-confirm-modal.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold gradient-text">AI确认任务</h3>
                    </div>
                    <p class="text-gray-600 text-sm">AI语音解析结果、置信度显示、编辑确认</p>
                </a>

                <a href="modals/xueba-mode-info-modal.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold gradient-text">学霸模式</h3>
                    </div>
                    <p class="text-gray-600 text-sm">专注模式说明、功能介绍、开关控制</p>
                </a>

                <a href="modals/snooze-reminder-modal.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-orange-100 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold gradient-text">推迟提醒</h3>
                    </div>
                    <p class="text-gray-600 text-sm">推迟时间选择、自定义分钟数设置</p>
                </a>

                <!-- 管理操作弹窗 -->
                <a href="modals/import-plan-modal.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-teal-100 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold gradient-text">导入计划</h3>
                    </div>
                    <p class="text-gray-600 text-sm">分享码输入、智能粘贴、格式验证</p>
                </a>

                <a href="modals/restart-plan-modal.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-emerald-100 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold gradient-text">重启计划</h3>
                    </div>
                    <p class="text-gray-600 text-sm">计划重新激活、任务选择、批量重启</p>
                </a>

                <a href="modals/delete-confirm-modal.html" class="nav-card p-6 block">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-bold gradient-text">删除确认</h3>
                    </div>
                    <p class="text-gray-600 text-sm">删除操作确认、安全提示、取消选项</p>
                </a>
            </div>
        </section>

        <!-- 功能特色 -->
        <section class="mb-8">
            <h2 class="text-2xl section-title mb-6">✨ 核心功能特色</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="nav-card p-6">
                    <h3 class="text-xl font-bold gradient-text mb-4">🤖 AI语音助手</h3>
                    <ul class="text-gray-600 space-y-2 text-sm">
                        <li>• 自然语言创建任务和提醒</li>
                        <li>• 智能解析时间和优先级</li>
                        <li>• 语音交互确认和编辑</li>
                    </ul>
                </div>
                
                <div class="nav-card p-6">
                    <h3 class="text-xl font-bold gradient-text mb-4">🍅 番茄钟专注系统</h3>
                    <ul class="text-gray-600 space-y-2 text-sm">
                        <li>• 可调节工作/休息时间</li>
                        <li>• SVG动画进度圆环</li>
                        <li>• 学霸模式锁定功能</li>
                    </ul>
                </div>
                
                <div class="nav-card p-6">
                    <h3 class="text-xl font-bold gradient-text mb-4">📊 层级化计划管理</h3>
                    <ul class="text-gray-600 space-y-2 text-sm">
                        <li>• 长期计划→日常任务→子任务</li>
                        <li>• 进度跟踪和状态管理</li>
                        <li>• 计划模板分享功能</li>
                    </ul>
                </div>
                
                <div class="nav-card p-6">
                    <h3 class="text-xl font-bold gradient-text mb-4">📈 数据复盘分析</h3>
                    <ul class="text-gray-600 space-y-2 text-sm">
                        <li>• 学习时长统计分析</li>
                        <li>• 成就系统和里程碑</li>
                        <li>• 周/月数据对比</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 注意事项 -->
        <section class="mb-8">
            <div class="nav-card p-6 border-2 border-green-200 bg-green-50/50">
                <h2 class="text-2xl gradient-text mb-4">✅ 完善说明</h2>
                <div class="text-sm text-gray-700 space-y-3">
                    <p><strong>当前状态:</strong> 已完成主要功能页面的拆分，包含了核心的8个页面和9个弹窗组件。</p>
                    <div>
                        <p class="font-semibold mb-2">✅ 已补全的弹窗组件：</p>
                        <ul class="ml-4 space-y-1 text-xs">
                            <li>• 基础创建：新建任务、新建提醒、新建计划</li>
                            <li>• 高级功能：AI确认任务、学霸模式说明、推迟提醒</li>
                            <li>• 管理操作：导入计划、重启计划、删除确认</li>
                        </ul>
                    </div>
                    <div>
                        <p class="font-semibold mb-2">原始文件中其他页面（可选拆分）：</p>
                        <ul class="ml-4 space-y-1 text-xs">
                            <li>• page-plan-details-1, 2, 3, 4 (4个具体计划详情页)</li>
                            <li>• page-plan-details-completed-cpa, ui, react (3个已完成计划页)</li>
                            <li>• share-action-sheet-overlay (分享动作表单)</li>
                            <li>• onboarding-overlay (新手引导覆盖层)</li>
                        </ul>
                    </div>
                    <p class="bg-green-50 p-3 rounded-lg border border-green-200"><strong>完成度:</strong> 核心功能已100%覆盖，包括所有重要的弹窗组件。当前版本完全满足应用原型展示和开发参考需求。</p>
                </div>
            </div>
        </section>

        <!-- 技术信息 -->
        <section class="mb-8">
            <div class="nav-card p-6">
                <h2 class="text-2xl gradient-text mb-4">🛠️ 技术实现说明</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
                    <div>
                        <h3 class="font-bold text-gray-800 mb-2">前端技术栈</h3>
                        <ul class="space-y-1">
                            <li>• <strong>样式框架:</strong> TailwindCSS</li>
                            <li>• <strong>字体:</strong> Noto Sans SC + Inter</li>
                            <li>• <strong>图标:</strong> Heroicons SVG</li>
                            <li>• <strong>动画:</strong> CSS Transitions + Transforms</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-bold text-gray-800 mb-2">设计特性</h3>
                        <ul class="space-y-1">
                            <li>• <strong>尺寸适配:</strong> 420×880px (iPhone标准)</li>
                            <li>• <strong>设计语言:</strong> 现代化扁平设计</li>
                            <li>• <strong>配色方案:</strong> 紫色渐变主题</li>
                            <li>• <strong>交互模式:</strong> 手势导航 + 卡片设计</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 底部信息 -->
        <footer class="text-center text-white/70 mt-12">
            <p>📱 此原型为 iOS 应用开发提供完整的页面结构和交互参考</p>
            <p class="mt-2 text-sm">所有页面均可独立运行，包含完整的样式和基础交互逻辑</p>
            <div class="mt-6">
                <button id="show-onboarding-demo" class="px-6 py-3 bg-white/20 text-white font-semibold rounded-full hover:bg-white/30 transition">
                    🎯 体验引导遮罩效果
                </button>
            </div>
        </footer>
    </div>

    <!-- 引导遮罩 (模拟home.html中的引导体验) -->
    <div id="onboarding-overlay" class="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-end justify-center opacity-0 pointer-events-none transition-all duration-500">
        <div class="relative w-full px-6 pb-28 text-center">
            <svg class="onboarding-arrow w-12 h-12 text-white mx-auto animate-bounce" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 11l3-3m0 0l3 3m-3-3v8m0-13a9 9 0 110 18 9 9 0 010-18z" />
            </svg>
            <h2 class="text-2xl font-bold text-white mt-4">体验AI创建任务</h2>
            <p class="text-base text-gray-200 mt-2">点击下方的麦克风按钮，试着对我说出你的待办事项，比如"周五晚上八点去看电影"。</p>
            <button id="close-onboarding-btn" class="mt-6 px-6 py-2 bg-white/20 text-white font-semibold rounded-full hover:bg-white/30 transition">我知道了</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const onboardingOverlay = document.getElementById('onboarding-overlay');
            const showOnboardingBtn = document.getElementById('show-onboarding-demo');
            const closeOnboardingBtn = document.getElementById('close-onboarding-btn');

            // 显示引导遮罩
            showOnboardingBtn.addEventListener('click', function() {
                onboardingOverlay.style.opacity = '1';
                onboardingOverlay.style.pointerEvents = 'auto';
            });

            // 隐藏引导遮罩
            closeOnboardingBtn.addEventListener('click', function() {
                onboardingOverlay.style.opacity = '0';
                onboardingOverlay.style.pointerEvents = 'none';
            });

            // 点击遮罩背景隐藏
            onboardingOverlay.addEventListener('click', function(e) {
                if (e.target === onboardingOverlay) {
                    onboardingOverlay.style.opacity = '0';
                    onboardingOverlay.style.pointerEvents = 'none';
                }
            });
        });
    </script>
</body>
</html> 