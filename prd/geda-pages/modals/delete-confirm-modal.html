<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>删除确认弹窗</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-900/50">
    <div class="w-full max-w-md mx-4">
        <!-- 删除确认弹窗 -->
        <div id="delete-confirm-modal" class="modal-content bg-white rounded-xl shadow-2xl">
        <h2 class="text-xl font-bold text-gray-800 text-center">确认删除</h2>
        
        <p id="delete-confirm-message" class="text-center text-gray-500 my-4 text-sm">
            您确定要永久删除这个已完成的计划吗？<br>此操作无法撤销。
        </p>
        
        <div class="flex justify-center space-x-4 mt-6">
            <button id="cancel-delete-btn" class="flex-1 py-3 border border-gray-300 rounded-lg font-semibold text-gray-700 hover:bg-gray-100 transition-colors">
                取消
            </button>
            <button id="confirm-delete-btn" class="flex-1 py-3 bg-red-500 text-white rounded-lg font-semibold hover:bg-red-600 transition-colors">
                确认删除
            </button>
        </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 取消删除按钮
            const cancelBtn = document.getElementById('cancel-delete-btn');
            if (cancelBtn) {
                cancelBtn.addEventListener('click', function() {
                    window.history.back();
                });
            }

            // 确认删除按钮
            const confirmBtn = document.getElementById('confirm-delete-btn');
            if (confirmBtn) {
                confirmBtn.addEventListener('click', function() {
                    // 这里应该触发实际的删除逻辑
                    console.log('执行删除操作');
                    alert('删除成功！');
                    
                    // 关闭弹窗
                    window.history.back();
                });
            }
        });
    </script>
</body>
</html> 