<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新建计划 - 咯嗒</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-900/50">
    <div class="w-full max-w-md mx-4">
        <div class="modal-content bg-white rounded-xl shadow-2xl">
                <!-- 标题栏 -->
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">新建计划</h2>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>

                <form id="planForm" onsubmit="handleSubmit(event)">
                    <!-- 计划名称 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">计划名称</label>
                        <input 
                            type="text" 
                            name="title" 
                            placeholder="请输入计划名称"
                            class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                            required
                        >
                    </div>

                    <!-- 计划类型 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">计划类型</label>
                        <div class="grid grid-cols-2 gap-3">
                            <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input type="radio" name="type" value="study" checked class="mr-3 text-purple-600">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-medium">学习计划</span>
                                </div>
                            </label>

                            <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input type="radio" name="type" value="work" class="mr-3 text-purple-600">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-medium">工作计划</span>
                                </div>
                            </label>

                            <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input type="radio" name="type" value="life" class="mr-3 text-purple-600">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-medium">生活计划</span>
                                </div>
                            </label>

                            <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input type="radio" name="type" value="fitness" class="mr-3 text-purple-600">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-medium">健身计划</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- 时间范围 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">计划时间</label>
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-xs text-gray-500 mb-1">开始日期</label>
                                <input 
                                    type="date" 
                                    name="startDate"
                                    id="planStartDate"
                                    class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                    required
                                >
                            </div>
                            <div>
                                <label class="block text-xs text-gray-500 mb-1">结束日期</label>
                                <input 
                                    type="date" 
                                    name="endDate"
                                    id="planEndDate"
                                    class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                    required
                                >
                            </div>
                        </div>
                    </div>

                    <!-- 计划描述 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">计划描述</label>
                        <textarea 
                            name="description" 
                            placeholder="简要描述你的计划内容和目标..."
                            rows="3"
                            class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
                        ></textarea>
                    </div>

                    <!-- 目标设置 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">目标设置</label>
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-xs text-gray-500 mb-1">每日目标时长(分钟)</label>
                                <input 
                                    type="number" 
                                    name="dailyTarget"
                                    min="15"
                                    max="480"
                                    step="15"
                                    value="90"
                                    class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                >
                            </div>
                            <div>
                                <label class="block text-xs text-gray-500 mb-1">每日番茄钟数</label>
                                <input 
                                    type="number" 
                                    name="dailyPomodoros"
                                    min="1"
                                    max="20"
                                    value="4"
                                    class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                >
                            </div>
                        </div>
                    </div>

                    <!-- 提醒设置 -->
                    <div class="mb-8">
                        <label class="block text-sm font-medium text-gray-700 mb-3">提醒设置</label>
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="notifications[]" value="daily" checked class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                                <span class="ml-3 text-sm text-gray-700">每日进度提醒</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="notifications[]" value="milestone" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                                <span class="ml-3 text-sm text-gray-700">里程碑达成提醒</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="notifications[]" value="encouragement" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                                <span class="ml-3 text-sm text-gray-700">鼓励提醒</span>
                            </label>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex space-x-3">
                        <button 
                            type="button"
                            onclick="closeModal()"
                            class="flex-1 py-3 px-4 border border-gray-200 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors"
                        >
                            取消
                        </button>
                        <button 
                            type="submit"
                            class="flex-1 py-3 px-4 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors"
                        >
                            创建计划
                        </button>
                    </div>
                </form>
        </div>
    </div>

    <script>
        // 初始化默认日期
        function initializeDates() {
            const today = new Date();
            const startDate = today.toISOString().split('T')[0];
            
            // 设置默认结束日期为30天后
            const endDate = new Date(today);
            endDate.setDate(today.getDate() + 30);
            const endDateStr = endDate.toISOString().split('T')[0];
            
            document.getElementById('planStartDate').value = startDate;
            document.getElementById('planEndDate').value = endDateStr;
        }

        // 关闭弹窗
        function closeModal() {
            window.history.back();
        }

        // 处理表单提交
        function handleSubmit(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            
            // 获取所有选中的通知设置
            const notifications = [];
            const notificationCheckboxes = document.querySelectorAll('input[name="notifications[]"]:checked');
            notificationCheckboxes.forEach(checkbox => {
                notifications.push(checkbox.value);
            });
            
            const planData = {
                title: formData.get('title'),
                type: formData.get('type'),
                startDate: formData.get('startDate'),
                endDate: formData.get('endDate'),
                description: formData.get('description'),
                dailyTarget: parseInt(formData.get('dailyTarget')),
                dailyPomodoros: parseInt(formData.get('dailyPomodoros')),
                notifications: notifications
            };
            
            console.log('创建计划:', planData);
            
            // 验证日期
            if (new Date(planData.endDate) <= new Date(planData.startDate)) {
                alert('结束日期必须晚于开始日期');
                return;
            }
            
            // 显示成功反馈
            showSuccess();
        }

        // 显示成功反馈
        function showSuccess() {
            const button = document.querySelector('button[type="submit"]');
            const originalText = button.textContent;
            
            button.textContent = '✓ 创建成功';
            button.style.backgroundColor = '#10b981';
            
            setTimeout(() => {
                closeModal();
            }, 1000);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeDates();
            
            // 监听开始日期变化，自动调整结束日期
            document.getElementById('planStartDate').addEventListener('change', function(e) {
                const startDate = new Date(e.target.value);
                const endDateInput = document.getElementById('planEndDate');
                const currentEndDate = new Date(endDateInput.value);
                
                // 如果结束日期早于或等于开始日期，自动设置为开始日期后30天
                if (currentEndDate <= startDate) {
                    const newEndDate = new Date(startDate);
                    newEndDate.setDate(startDate.getDate() + 30);
                    endDateInput.value = newEndDate.toISOString().split('T')[0];
                }
            });
        });

        // 点击背景关闭弹窗 - 新结构下不需要此功能
    </script>
</body>
</html> 