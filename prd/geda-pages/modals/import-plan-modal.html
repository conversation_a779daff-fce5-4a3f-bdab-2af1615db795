<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入计划弹窗</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-900/50">
    <div class="w-full max-w-md mx-4">
        <!-- 导入计划弹窗 -->
        <div id="import-plan-modal" class="modal-content bg-white rounded-xl shadow-2xl">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold">导入计划</h2>
            <button class="close-modal text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
        </div>
        
        <form action="#">
            <div class="space-y-4">
                <div>
                    <label class="text-sm font-semibold text-gray-600">计划分享码</label>
                    <div class="relative flex items-center mt-1">
                        <input 
                            type="text" 
                            id="share-code-input" 
                            placeholder="请在此处粘贴分享码" 
                            class="w-full p-3 pr-20 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none"
                        >
                        <button 
                            type="button" 
                            id="paste-code-btn" 
                            class="absolute right-2 bg-indigo-100 text-indigo-600 text-sm font-semibold px-3 py-1 rounded-md hover:bg-indigo-200 transition-colors"
                        >
                            粘贴
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">
                        分享码格式示例：GEDA_PLAN_ABC123DEF456
                    </p>
                </div>
            </div>
            
            <div class="mt-8">
                <button type="submit" class="w-full text-white font-bold py-3 rounded-lg gradient-button">
                    确认导入
                </button>
            </div>
        </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 粘贴按钮功能
            const pasteBtn = document.getElementById('paste-code-btn');
            const shareCodeInput = document.getElementById('share-code-input');
            
            if (pasteBtn && shareCodeInput) {
                pasteBtn.addEventListener('click', async function() {
                    try {
                        // 尝试从剪贴板读取内容
                        const text = await navigator.clipboard.readText();
                        shareCodeInput.value = text;
                        
                        // 给用户反馈
                        pasteBtn.textContent = '已粘贴';
                        pasteBtn.classList.add('bg-green-100', 'text-green-600');
                        pasteBtn.classList.remove('bg-indigo-100', 'text-indigo-600');
                        
                        setTimeout(() => {
                            pasteBtn.textContent = '粘贴';
                            pasteBtn.classList.remove('bg-green-100', 'text-green-600');
                            pasteBtn.classList.add('bg-indigo-100', 'text-indigo-600');
                        }, 2000);
                    } catch (err) {
                        console.log('粘贴失败:', err);
                        // 在不支持剪贴板API的情况下的备用方案
                        shareCodeInput.focus();
                    }
                });
            }

            // 关闭弹窗逻辑
            const closeModal = document.querySelector('.close-modal');
            if (closeModal) {
                closeModal.addEventListener('click', function() {
                    window.history.back();
                });
            }

            // 表单提交逻辑
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const shareCode = shareCodeInput ? shareCodeInput.value.trim() : '';
                    
                    if (!shareCode) {
                        alert('请输入分享码');
                        return;
                    }
                    
                    // 验证分享码格式
                    if (!shareCode.startsWith('GEDA_PLAN_')) {
                        alert('分享码格式不正确');
                        return;
                    }
                    
                    console.log('导入计划:', shareCode);
                    alert('计划导入成功！');
                    
                    // 关闭弹窗
                    window.history.back();
                });
            }
        });
    </script>
</body>
</html> 