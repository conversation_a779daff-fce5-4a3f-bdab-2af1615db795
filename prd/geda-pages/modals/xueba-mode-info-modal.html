<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学霸模式说明弹窗</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-900/50">
    <div class="w-full max-w-md mx-4">
        <!-- 学霸模式说明弹窗 -->
        <div id="xueba-mode-info-modal" class="modal-content bg-white rounded-xl shadow-2xl">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">学霸模式</h2>
            <button class="close-modal text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
        </div>
        
        <div class="space-y-4">
            <!-- 模式说明 -->
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-100">
                <h3 class="font-semibold text-gray-800 mb-2">🧠 什么是学霸模式？</h3>
                <p class="text-sm text-gray-600">
                    专注期间将为您屏蔽所有手机通知，并锁定App，必须完成该次专注后方可解锁。
                </p>
            </div>

            <!-- 功能特性 -->
            <div class="space-y-3">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-800">屏蔽所有通知</p>
                        <p class="text-xs text-gray-500">专注期间不会收到任何App通知干扰</p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-800">应用锁定保护</p>
                        <p class="text-xs text-gray-500">必须完成番茄钟才能解锁应用</p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-800">强制专注模式</p>
                        <p class="text-xs text-gray-500">无法中途退出，提升专注效果</p>
                    </div>
                </div>
            </div>

            <!-- 开关控制 -->
            <div class="bg-white border border-gray-200 rounded-lg p-4 flex justify-between items-center">
                <span class="font-semibold" style="color: var(--text-primary);">开启学霸模式</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="xueba-mode-actual-toggle">
                    <span class="toggle-slider"></span>
                </label>
            </div>

            <!-- 警告提示 -->
            <div class="bg-amber-50 border border-amber-200 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <p class="text-sm font-medium text-amber-800">温馨提示</p>
                </div>
                <p class="text-xs text-amber-700 mt-1">
                    启用学霸模式后，在专注期间将无法访问其他应用，请确保没有紧急事务需要处理。
                </p>
            </div>
        </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 学霸模式开关切换
            const xuebaModeToggle = document.getElementById('xueba-mode-actual-toggle');
            
            if (xuebaModeToggle) {
                xuebaModeToggle.addEventListener('change', function() {
                    const isEnabled = this.checked;
                    
                    if (isEnabled) {
                        console.log('学霸模式已开启');
                        // 这里可以添加开启学霸模式的逻辑
                        
                        // 显示确认提示
                        setTimeout(() => {
                            alert('学霸模式已开启！下次专注时将自动启用。');
                        }, 100);
                    } else {
                        console.log('学霸模式已关闭');
                        // 这里可以添加关闭学霸模式的逻辑
                    }
                    
                    // 保存设置到本地存储
                    localStorage.setItem('xuebaModeEnabled', isEnabled);
                });
                
                // 加载之前的设置
                const savedSetting = localStorage.getItem('xuebaModeEnabled');
                if (savedSetting === 'true') {
                    xuebaModeToggle.checked = true;
                }
            }

            // 关闭弹窗逻辑
            const closeModal = document.querySelector('.close-modal');
            if (closeModal) {
                closeModal.addEventListener('click', function() {
                    window.history.back();
                });
            }
        });
    </script>
</body>
</html> 