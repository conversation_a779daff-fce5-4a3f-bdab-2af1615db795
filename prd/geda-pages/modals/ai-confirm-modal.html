<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI创建任务确认弹窗</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-900/50">
    <div class="w-full max-w-md mx-4">
        <!-- AI确认创建任务弹窗 -->
        <div id="ai-confirm-modal" class="modal-content bg-white rounded-xl shadow-2xl">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">AI为你创建了任务</h2>
            <button class="close-modal text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
        </div>
        
        <p class="text-sm text-gray-500 mb-4">
            请确认为您解析出的信息是否准确：
        </p>
        
        <!-- AI解析结果展示区域 -->
        <div class="space-y-3 bg-slate-50 p-4 rounded-lg border border-slate-200">
            <!-- 任务标题 -->
            <div id="ai-confirm-title" class="font-bold text-gray-800 text-lg">
                明天下午3点和设计团队开会
            </div>
            
            <!-- 时间信息 -->
            <div class="text-sm text-gray-600">
                <span class="font-semibold">时间:</span>
                <span id="ai-confirm-time">2025年6月24日 15:00</span>
            </div>
            
            <!-- 所属计划 -->
            <div class="text-sm text-gray-600">
                <span class="font-semibold">所属计划:</span>
                <span id="ai-confirm-plan">无计划</span>
            </div>
            
            <!-- 优先级 -->
            <div class="text-sm text-gray-600">
                <span class="font-semibold">优先级:</span>
                <span id="ai-confirm-priority" class="text-orange-600">中</span>
            </div>
            
            <!-- 番茄钟数量 -->
            <div class="text-sm text-gray-600">
                <span class="font-semibold">番茄钟:</span>
                <span id="ai-confirm-tomatoes">🍅 2个</span>
            </div>
        </div>
        
        <!-- AI置信度指示器 -->
        <div class="mt-3 flex items-center justify-between text-xs">
            <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                <span class="text-gray-500">AI解析置信度: 92%</span>
            </div>
            <div class="flex items-center space-x-1 text-gray-400">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                <span>AI智能解析</span>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex justify-center space-x-4 mt-6">
            <button id="ai-edit-btn" class="flex-1 py-3 border border-gray-300 rounded-lg font-semibold text-gray-700 hover:bg-gray-100 transition-colors">
                编辑
            </button>
            <button id="ai-confirm-btn" class="flex-1 py-3 text-white rounded-lg font-semibold gradient-button">
                确认创建
            </button>
        </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // AI解析数据（示例数据，实际应用中应该从语音识别结果获取）
            const aiParsedData = {
                title: "明天下午3点和设计团队开会",
                time: "2025年6月24日 15:00",
                plan: "无计划",
                priority: "中",
                tomatoes: 2,
                confidence: 92
            };
            
            // 更新AI解析结果显示
            function updateAIResult(data) {
                document.getElementById('ai-confirm-title').textContent = data.title;
                document.getElementById('ai-confirm-time').textContent = data.time;
                document.getElementById('ai-confirm-plan').textContent = data.plan;
                document.getElementById('ai-confirm-priority').textContent = data.priority;
                document.getElementById('ai-confirm-tomatoes').textContent = `🍅 ${data.tomatoes}个`;
                
                // 根据优先级设置颜色
                const priorityElement = document.getElementById('ai-confirm-priority');
                if (data.priority === '高') {
                    priorityElement.className = 'text-red-600';
                } else if (data.priority === '中') {
                    priorityElement.className = 'text-orange-600';
                } else {
                    priorityElement.className = 'text-green-600';
                }
            }

            // 编辑按钮点击处理
            const editBtn = document.getElementById('ai-edit-btn');
            if (editBtn) {
                editBtn.addEventListener('click', function() {
                    console.log('用户选择编辑任务');
                    
                    // 这里应该打开新建任务弹窗并预填充AI解析的数据
                    alert('跳转到任务编辑界面');
                    
                    // 关闭AI确认弹窗
                    window.history.back();
                });
            }

            // 确认创建按钮点击处理
            const confirmBtn = document.getElementById('ai-confirm-btn');
            if (confirmBtn) {
                confirmBtn.addEventListener('click', function() {
                    console.log('用户确认创建任务:', aiParsedData);
                    
                    // 创建任务的逻辑
                    alert('任务创建成功！');
                    
                    // 关闭弹窗
                    window.history.back();
                });
            }

            // 关闭弹窗逻辑
            const closeModal = document.querySelector('.close-modal');
            if (closeModal) {
                closeModal.addEventListener('click', function() {
                    window.history.back();
                });
            }

            // 初始化AI解析结果显示
            updateAIResult(aiParsedData);

            // 暴露更新函数供外部调用
            window.updateAIConfirmModal = function(data) {
                updateAIResult(data);
                // 显示弹窗
                const modal = document.getElementById('ai-confirm-modal');
                if (modal) {
                    modal.classList.remove('hidden');
                }
            };
        });
    </script>
</body>
</html> 