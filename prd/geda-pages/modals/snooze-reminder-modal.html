<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推迟提醒弹窗</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-900/50">
    <div class="w-full max-w-md mx-4">
        <!-- 推迟提醒弹窗 -->
        <div id="snooze-reminder-modal" class="modal-content bg-white rounded-xl shadow-2xl">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">推迟提醒</h2>
            <button class="close-modal text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
        </div>
        
        <div class="space-y-4">
            <p class="text-sm" style="color: var(--text-secondary);">选择推迟时间：</p>
            
            <!-- 预设时间选择 -->
            <div class="flex justify-between space-x-2">
                <button class="snooze-btn flex-1 py-2 border border-gray-300 rounded-lg text-sm font-semibold">10 分钟</button>
                <button class="snooze-btn flex-1 py-2 border border-gray-300 rounded-lg text-sm font-semibold active">30 分钟</button>
                <button class="snooze-btn flex-1 py-2 border border-gray-300 rounded-lg text-sm font-semibold">1 小时</button>
            </div>
            
            <!-- 自定义时间输入 -->
            <div>
                <label class="text-sm font-semibold text-gray-600">或自定义分钟数</label>
                <input 
                    type="number" 
                    placeholder="输入分钟数" 
                    class="w-full mt-1 p-3 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none"
                >
            </div>
        </div>
        
        <!-- 确认按钮 -->
        <div class="mt-8">
            <button type="submit" class="w-full text-white font-bold py-3 rounded-lg gradient-button">
                确认推迟
            </button>
        </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 推迟按钮选择逻辑
            const snoozeBtns = document.querySelectorAll('.snooze-btn');
            
            snoozeBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active状态
                    snoozeBtns.forEach(b => b.classList.remove('active'));
                    // 添加当前按钮的active状态
                    this.classList.add('active');
                });
            });

            // 关闭弹窗逻辑
            const closeModal = document.querySelector('.close-modal');
            if (closeModal) {
                closeModal.addEventListener('click', function() {
                    window.history.back();
                });
            }
        });
    </script>
</body>
</html> 