<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重启计划弹窗</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-900/50">
    <div class="w-full max-w-md mx-4">
        <!-- 重启计划弹窗 -->
        <div id="restart-plan-modal" class="modal-content bg-white rounded-xl shadow-2xl">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold text-gray-800">重新激活计划</h2>
            <button class="close-modal text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
        </div>
        
        <p class="text-sm text-gray-600 mb-6">
            您可以为此已完成的计划新建任务，或选择重新开始已有的任务。
        </p>
        
        <div class="space-y-3">
            <!-- 新建单个任务选项 -->
            <button id="restart-option-new-task" class="restart-option w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <p class="font-semibold text-gray-800">新建单个任务</p>
                <p class="text-xs text-gray-500 mt-1">为这个计划添加一个全新的任务</p>
            </button>
            
            <!-- 选择部分任务重新开始 -->
            <button id="restart-option-select-tasks" class="restart-option w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <p class="font-semibold text-gray-800">选择部分任务重新开始</p>
                <p class="text-xs text-gray-500 mt-1">从列表中选择一个或多个任务来重新激活</p>
            </button>
            
            <!-- 重新开始所有任务 -->
            <button id="restart-option-all-tasks" class="restart-option w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <p class="font-semibold text-gray-800">重新开始所有任务</p>
                <p class="text-xs text-gray-500 mt-1">将计划内的所有任务恢复为待办状态</p>
            </button>
        </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 重启选项点击处理
            const restartOptions = document.querySelectorAll('.restart-option');
            
            restartOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const optionId = this.id;
                    
                    switch(optionId) {
                        case 'restart-option-new-task':
                            console.log('用户选择：新建单个任务');
                            // 这里应该打开新建任务弹窗
                            alert('跳转到新建任务界面');
                            break;
                            
                        case 'restart-option-select-tasks':
                            console.log('用户选择：选择部分任务重新开始');
                            // 这里应该打开任务选择界面
                            alert('跳转到任务选择界面');
                            break;
                            
                        case 'restart-option-all-tasks':
                            console.log('用户选择：重新开始所有任务');
                            // 确认重启所有任务
                            if (confirm('确定要重新激活这个计划的所有任务吗？')) {
                                alert('所有任务已重新激活');
                            }
                            break;
                    }
                    
                    // 关闭弹窗
                    window.history.back();
                });
            });

            // 关闭弹窗逻辑
            const closeModal = document.querySelector('.close-modal');
            if (closeModal) {
                closeModal.addEventListener('click', function() {
                    window.history.back();
                });
            }

            // 添加选项悬停效果
            restartOptions.forEach(option => {
                option.addEventListener('mouseenter', function() {
                    this.classList.add('border-indigo-300', 'bg-indigo-50');
                });
                
                option.addEventListener('mouseleave', function() {
                    this.classList.remove('border-indigo-300', 'bg-indigo-50');
                });
            });
        });
    </script>
</body>
</html> 