<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咯嗒应用 - 新建任务</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-900/50">
    <div class="w-full max-w-md mx-4">
        <div class="modal-content bg-white rounded-xl shadow-2xl">
            <!-- 标题栏 -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-gray-800">新建任务</h2>
                <button class="text-gray-400 hover:text-gray-600 text-2xl transition-colors" onclick="window.history.back()">&times;</button>
            </div>

            <!-- 表单 -->
            <form id="new-task-form">
                <div class="space-y-4">
                    <!-- 任务标题 -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-600 mb-1">任务标题</label>
                        <input type="text" id="task-title" placeholder="例如: 完成第三章习题" class="w-full p-3 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none transition-colors">
                    </div>

                    <!-- 执行日期 -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-600 mb-1">执行日期</label>
                        <input type="date" id="task-date" class="w-full p-3 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none transition-colors">
                    </div>

                    <!-- 所属计划 -->
                    <div class="relative">
                        <label class="block text-sm font-semibold text-gray-600 mb-1">所属计划</label>
                        <div class="relative">
                            <input type="text" id="plan-input" readonly value="无计划" class="w-full p-3 border border-gray-400 rounded-lg bg-white cursor-pointer">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute right-3 top-3.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </div>
                        <div id="plan-dropdown" class="absolute w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10 hidden">
                            <div class="py-1">
                                <div class="px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer" data-value="无计划">无计划</div>
                                <div class="px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer" data-value="考研冲刺">考研冲刺</div>
                                <div class="px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer" data-value="健身增肌计划">健身增肌计划</div>
                                <div class="px-4 py-2 text-gray-700 hover:bg-gray-100 cursor-pointer" data-value="阅读名著100本">阅读名著100本</div>
                            </div>
                        </div>
                    </div>

                    <!-- 重复设置 -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-600 mb-2">重复</label>
                        <div class="grid grid-cols-4 gap-2">
                            <button type="button" class="repeat-btn py-2 border border-gray-400 rounded-lg text-sm font-medium transition-colors active" data-value="不重复">不重复</button>
                            <button type="button" class="repeat-btn py-2 border border-gray-400 rounded-lg text-sm font-medium transition-colors" data-value="每天">每天</button>
                            <button type="button" class="repeat-btn py-2 border border-gray-400 rounded-lg text-sm font-medium transition-colors" data-value="每周">每周</button>
                            <button type="button" class="repeat-btn py-2 border border-gray-400 rounded-lg text-sm font-medium transition-colors" data-value="每月">每月</button>
                        </div>
                    </div>

                    <!-- 优先级 -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-600 mb-2">优先级</label>
                        <div class="grid grid-cols-3 gap-2">
                            <button type="button" class="priority-btn py-2 border border-gray-400 rounded-lg text-sm font-medium transition-colors" data-value="低" data-color="green">低</button>
                            <button type="button" class="priority-btn py-2 border border-gray-400 rounded-lg text-sm font-medium transition-colors active" data-value="中" data-color="orange">中</button>
                            <button type="button" class="priority-btn py-2 border border-gray-400 rounded-lg text-sm font-medium transition-colors" data-value="高" data-color="red">高</button>
                        </div>
                    </div>

                    <!-- 番茄钟数量 -->
                    <div>
                        <label class="block text-sm font-semibold text-gray-600 mb-2">番茄钟数量</label>
                        <div class="flex items-center justify-between p-2 border border-gray-400 rounded-lg">
                            <button type="button" id="pomo-decrease" class="w-8 h-8 rounded-md flex items-center justify-center bg-gray-200/80 text-gray-700 font-bold text-lg hover:bg-gray-300 transition-colors">-</button>
                            <div class="text-gray-800">
                                <span id="pomo-count" class="font-bold text-lg">2</span>
                                <span class="text-sm">个</span>
                            </div>
                            <button type="button" id="pomo-increase" class="w-8 h-8 rounded-md flex items-center justify-center bg-gray-200/80 text-gray-700 font-bold text-lg hover:bg-gray-300 transition-colors">+</button>
                        </div>
                    </div>

                    <!-- 时间设置 -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-semibold text-gray-600 mb-1">开始时间</label>
                            <input type="time" id="start-time" value="09:00" class="w-full p-3 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none">
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-gray-600 mb-1">结束时间</label>
                            <input type="time" id="end-time" value="11:00" class="w-full p-3 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none">
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="mt-8">
                    <button type="submit" class="w-full text-white font-bold py-3 rounded-lg gradient-button transition-all hover:shadow-lg">创建任务</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 番茄钟计数器
            let pomoCount = 2;
            const pomoCountElement = document.getElementById('pomo-count');
            const decreaseBtn = document.getElementById('pomo-decrease');
            const increaseBtn = document.getElementById('pomo-increase');

            // 计划下拉菜单
            const planInput = document.getElementById('plan-input');
            const planDropdown = document.getElementById('plan-dropdown');

            // 重复按钮组
            const repeatBtns = document.querySelectorAll('.repeat-btn');
            let selectedRepeat = '不重复';

            // 优先级按钮组
            const priorityBtns = document.querySelectorAll('.priority-btn');
            let selectedPriority = '中';

            // 番茄钟数量控制
            decreaseBtn.addEventListener('click', function() {
                if (pomoCount > 1) {
                    pomoCount--;
                    pomoCountElement.textContent = pomoCount;
                }
            });

            increaseBtn.addEventListener('click', function() {
                if (pomoCount < 10) {
                    pomoCount++;
                    pomoCountElement.textContent = pomoCount;
                }
            });

            // 计划选择
            planInput.addEventListener('click', function() {
                planDropdown.classList.toggle('hidden');
            });

            // 计划选项点击
            planDropdown.addEventListener('click', function(e) {
                if (e.target.dataset.value) {
                    planInput.value = e.target.dataset.value;
                    planDropdown.classList.add('hidden');
                }
            });

            // 点击外部关闭下拉菜单
            document.addEventListener('click', function(e) {
                if (!planInput.contains(e.target) && !planDropdown.contains(e.target)) {
                    planDropdown.classList.add('hidden');
                }
            });

            // 重复按钮切换
            repeatBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    repeatBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    selectedRepeat = this.dataset.value;
                });
            });

            // 优先级按钮切换
            priorityBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    priorityBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    selectedPriority = this.dataset.value;
                });
            });

            // 表单提交
            document.getElementById('new-task-form').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = {
                    title: document.getElementById('task-title').value,
                    date: document.getElementById('task-date').value,
                    plan: planInput.value,
                    repeat: selectedRepeat,
                    priority: selectedPriority,
                    pomoCount: pomoCount,
                    startTime: document.getElementById('start-time').value,
                    endTime: document.getElementById('end-time').value
                };

                console.log('新建任务:', formData);
                alert('任务创建成功！');
                
                // 实际应用中这里会发送数据到服务器
                // 然后重定向到首页或任务列表
                window.history.back();
            });

            // 设置默认日期为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('task-date').value = today;
        });
    </script>
</body>
</html> 