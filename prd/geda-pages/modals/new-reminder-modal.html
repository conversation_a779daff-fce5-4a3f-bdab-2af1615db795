<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新建提醒 - 咯嗒</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-900/50">
    <div class="w-full max-w-md mx-4">
        <div class="modal-content bg-white rounded-xl shadow-2xl">
                <!-- 标题栏 -->
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">新建提醒</h2>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>

                <form id="reminderForm" onsubmit="handleSubmit(event)">
                    <!-- 提醒内容 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">提醒内容</label>
                        <textarea 
                            name="content" 
                            placeholder="请输入提醒内容..."
                            rows="3"
                            class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
                            required
                        ></textarea>
                    </div>

                    <!-- 提醒时间 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">提醒时间</label>
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <input 
                                    type="date" 
                                    name="date"
                                    id="reminderDate"
                                    class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                    required
                                >
                            </div>
                            <div>
                                <input 
                                    type="time" 
                                    name="time"
                                    id="reminderTime"
                                    class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                    required
                                >
                            </div>
                        </div>
                    </div>

                    <!-- 重复设置 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">重复</label>
                        <select 
                            name="repeat" 
                            class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                        >
                            <option value="none">不重复</option>
                            <option value="daily">每天</option>
                            <option value="weekly">每周</option>
                            <option value="monthly">每月</option>
                            <option value="yearly">每年</option>
                        </select>
                    </div>

                    <!-- 提醒类型 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">提醒类型</label>
                        <div class="grid grid-cols-2 gap-3">
                            <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input type="radio" name="type" value="notification" checked class="mr-3 text-purple-600">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h1v-3a3 3 0 011-1h1m0 0V9a5 5 0 0110 0v6h1a3 3 0 011 1v3h1"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-medium">通知提醒</span>
                                </div>
                            </label>

                            <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input type="radio" name="type" value="popup" class="mr-3 text-purple-600">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-medium">弹窗提醒</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- 优先级 -->
                    <div class="mb-8">
                        <label class="block text-sm font-medium text-gray-700 mb-3">优先级</label>
                        <div class="grid grid-cols-3 gap-3">
                            <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input type="radio" name="priority" value="low" class="mr-3 text-green-600">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                    <span class="text-sm font-medium">低</span>
                                </div>
                            </label>

                            <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input type="radio" name="priority" value="medium" checked class="mr-3 text-yellow-600">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                                    <span class="text-sm font-medium">中</span>
                                </div>
                            </label>

                            <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input type="radio" name="priority" value="high" class="mr-3 text-red-600">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                                    <span class="text-sm font-medium">高</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex space-x-3">
                        <button 
                            type="button"
                            onclick="closeModal()"
                            class="flex-1 py-3 px-4 border border-gray-200 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors"
                        >
                            取消
                        </button>
                        <button 
                            type="submit"
                            class="flex-1 py-3 px-4 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors"
                        >
                            创建提醒
                        </button>
                    </div>
                </form>
        </div>
    </div>

    <script>
        // 初始化默认时间
        function initializeDateTime() {
            const now = new Date();
            const today = now.toISOString().split('T')[0];
            const currentTime = now.toTimeString().slice(0, 5);
            
            document.getElementById('reminderDate').value = today;
            document.getElementById('reminderTime').value = currentTime;
        }

        // 关闭弹窗
        function closeModal() {
            window.history.back();
        }

        // 处理表单提交
        function handleSubmit(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const reminderData = {
                content: formData.get('content'),
                date: formData.get('date'),
                time: formData.get('time'),
                repeat: formData.get('repeat'),
                type: formData.get('type'),
                priority: formData.get('priority')
            };
            
            console.log('创建提醒:', reminderData);
            
            // 显示成功反馈
            showSuccess();
        }

        // 显示成功反馈
        function showSuccess() {
            const button = document.querySelector('button[type="submit"]');
            const originalText = button.textContent;
            
            button.textContent = '✓ 创建成功';
            button.style.backgroundColor = '#10b981';
            
            setTimeout(() => {
                closeModal();
            }, 1000);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeDateTime();
        });

        // 点击背景关闭弹窗 - 新结构下不需要此功能
    </script>
</body>
</html> 