# 咯嗒(Geda) - iOS 智能时间管理应用产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品定位
咯嗒是一款以番茄钟专注法为核心的智能时间管理应用，集成AI语音助手、计划管理、任务调度和学习数据分析功能，专为移动端学习和工作场景设计。

### 1.2 核心价值主张
- **智能化管理**：AI语音助手支持自然语言创建任务和提醒
- **科学专注法**：基于番茄钟技术的专注时间管理
- **层级化规划**：长期计划→日常任务→子任务的完整体系
- **沉浸式体验**：学霸模式提供无干扰专注环境
- **社交化学习**：计划模板分享和成就激励系统

### 1.3 目标用户
- 主要用户：大学生、考研学生、职场学习者
- 次要用户：时间管理需求的专业人士

## 2. 应用架构

### 2.1 技术参数 (基于原型)
- **屏幕尺寸**：420×880px (iPhone标准尺寸适配)
- **设计语言**：现代化扁平设计，渐变配色
- **交互模式**：手势导航 + 底部标签栏

### 2.2 核心页面架构

```
咯嗒应用 (iOS App)
├── 主导航层
│   ├── 📅 首页 (HomeViewController)
│   ├── 📋 计划 (PlanViewController) 
│   ├── 🍅 专注 (FocusViewController)
│   ├── 📊 复盘 (ReviewViewController)
│   └── 👤 我的 (ProfileViewController)
│
├── 详情页面层
│   ├── 计划详情 (PlanDetailViewController)
│   │   ├── 进行中计划详情
│   │   ├── 已搁置计划详情  
│   │   └── 已完成计划详情 (特殊功能：任务重启)
│   ├── 任务详情 (TaskDetailViewController)
│   └── 日历页面 (CalendarViewController)
│
├── 功能弹窗层
│   ├── 新建任务弹窗 (TaskCreationModal)
│   ├── 新建计划弹窗 (PlanCreationModal)
│   ├── 新建提醒弹窗 (ReminderModal)
│   ├── 学霸模式说明弹窗 (FocusModeInfoModal)
│   └── 各类确认弹窗 (ConfirmationModals)
│
└── 扫描功能页 (ScanViewController)
```

## 3. 核心功能模块

### 3.1 首页模块 (HomeViewController)

#### 3.1.1 页面布局
- **顶部状态栏**：时间、网络、电池状态
- **日历区域**：当前日期展示，点击可进入完整日历
- **今日任务卡片**：显示当日计划的任务列表
- **快速操作区**：语音输入、扫描、新建任务快捷入口

#### 3.1.2 任务卡片设计
```
任务卡片组件 (TaskCardView)
├── 任务标题 (黑体，16px)
├── 时间范围 (灰色，12px) 
├── 优先级标识 (彩色圆点)
├── 番茄钟数量 (🍅 图标 + 数字)
└── 完成状态 (复选框)
```

#### 3.1.3 交互逻辑
- 点击任务卡片 → 跳转任务详情页
- 点击语音按钮 → 启动AI语音助手
- 点击扫描按钮 → 进入二维码/条形码扫描页
- 下拉刷新 → 同步最新任务数据

### 3.2 计划管理模块 (PlanViewController)

#### 3.2.1 计划分类展示
- **进行中计划**：显示当前活跃的学习/工作计划
- **已搁置计划**：暂停的计划，支持重新激活
- **已完成计划**：完成的计划，支持查看成果和重启任务

#### 3.2.2 计划卡片信息
```
计划卡片组件 (PlanCardView)
├── 计划名称
├── 计划描述/座右铭
├── 完成进度条
├── 包含任务数量统计
├── 时间范围
└── 状态标识
```

#### 3.2.3 计划详情页功能

##### 进行中计划详情
- 计划基本信息展示
- 包含任务列表（支持批量选择操作）
- 任务创建和编辑
- 计划进度可视化
- 分享计划模板功能

##### 已完成计划详情 (特殊功能)
- 显示计划完成度为100%
- 任务标题添加删除线效果
- **任务重启功能**：选择已完成的任务重新激活
- 成就徽章展示
- 总番茄钟统计

### 3.3 任务详情模块 (TaskDetailViewController)

#### 3.3.1 任务信息展示
- 任务标题和时间安排
- 所属计划关联
- 番茄钟数量设置
- 优先级设置（高/中/低）
- 任务提醒开关

#### 3.3.2 子任务管理
- 子任务完成度统计 (如：0/2)
- 子任务列表（支持复选框交互）
- 新增子任务功能
- 子任务删除功能

#### 3.3.3 操作按钮
- **开始专注**按钮：跳转至番茄钟计时页面

### 3.4 番茄钟专注模块 (FocusViewController)

#### 3.4.1 计时器设计
- **环形进度条**：SVG实现的动态进度圆环
- **时间显示**：大字号时间显示 (如：25:00)
- **状态文本**：工作进行中/休息中状态提示

#### 3.4.2 控制按钮
- **播放/暂停**：中央大圆形按钮
- **重置按钮**：重新开始当前番茄钟
- **跳过按钮**：跳过当前阶段

#### 3.4.3 番茄钟设置
- **工作时间调节**：+/- 按钮调整 (默认25分钟)
- **休息时间调节**：+/- 按钮调整 (默认5分钟)
- **时钟归属显示**：显示当前专注任务名称

#### 3.4.4 专注模式特性
- 全屏沉浸式界面
- 工作/休息自动切换
- 完成后的成就反馈

### 3.5 数据复盘模块 (ReviewViewController)

#### 3.5.1 数据概览卡片
```
今日统计卡片组
├── 今日学习：3.5小时
├── 任务完成：5个
└── 完成度：100%
```

#### 3.5.2 复盘范围选择
- 下拉选择器：全部计划/指定计划
- 支持按计划筛选学习数据

#### 3.5.3 学习时长分析
- **时间维度切换**：周视图/月视图
- **柱状图展示**：每日学习时长可视化
- **当日高亮**：突出显示当天数据

#### 3.5.4 成就墙系统
- **已获得成就**：展示完成的学习目标 (如：CPA经济法、React学习)
- **成就徽章**：不同颜色的圆形徽章设计
- **获得日期**：成就获得的具体时间
- **下一目标**：激励用户继续学习

### 3.6 个人资料模块 (ProfileViewController)

#### 3.6.1 用户信息
- 头像 (圆形)
- 用户名称
- 用户ID
- 连续打卡天数统计

#### 3.6.2 功能入口
- **学霸模式**：专注时屏蔽外界干扰的详细说明
- **官方模板**：获取官方提供的学习计划模板

#### 3.6.3 设置选项
- 通用设置
- 回收站
- 关于我们
- 退出登录

### 3.7 日历模块 (CalendarViewController)

#### 3.7.1 日历视图
- 月历展示
- 前/后月份切换
- 当前日期高亮
- 有任务的日期标记

#### 3.7.2 快速创建
- 浮动添加按钮（右下角）
- 三种创建选项：新建提醒/新建任务/新建计划

### 3.8 扫描功能模块 (ScanViewController)

#### 3.8.1 扫描界面
- 黑色背景的全屏界面
- 中央扫描框（带动画扫描线）
- 四角定位标识
- 扫描提示文本

#### 3.8.2 扫描能力
- 二维码识别
- 条形码识别
- 自动对焦和识别

## 4. 交互设计规范

### 4.1 导航模式
- **主导航**：底部TabBar导航（5个主要模块）
- **页面跳转**：右滑进入，左滑返回
- **弹窗模式**：从底部弹起的模态框

### 4.2 手势交互
- **点击**：按钮点击、卡片选择
- **长按**：任务卡片的批量选择模式
- **滑动**：页面切换、列表滚动
- **下拉刷新**：首页数据更新

### 4.3 反馈机制
- **触觉反馈**：按钮点击时的震动反馈
- **视觉反馈**：按钮缩放动画、状态颜色变化
- **声音反馈**：番茄钟完成的提示音

## 5. 数据结构设计

### 5.1 核心数据模型

#### 用户模型 (User)
```swift
struct User {
    let id: String          // 用户ID (如: PLAN78935)
    let name: String        // 用户名
    let avatar: String      // 头像URL
    let checkInDays: Int    // 连续打卡天数
    let createdAt: Date     // 注册时间
}
```

#### 计划模型 (Plan)
```swift
struct Plan {
    let id: String          // 计划ID
    let title: String       // 计划标题
    let description: String // 计划描述/座右铭
    let status: PlanStatus  // 状态：进行中/已搁置/已完成
    let progress: Float     // 完成进度 (0.0-1.0)
    let startDate: Date     // 开始日期
    let endDate: Date?      // 结束日期
    let totalTomatoes: Int  // 总番茄钟数
    let tasks: [Task]       // 包含的任务
}

enum PlanStatus {
    case active     // 进行中
    case paused     // 已搁置
    case completed  // 已完成
}
```

#### 任务模型 (Task)
```swift
struct Task {
    let id: String          // 任务ID
    let title: String       // 任务标题
    let planId: String      // 所属计划ID
    let startTime: Date     // 开始时间
    let endTime: Date       // 结束时间
    let priority: Priority  // 优先级
    let tomatoCount: Int    // 番茄钟数量
    let isReminderEnabled: Bool // 是否开启提醒
    let subtasks: [Subtask] // 子任务
    let isCompleted: Bool   // 是否完成
}

enum Priority {
    case high, medium, low
}
```

#### 子任务模型 (Subtask)
```swift
struct Subtask {
    let id: String          // 子任务ID
    let title: String       // 子任务标题
    let isCompleted: Bool   // 是否完成
}
```

#### 番茄钟会话模型 (PomodoroSession)
```swift
struct PomodoroSession {
    let id: String          // 会话ID
    let taskId: String      // 关联任务ID
    let sessionType: SessionType // 会话类型
    let duration: TimeInterval  // 时长
    let startTime: Date     // 开始时间
    let endTime: Date?      // 结束时间
    let isCompleted: Bool   // 是否完成
}

enum SessionType {
    case work    // 工作时间
    case break   // 休息时间
}
```

### 5.2 本地存储策略
- **Core Data**：用户数据、计划、任务的持久化存储
- **UserDefaults**：应用设置、番茄钟配置
- **Keychain**：用户敏感信息加密存储

## 6. 技术实现要点

### 6.1 iOS特有功能集成

#### 通知系统
- **本地通知**：任务提醒、番茄钟完成提醒
- **通知分类**：任务提醒、专注提醒、成就解锁
- **通知操作**：快速标记完成、延后提醒

#### 后台执行
- **Background App Refresh**：数据同步
- **Background Processing**：番茄钟后台计时
- **Siri Shortcuts**：语音创建任务和计划

#### 多媒体集成
- **语音识别**：使用Speech框架实现AI语音助手
- **相机集成**：二维码扫描功能
- **触觉引擎**：按钮反馈和专注提醒

### 6.2 UI框架选择
- **UIKit** 或 **SwiftUI**：根据iOS版本要求选择
- **第三方库**：图表库(Charts)、动画库(Lottie)

### 6.3 网络与数据同步
- **RESTful API**：用户数据、计划模板同步
- **WebSocket**：实时数据更新(可选)
- **Cloud Kit**：iCloud数据同步

## 7. 用户体验设计原则

### 7.1 视觉设计
- **配色方案**：主题渐变色 (#667eea → #764ba2)
- **字体系统**：iOS系统字体，层级分明
- **图标设计**：线性图标，简洁现代
- **卡片设计**：圆角矩形，阴影效果

### 7.2 交互设计
- **一致性**：全应用统一的交互模式
- **可预测性**：用户操作结果可预期
- **容错性**：误操作的撤销机制
- **反馈性**：及时的视觉和触觉反馈

### 7.3 可访问性
- **VoiceOver支持**：视障用户的语音导航
- **动态字体**：支持系统字体大小设置
- **高对比度**：适配高对比度模式
- **减少动画**：尊重用户的减少动画设置

## 8. 开发优先级与里程碑

### 8.1 MVP功能 (第一版本)
1. **基础导航框架**：TabBar导航和页面跳转
2. **任务管理核心**：创建、编辑、删除任务
3. **番茄钟计时器**：基础计时功能
4. **数据本地存储**：Core Data集成
5. **基础UI组件**：卡片、按钮、输入框

### 8.2 增强功能 (第二版本)
1. **计划管理系统**：多层级的计划-任务关系
2. **数据可视化**：学习时长图表和成就系统
3. **通知提醒**：本地通知集成
4. **语音助手**：基础语音识别功能

### 8.3 高级功能 (第三版本)
1. **云端同步**：多设备数据同步
2. **社交分享**：计划模板分享
3. **AI增强**：智能任务调度建议
4. **Siri集成**：语音快捷指令

## 9. 质量保证与测试

### 9.1 功能测试
- **单元测试**：Core Data操作、业务逻辑
- **UI测试**：页面跳转、用户交互流程
- **集成测试**：通知、语音识别、相机等系统功能

### 9.2 性能优化
- **内存管理**：避免内存泄漏，优化大数据加载
- **电池优化**：后台处理的电池消耗控制
- **启动优化**：应用冷启动时间控制在3秒内

### 9.3 兼容性测试
- **iOS版本**：支持iOS 14+
- **设备适配**：iPhone SE到iPhone 15 Pro Max
- **网络环境**：离线模式和弱网络处理

## 10. 上线与运营考虑

### 10.1 App Store优化
- **应用描述**：突出番茄钟专注和AI助手特性
- **关键词优化**：时间管理、番茄钟、学习助手
- **截图设计**：突出核心功能界面

### 10.2 用户反馈机制
- **应用内反馈**：意见收集和Bug报告
- **版本更新**：功能改进和用户建议响应
- **崩溃监控**：Crashlytics集成

### 10.3 数据隐私合规
- **隐私政策**：明确数据收集和使用范围
- **GDPR合规**：欧盟用户的数据权利保护
- **iOS隐私标签**：App Store隐私信息准确填写

---

## 附录：技术栈建议

### 前端技术
- **开发语言**：Swift 5.5+
- **UI框架**：SwiftUI (iOS 14+) 或 UIKit
- **动画框架**：SwiftUI Animations 或 Lottie
- **图表库**：Swift Charts 或 Charts

### 后端技术 (如需云端功能)
- **服务器**：Node.js + Express 或 Python + Django
- **数据库**：PostgreSQL 或 MongoDB
- **云服务**：AWS、Azure 或 阿里云
- **实时通信**：WebSocket 或 Server-Sent Events

### 第三方服务
- **语音识别**：Apple Speech Framework
- **消息推送**：Apple Push Notification Service
- **崩溃监控**：Firebase Crashlytics
- **分析统计**：Firebase Analytics 或 Apple Analytics

这份需求文档为咯嗒应用的iOS开发提供了完整的功能规范和技术指导，确保开发团队能够准确理解产品需求并高效实施。