<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整App高保真交互原型 (Final Version)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        .tasks-container.tasks-expanded + #collapse-tasks-wrapper {
            opacity: 1;
            pointer-events: auto;
        }
        :root {
            --bg-shell: #F7F8FC; 
            --bg-page: #FFFFFF;
            --text-primary: #1F2937;
            --text-secondary: #6B7280;
            --accent-start: #818cf8;
            --accent-end: #a78bfa;
            --card-shadow: 0 10px 35px -5px rgba(180, 190, 220, 0.2);
            --priority-high: #ef4444; /* red-500 */
            --priority-medium: #f97316; /* orange-500 */
            --priority-low: #22c55e; /* green-500 */
        }
        body { 
            font-family: 'Noto Sans SC', 'Inter', sans-serif;
            background-color: #FFFFFF;
        }
        .app-shell {
            width: 100%; height: 100vh;
            max-width: 420px; max-height: 880px;
            box-shadow: 0 20px 50px -10px rgba(0, 0, 0, 0.15);
            overflow: hidden; position: relative;
            background-color: var(--bg-shell);
        }
        .page {
            position: absolute; top: 0; left: 0; width: 100%; height: 100%;
            background-color: var(--bg-shell);
            display: flex; flex-direction: column;
            z-index: 10;
            transition: transform 0.4s ease;
        }
        .page.hidden-right { transform: translateX(100%); z-index: 9; }
        .page.hidden-left { transform: translateX(-30%); z-index: 9; }
        .main-content::-webkit-scrollbar { display: none; }
        .main-content.noscroll { overflow-y: hidden; }

        .custom-card {
            background-color: var(--bg-page);
            border-radius: 1.25rem;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
        }
        .tasks-container { position: relative; height: 128px; transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1); }
        .task-card { position: absolute; width: 100%; height: 128px; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); cursor: pointer; padding: 20px; }
        
        .task-card-1 { z-index: 30; }
        .task-card-2 { transform: scale(0.95) translateX(16px); z-index: 20; background-color: rgba(255, 255, 255, 0.8); }
        .task-card-3 { transform: scale(0.9) translateX(32px); z-index: 10; background-color: rgba(255, 255, 255, 0.6); }

        .tasks-container.tasks-expanded { height: 416px; }
        .tasks-container.tasks-expanded .task-card { transform: scale(1) translateX(0); cursor: default; background-color: white; }
        .tasks-container.tasks-expanded .task-card-2 { top: 144px; }
        .tasks-container.tasks-expanded .task-card-3 { top: 288px; }
        
        .modal-overlay {
            position: absolute; top: 0; left: 0; right: 0; bottom: 0;
            background-color: rgba(30, 41, 59, 0.5);
            backdrop-filter: blur(2px);
            display: flex; align-items: center; justify-content: center;
            opacity: 0; pointer-events: none;
            transition: opacity 0.3s ease-in-out;
            z-index: 100;
        }
        .modal-overlay.active { opacity: 1; pointer-events: auto; }
        .modal-content {
            background-color: white; padding: 24px; border-radius: 16px; width: 90%; max-width: 380px;
            box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1), 0 10px 10px -5px rgba(0,0,0,0.04);
            transform: scale(0.95); transition: transform 0.3s ease-in-out;
        }
        .modal-overlay.active .modal-content { transform: scale(1); }
        .priority-btn.active, .snooze-btn.active, .repeat-btn.active { background-image: linear-gradient(to right, var(--accent-start), var(--accent-end)); color: white; border-color: transparent; }
        .gradient-button { background-image: linear-gradient(to right, var(--accent-start), var(--accent-end)); transition: all 0.3s ease; }
        .gradient-button:hover { box-shadow: 0 10px 20px -5px rgba(129, 140, 248, 0.5); }
        .gradient-text { background-image: linear-gradient(to right, var(--accent-start), var(--accent-end)); -webkit-background-clip: text; color: transparent; }

        .chart-bar { transition: all 0.3s ease; }
        .chart-bar:hover { transform: translateY(-4px); opacity: 0.8; }
        
        @keyframes scan-line { 0% { top: 0; } 100% { top: 95%; } }
        .animate-scan { animation: scan-line 2.5s ease-in-out infinite alternate; }

        .text-strikethrough {
            text-decoration: line-through;
            color: var(--text-secondary);
        }
        
        .plan-hidden-section {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s ease-in-out;
        }
        .plan-visible-section { max-height: 500px; /* Adjust as needed */ }
        
        #share-action-sheet-overlay {
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease-in-out;
        }
        #share-action-sheet-overlay.active {
            opacity: 1;
            pointer-events: auto;
        }
        #share-action-sheet-content {
            transform: translateY(100%);
            transition: transform 0.3s ease-out;
        }
        #share-action-sheet-overlay.active #share-action-sheet-content {
            transform: translateY(0);
        }

        .task-delete-button {
            transition: all 0.2s ease-in-out;
        }
        .task-delete-button:hover {
            color: var(--priority-high);
            transform: scale(1.1);
        }
        .plan-details-task-card {
            border-left-width: 4px;
            transition: all 0.3s ease;
        }
        .selection-mode-active .selection-checkbox {
            display: flex;
        }
        .selection-mode-active .task-delete-button {
            display: none;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .toggle-slider {
            background-image: linear-gradient(to right, var(--accent-start), var(--accent-end));
        }
        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }

        /* --- NEW: Styles for Suggested Features START --- */
        .tasks-container.list-view {
            height: auto !important; /* Override fixed height */
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        .tasks-container.list-view .task-card {
            position: static !important;
            transform: none !important;
            width: 100% !important;
            height: auto !important; /* Auto height for content */
            background-color: white !important;
            opacity: 1 !important;
            z-index: 1 !important;
            padding: 16px;
        }
        .tasks-container.list-view .task-card p.text-lg { font-size: 1rem; }
        .tasks-container.list-view .task-card p.text-sm { font-size: 0.75rem; }
        .tasks-container.list-view + #collapse-tasks-wrapper { display: none; }
        
        .subtask-item { display: flex; align-items: center; padding: 8px 0; border-bottom: 1px solid #f3f4f6; }
        .subtask-item:last-child { border-bottom: none; }
        .subtask-item input[type="checkbox"] { width: 1.15rem; height: 1.15rem; margin-right: 12px; }
        .subtask-item.completed label { text-decoration: line-through; color: var(--text-secondary); }

        #onboarding-overlay {
            position: absolute; inset: 0; background-color: rgba(0, 0, 0, 0.7);
            z-index: 200; display: flex; flex-direction: column; justify-content: flex-end;
            opacity: 0; pointer-events: none; transition: opacity 0.3s ease;
        }
        #onboarding-overlay.active { opacity: 1; pointer-events: auto; }
        @keyframes arrow-bounce { 0%, 100% { transform: translateY(0); } 50% { transform: translateY(-10px); } }
        .onboarding-arrow { animation: arrow-bounce 1.5s infinite; }
        
        .empty-state {
            display: none; /* Hidden by default */
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }
        .empty-state.visible { display: block; }
        /* --- NEW: Styles for Suggested Features END --- */
    </style>
</head>
<body class="flex items-center justify-center min-h-screen">
    <div class="app-shell rounded-3xl">
        
        <div id="page-container" class="page-container">

            <div id="page-home" class="page">
                <header class="flex-shrink-0 px-6 pt-3 pb-2"><div class="flex justify-between items-center text-xs font-bold text-gray-800"><span>12:00</span><div class="flex items-center space-x-1"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12.55a11 11 0 0 1 14.08 0"></path><path d="M1.42 9a16 16 0 0 1 21.16 0"></path><path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path><path d="M12 20h.01"></path></svg><svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor"><path d="M18.667 6.002a2.333 2.333 0 0 1 2.333 2.333v7.333a2.333 2.333 0 0 1-2.333-2.333H5.333a2.333 2.333 0 0 1-2.333-2.333V8.335a2.333 2.333 0 0 1 2.333-2.333h13.334zM22.167 8.335a1.167 1.167 0 0 0-1.167-1.167h-.833v9.667h.833a1.167 1.167 0 0 0 1.167-1.167V8.335z"></path></svg></div></div></header>
                <div class="flex-grow overflow-y-auto main-content pb-28">
                    <div class="px-6 pt-3 flex justify-between items-center">
                        <div class="bg-gray-100 p-1 rounded-full flex items-center">
                            <button class="nav-link px-5 py-2 text-base font-bold bg-white text-indigo-600 rounded-full shadow-md transition-all duration-300" data-target="page-home">日程</button>
                            <button class="nav-link px-5 py-2 text-base font-bold text-gray-500 hover:text-gray-800 transition-all duration-300" data-target="page-plan">计划</button>
                        </div>
                        <div class="flex items-center space-x-4">
                            <a href="#" class="nav-link text-gray-500 hover:text-indigo-400" title="复盘" data-target="page-review"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/><path d="M12 11h4"/><path d="M12 16h4"/><path d="M8 11h.01"/><path d="M8 16h.01"/><rect x="8" y="2" width="8" height="4" rx="1" ry="1"/></svg></a>
                            <a href="#" class="nav-link text-gray-500 hover:text-indigo-400" data-target="page-scan"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 7V5a2 2 0 0 1 2-2h2"/><path d="M17 3h2a2 2 0 0 1 2 2v2"/><path d="M21 17v2a2 2 0 0 1-2 2h-2"/><path d="M7 21H5a2 2 0 0 1-2-2v-2"/><path d="M7 12h10"/></svg></a>
                            <a href="#" class="nav-link" data-target="page-profile"><div class="w-9 h-9 bg-gray-200 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-500"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg></div></a>
                        </div>
                    </div>
                    <div class="px-6 mt-6"><div class="custom-card p-4"><div class="flex justify-between items-center"><span id="home-calendar-month" class="font-bold text-lg text-gray-800"></span><span class="text-xs text-gray-400">开始新的一天吧</span></div><div id="home-calendar-week" class="mt-4 grid grid-cols-7 text-center text-sm"></div></div></div>
                    <div class="flex justify-center mt-2"><a href="#" class="nav-link" data-target="page-calendar"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400"><polyline points="6 9 12 15 18 9"></polyline></svg></a></div>
                     <div class="px-6 mt-6">
    <h2 class="font-bold text-gray-800 text-lg mb-4">今日提醒</h2>
    <div class="custom-card p-4 flex items-center space-x-3">
        <div class="w-10 h-10 bg-yellow-100 rounded-full flex-shrink-0 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-yellow-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"></path><path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"></path></svg>
        </div>
        <p id="reminder-text" class="text-gray-700 font-medium text-sm flex-grow transition-colors">晚上八点, CPA经济法报名?</p>
        <div class="relative">
            <button id="reminder-options-btn" class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100" data-menu="reminder-menu">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z" /></svg>
            </button>
            <div id="reminder-menu" class="popup-menu absolute right-0 bottom-10 w-36 bg-white rounded-lg shadow-xl p-2 z-50 origin-bottom-right scale-95 opacity-0 pointer-events-none transition-all">
                <a href="#" id="mark-reminder-done-btn" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">标记为完成</a>
                <a href="#" id="snooze-reminder-btn" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">推迟</a>
            </div>
        </div>
    </div>
</div>
                    <div class="mt-8 px-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="font-bold text-gray-800 text-lg">今日任务</h2>
                            <div class="flex items-center space-x-2">
                                <div class="relative">
                                    <button id="sort-tasks-btn" class="flex items-center space-x-1 text-xs text-gray-500 font-medium p-1 rounded-md hover:bg-gray-100">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                        <span id="sort-tasks-text">按开始时间排序</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                                    </button>
                                    <div id="sort-tasks-menu" class="popup-menu absolute right-0 mt-2 w-32 bg-white rounded-lg shadow-xl p-2 z-50 origin-top-right scale-95 opacity-0 pointer-events-none transition-all">
                                        <a href="#" class="sort-option block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">按开始时间</a>
                                        <a href="#" class="sort-option block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">按优先级</a>
                                        <a href="#" class="sort-option block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">按创建时间</a>
                                    </div>
                                </div>
                                <button id="view-toggle-btn" class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                                    <svg id="view-icon-list" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 10h16M4 14h16M4 18h16" /></svg>
                                    <svg id="view-icon-stack" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M4 5v14l7-3.5L18 19V5l-7 3.5L4 5z" /></svg>
                                </button>
                            </div>
                            </div>
                        <div id="tasks-container" class="tasks-container">
                            <div id="tasks-empty-state" class="empty-state">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-16 h-16 mx-auto text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <h3 class="mt-2 text-sm font-semibold text-gray-700">今日无任务</h3>
                                <p class="mt-1 text-sm text-gray-500">试着通过下方的麦克风添加一个吧！</p>
                            </div>
                            <div id="task-card-3" class="task-card task-card-3 custom-card flex items-center nav-link" data-target="page-task-details" data-task-name="背诵新概念英语第四册"><div class="flex-grow"><p class="font-bold text-gray-800 text-lg">背诵新概念英语第四册</p><p class="text-sm text-gray-400 mt-1 flex items-center"><span>15:30 - 16:30</span><span class="mx-2 font-bold">&middot;</span><span>🍅 2 番茄</span></p><div class="w-full bg-gray-200 rounded-full h-2 mt-4"><div class="h-2 rounded-full gradient-button" style="width: 50%;"></div></div></div><div class="ml-4 flex-shrink-0"><a href="#" class="nav-link" data-target="page-clock" data-task-name="背诵新概念英语第四册"><button class="w-14 h-14 bg-indigo-50 text-indigo-500 rounded-full flex items-center justify-center shadow-sm hover:bg-indigo-100 transition-colors"><svg class="w-8 h-8 ml-1" viewBox="0 0 24 24" fill="currentColor"><path d="M8 5v14l11-7z"></path></svg></button></a></div></div>
                            <div id="task-card-2" class="task-card task-card-2 custom-card flex items-center nav-link" data-target="page-task-details" data-task-name="复习长难句结构分析"><div class="flex-grow"><p class="font-bold text-gray-800 text-lg">复习长难句结构分析</p><p class="text-sm text-gray-400 mt-1 flex items-center"><span>14:00 - 15:00</span><span class="mx-2 font-bold">&middot;</span><span>🍅 2 番茄</span></p><div class="w-full bg-gray-200 rounded-full h-2 mt-4"><div class="h-2 rounded-full gradient-button" style="width: 75%;"></div></div></div><div class="ml-4 flex-shrink-0"><a href="#" class="nav-link" data-target="page-clock" data-task-name="复习长难句结构分析"><button class="w-14 h-14 bg-indigo-50 text-indigo-500 rounded-full flex items-center justify-center shadow-sm hover:bg-indigo-100 transition-colors"><svg class="w-8 h-8 ml-1" viewBox="0 0 24 24" fill="currentColor"><path d="M8 5v14l11-7z"></path></svg></button></a></div></div>
                            <div id="task-card-1" class="task-card task-card-1 custom-card flex items-center nav-link" data-target="page-task-details" data-task-name="学习考研英语"><div class="flex-grow"><p class="font-bold text-gray-800 text-lg">学习考研英语</p><p class="text-sm text-gray-400 mt-1 flex items-center"><span>09:00 - 11:00</span><span class="mx-2 font-bold">&middot;</span><span>🍅 4 番茄</span></p><div class="w-full bg-gray-200 rounded-full h-2 mt-4"><div class="h-2 rounded-full gradient-button" style="width: 25%;"></div></div></div><div class="ml-4 flex-shrink-0"><a href="#" class="nav-link" data-target="page-clock" data-task-name="学习考研英语"><button class="w-14 h-14 bg-indigo-50 text-indigo-500 rounded-full flex items-center justify-center shadow-sm hover:bg-indigo-100 transition-colors"><svg class="w-8 h-8 ml-1" viewBox="0 0 24 24" fill="currentColor"><path d="M8 5v14l11-7z"></path></svg></button></a></div></div>
                        </div>
                        <div id="collapse-tasks-wrapper" class="flex justify-center -mt-1 opacity-0 pointer-events-none transition-opacity duration-300">
                            <button id="new-collapse-btn" class="text-gray-400 hover:text-indigo-500 p-1 rounded-full hover:bg-gray-100 transition-all">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M5 15l7-7 7 7" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                 <footer class="absolute bottom-4 left-0 right-0 w-full max-w-[420px] mx-auto px-6 z-40">
                    <div class="bg-white/70 backdrop-blur-lg rounded-full flex items-center justify-between p-2 shadow-2xl shadow-gray-400/20">
                        <button id="ai-voice-btn" class="w-12 h-12 rounded-full flex items-center justify-center text-white gradient-button transform active:scale-90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z"></path></svg></button>
                        <span class="text-sm text-gray-500 font-medium px-2 text-center">可以对我讲你的待办任务</span>
                        <div class="relative">
                             <button id="add-btn" class="add-button w-12 h-12 rounded-full flex items-center justify-center text-white shadow-md gradient-button transform active:scale-90" data-menu="add-menu"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></button>
                             <div id="add-menu" class="popup-menu absolute right-0 bottom-16 w-40 bg-white rounded-lg shadow-xl p-2 z-50 origin-bottom-right scale-95 opacity-0 pointer-events-none transition-all">
                                <a href="#" class="nav-link flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md" data-target="page-clock" data-task-name="快速专注">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-3 text-indigo-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon></svg>
                                    <span class="font-semibold">立即专注</span>
                                </a>
                                <div class="border-t my-1 -mx-2"></div>
                                <a href="#" class="action-new-reminder flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">新建提醒</a>
                                <a href="#" class="action-new-task flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">新建任务</a>
                                <a href="#" class="action-new-plan flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">新建计划</a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
            
            <div id="page-plan" class="page hidden-right">
                <header class="flex-shrink-0 px-6 pt-3 pb-2"><div class="flex justify-between items-center text-xs font-bold text-gray-800"><span>12:00</span><div class="flex items-center space-x-1"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12.55a11 11 0 0 1 14.08 0"></path><path d="M1.42 9a16 16 0 0 1 21.16 0"></path><path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path><path d="M12 20h.01"></path></svg><svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor"><path d="M18.667 6.002a2.333 2.333 0 0 1 2.333 2.333v7.333a2.333 2.333 0 0 1-2.333-2.333H5.333a2.333 2.333 0 0 1-2.333-2.333V8.335a2.333 2.333 0 0 1 2.333-2.333h13.334zM22.167 8.335a1.167 1.167 0 0 0-1.167-1.167h-.833v9.667h.833a1.167 1.167 0 0 0 1.167-1.167V8.335z"></path></svg></div></div></header>
                <div class="flex-grow overflow-y-auto main-content pb-28">
                    <div class="px-6 pt-3 flex justify-between items-center">
                        <div class="bg-gray-100 p-1 rounded-full flex items-center">
                            <button class="nav-link px-5 py-2 text-base font-bold text-gray-500 hover:text-gray-800 transition-all duration-300" data-target="page-home">日程</button>
                            <button class="nav-link px-5 py-2 text-base font-bold bg-white text-indigo-600 rounded-full shadow-md transition-all duration-300" data-target="page-plan">计划</button>
                        </div>
                    </div>
                    <div class="px-6 mt-4 grid grid-cols-2 gap-4">
                        <button id="import-share-code-btn" type="button" class="flex items-center justify-center space-x-2 bg-indigo-50 text-indigo-600 font-semibold py-3 rounded-xl hover:bg-indigo-100 transition-all duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" /></svg>
                            <span>导入分享码</span>
                        </button>
                        <button class="flex items-center justify-center space-x-2 bg-indigo-50 text-indigo-600 font-semibold py-3 rounded-xl hover:bg-indigo-100 transition-all duration-200">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0l2-2m-2 2l-2-2" /></svg>
                            <span>探索官方模板</span>
                        </button>
                    </div>
                    
                    <div class="px-6 mt-8">
                        <h3 class="text-lg font-bold" style="color: var(--text-primary);">进行中</h3>
                        <div class="mt-4 space-y-4">
                            <div class="custom-card p-4 nav-link cursor-pointer hover:shadow-lg" data-target="page-plan-details-1">
                                <div class="flex justify-between items-center">
                                    <span class="font-bold pointer-events-none" style="color: var(--text-primary);">考研冲刺</span>
                                    <button class="share-plan-btn text-gray-400 hover:text-gray-600"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pointer-events-none" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" /></svg></button>
                                </div>
                                <div class="mt-3 flex items-center space-x-3 pointer-events-none">
                                    <span class="text-xs" style="color: var(--text-secondary);">进度</span>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5"><div class="h-2.5 rounded-full gradient-button" style="width: 65%"></div></div>
                                    <span class="text-xs font-semibold" style="color: var(--text-secondary);">65%</span>
                                </div>
                            </div>
                            <div class="custom-card p-4 nav-link cursor-pointer hover:shadow-lg" data-target="page-plan-details-2">
                                <div class="flex justify-between items-center">
                                    <span class="font-bold pointer-events-none" style="color: var(--text-primary);">健身增肌计划</span>
                                    <button class="share-plan-btn text-gray-400 hover:text-gray-600"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pointer-events-none" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" /></svg></button>
                                </div>
                                <div class="mt-3 flex items-center space-x-3 pointer-events-none">
                                    <span class="text-xs" style="color: var(--text-secondary);">进度</span>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5"><div class="h-2.5 rounded-full gradient-button" style="width: 30%"></div></div>
                                    <span class="text-xs font-semibold" style="color: var(--text-secondary);">30%</span>
                                </div>
                            </div>
                            <div id="more-in-progress" class="plan-hidden-section space-y-4">
                                <div class="custom-card p-4 nav-link cursor-pointer hover:shadow-lg" data-target="page-plan-details-3">
                                   <div class="flex justify-between items-center">
                                       <span class="font-bold pointer-events-none" style="color: var(--text-primary);">阅读名著100本</span>
                                       <button class="share-plan-btn text-gray-400 hover:text-gray-600"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pointer-events-none" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" /></svg></button>
                                   </div>
                                   <div class="mt-3 flex items-center space-x-3 pointer-events-none">
                                       <span class="text-xs" style="color: var(--text-secondary);">进度</span>
                                       <div class="w-full bg-gray-200 rounded-full h-2.5"><div class="h-2.5 rounded-full gradient-button" style="width: 15%"></div></div>
                                       <span class="text-xs font-semibold" style="color: var(--text-secondary);">15%</span>
                                   </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-center my-3"><button id="toggle-in-progress-btn" class="text-gray-400 hover:text-gray-600 p-1"><svg id="toggle-in-progress-icon" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" /></svg></button></div>
                    </div>

                    <div class="px-6 mt-4">
    <h3 class="text-lg font-bold" style="color: var(--text-primary);">已搁置</h3>
    <div class="mt-4">
        <div id="toggle-shelved-btn" class="custom-card p-4 flex justify-between items-center cursor-pointer hover:shadow-lg">
            <span class="font-semibold" style="color: var(--text-primary);">搁置的计划 (1)</span>
            <button class="p-1">
                <svg id="toggle-shelved-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                </svg>
            </button>
        </div>

        <div id="more-shelved" class="mt-4 plan-hidden-section space-y-4">
            <div class="custom-card p-4 nav-link cursor-pointer hover:shadow-lg opacity-70" data-target="page-plan-details-4">
               <div class="flex justify-between items-center">
                   <span class="font-bold pointer-events-none" style="color: var(--text-primary);">Side Project X</span>
                   <button class="share-plan-btn text-gray-400 hover:text-gray-600"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pointer-events-none" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path></svg></button>
               </div>
               <div class="mt-3 flex items-center space-x-3 pointer-events-none">
                   <span class="text-xs" style="color: var(--text-secondary);">进度</span>
                   <div class="w-full bg-gray-200 rounded-full h-2.5"><div class="h-2.5 rounded-full bg-gray-400" style="width: 40%"></div></div>
                   <span class="text-xs font-semibold" style="color: var(--text-secondary);">40%</span>
               </div>
            </div>
            </div>
    </div>
</div>
                    
                    <div class="px-6 mt-4">
                        <h3 class="text-lg font-bold" style="color: var(--text-primary);">已完成</h3>
                        <div class="mt-4">
                           <div class="custom-card p-4 flex justify-between items-center nav-link cursor-pointer" data-target="page-plan-details-completed-cpa">
                                <span class="font-semibold text-strikethrough pointer-events-none">CPA经济法通关</span>
                                <div class="flex items-center space-x-3">
                                   <button class="share-plan-btn text-gray-400 hover:text-gray-600"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pointer-events-none" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" /></svg></button>
                                   <button id="toggle-completed-btn" class="p-1"><svg id="toggle-completed-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" /></svg></button>
                                </div>
                           </div>
                        </div>
                        <div id="more-completed" class="mt-4 plan-hidden-section space-y-4">
                            <div class="plan-completed-item custom-card p-4 opacity-80 flex justify-between items-center nav-link cursor-pointer" data-target="page-plan-details-completed-ui">
                               <span class="font-semibold text-strikethrough pointer-events-none">完成UI设计原型</span>
                               <div class="flex items-center space-x-3">
                                   <button class="share-plan-btn text-gray-400 hover:text-gray-600"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pointer-events-none" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path></svg></button>
                                   <button class="delete-item-icon hidden text-gray-400 hover:text-red-500 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd"></path></svg></button>
                               </div>
                            </div>
                            <div class="plan-completed-item custom-card p-4 opacity-80 flex justify-between items-center nav-link cursor-pointer" data-target="page-plan-details-completed-react">
                               <span class="font-semibold text-strikethrough pointer-events-none">学习React基础</span>
                               <div class="flex items-center space-x-3">
                                    <button class="share-plan-btn text-gray-400 hover:text-gray-600"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pointer-events-none" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path></svg></button>
                                    <button class="delete-item-icon hidden text-gray-400 hover:text-red-500 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd"></path></svg></button>
                               </div>
                           </div>
                           <div id="clear-all-container" class="hidden flex justify-center pt-2">
                               <button id="clear-all-btn" class="w-full bg-red-50 text-red-600 font-bold py-3 rounded-xl hover:bg-red-100 transition-colors duration-200 flex items-center justify-center space-x-2">
                                   <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd" /></svg>
                                   <span>一键清空</span>
                               </button>
                           </div>
                        </div>
                    </div>
                </div>

                <footer class="absolute bottom-4 left-0 right-0 w-full max-w-[420px] mx-auto px-6 z-40">
                    <div class="bg-white/70 backdrop-blur-lg rounded-full flex items-center justify-between p-2 shadow-2xl shadow-gray-400/20">
                        <button class="w-12 h-12 rounded-full flex items-center justify-center text-white gradient-button transform active:scale-90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z"></path></svg></button>
                        <span class="text-sm text-gray-500 font-medium px-2 text-center">通过语音或点击+号新建计划</span>
                        <div class="relative">
                             <button id="add-btn-plan" class="add-button w-12 h-12 rounded-full flex items-center justify-center text-white shadow-md gradient-button transform active:scale-90" data-menu="add-menu-plan"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></button>
                             <div id="add-menu-plan" class="popup-menu absolute right-0 bottom-16 w-36 bg-white rounded-lg shadow-xl p-2 z-50 origin-bottom-right scale-95 opacity-0 pointer-events-none transition-all">
                                <a href="#" class="action-new-reminder flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">新建提醒</a>
                                <a href="#" class="action-new-task flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">新建任务</a>
                                <a href="#" class="action-new-plan flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">新建计划</a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
            
            <div id="page-plan-details-1" class="page hidden-right">
                <header class="flex-shrink-0 px-4 pt-4 pb-3 flex items-center relative">
                    <button class="back-link p-2 -ml-2 text-gray-600 hover:bg-gray-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg>
                    </button>
                    <h1 class="text-xl font-bold text-center flex-grow" style="color: var(--text-primary);">计划详情</h1>
                    <div class="w-10">
                        <button id="plan-details-options-btn-1" class="options-button p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100" data-menu="plan-details-menu-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" /></svg>
                        </button>
                         <div id="plan-details-menu-1" class="popup-menu absolute right-4 top-14 w-36 bg-white rounded-lg shadow-xl p-2 z-50 origin-top-right scale-95 opacity-0 pointer-events-none transition-all">
                            <a href="#" class="share-plan-btn-details flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">分享计划</a>
                            <a href="#" class="shelve-plan-btn-details flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">搁置计划</a>
                            <div class="border-t my-1"></div>
                            <a href="#" class="delete-plan-btn-details flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md">删除计划</a>
                        </div>
                    </div>
                </header>
                <div class="flex-grow overflow-y-auto main-content pb-28">
                    <div class="px-6 mt-2">
                        <div class="p-5 rounded-2xl text-white gradient-button relative overflow-hidden">
                            <div class="absolute -right-8 -bottom-8 w-32 h-32 bg-white/10 rounded-full"></div>
                            <div class="relative z-10">
                                <h2 class="text-2xl font-bold">考研冲刺</h2>
                                <p class="text-sm opacity-80 mt-1">行百里者，半九十</p>
                                <div class="relative w-20 h-20 mx-auto my-4">
                                    <svg class="w-full h-full" viewBox="0 0 100 100"><circle cx="50" cy="50" r="45" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="8"></circle><circle cx="50" cy="50" r="45" fill="none" stroke="white" stroke-width="8" stroke-linecap="round" transform="rotate(-90 50 50)" stroke-dasharray="282.7" stroke-dashoffset="98.9"></circle></svg>
                                    <div class="absolute inset-0 flex items-center justify-center text-2xl font-bold">65%</div>
                                </div>
                                <div class="grid grid-cols-2 gap-x-4 text-sm text-center">
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" /></svg><span>总番茄: <span class="font-semibold">22</span></span></div>
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" /></svg><span>6/1 - 12/22</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-6 mt-6">
                        <h3 class="text-lg font-bold" style="color: var(--text-primary);">包含任务</h3>
                        <div class="mt-4 space-y-3 plan-details-task-list">
                            <div class="custom-card p-4 flex items-center plan-details-task-card border-l-red-500 nav-link cursor-pointer" data-target="page-task-details">
                                <div class="flex-grow pointer-events-none">
                                    <p class="font-semibold" style="color: var(--text-primary);">学习考研英语</p>
                                    <div class="text-sm mt-1 space-x-3" style="color: var(--text-secondary);"><span>优先级: <span class="font-medium" style="color: var(--priority-high);">高</span></span><span>🍅 4 番茄</span></div>
                                </div>
                                <button class="task-delete-button text-gray-300 p-2 ml-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pointer-events-none" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd"></path></svg></button>
                            </div>
                            <div class="custom-card p-4 flex items-center plan-details-task-card border-l-orange-500 nav-link cursor-pointer" data-target="page-task-details">
                                <div class="flex-grow pointer-events-none">
                                    <p class="font-semibold" style="color: var(--text-primary);">复习长难句结构分析</p>
                                    <div class="text-sm mt-1 space-x-3" style="color: var(--text-secondary);"><span>优先级: <span class="font-medium" style="color: var(--priority-medium);">中</span></span><span>🍅 2 番茄</span></div>
                                </div>
                                <button class="task-delete-button text-gray-300 p-2 ml-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pointer-events-none" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd"></path></svg></button>
                            </div>
                             <div class="custom-card p-4 flex items-center plan-details-task-card border-l-green-500 nav-link cursor-pointer" data-target="page-task-details">
                                <div class="flex-grow pointer-events-none">
                                    <p class="font-semibold" style="color: var(--text-primary);">背诵新概念英语第四册</p>
                                    <div class="text-sm mt-1 space-x-3" style="color: var(--text-secondary);"><span>优先级: <span class="font-medium" style="color: var(--priority-low);">低</span></span><span>🍅 2 番茄</span></div>
                                </div>
                                <button class="task-delete-button text-gray-300 p-2 ml-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pointer-events-none" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd"></path></svg></button>
                            </div>
                        </div>
                    </div>
                </div>
                <footer class="absolute bottom-4 left-0 right-0 w-full max-w-[420px] mx-auto px-6 z-40">
                    <div class="bg-white/70 backdrop-blur-lg rounded-full flex items-center justify-between p-2 shadow-2xl shadow-gray-400/20">
                        <button class="w-12 h-12 rounded-full flex items-center justify-center text-white gradient-button transform active:scale-90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z"></path></svg></button>
                        <span class="text-sm text-gray-500 font-medium px-2 text-center">通过语音为该计划生成任务</span>
                        <div class="relative">
                            <button class="add-button w-12 h-12 rounded-full flex items-center justify-center text-white shadow-md gradient-button transform active:scale-90" data-menu="add-menu-plan-details-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></button>
                            <div id="add-menu-plan-details-1" class="popup-menu absolute right-0 bottom-16 w-36 bg-white rounded-lg shadow-xl p-2 z-50 origin-bottom-right scale-95 opacity-0 pointer-events-none transition-all">
                               <a href="#" class="action-new-task flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">新建任务</a>
                           </div>
                        </div>
                    </div>
                </footer>
            </div>
            
            <div id="page-plan-details-2" class="page hidden-right">
                <header class="flex-shrink-0 px-4 pt-4 pb-3 flex items-center relative">
                    <button class="back-link p-2 -ml-2 text-gray-600 hover:bg-gray-100 rounded-full"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg></button>
                    <h1 class="text-xl font-bold text-center flex-grow" style="color: var(--text-primary);">计划详情</h1>
                    <div class="w-10">
                        <button id="plan-details-options-btn-2" class="options-button p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100" data-menu="plan-details-menu-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" /></svg>
                        </button>
                         <div id="plan-details-menu-2" class="popup-menu absolute right-4 top-14 w-36 bg-white rounded-lg shadow-xl p-2 z-50 origin-top-right scale-95 opacity-0 pointer-events-none transition-all">
                            <a href="#" class="share-plan-btn-details flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">分享计划</a>
                             <a href="#" class="shelve-plan-btn-details flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">搁置计划</a>
                            <div class="border-t my-1"></div>
                            <a href="#" class="delete-plan-btn-details flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md">删除计划</a>
                        </div>
                    </div>
                </header>
                <div class="flex-grow overflow-y-auto main-content pb-28">
                    <div class="px-6 mt-2">
                        <div class="p-5 rounded-2xl text-white gradient-button relative overflow-hidden">
                             <div class="absolute -right-8 -bottom-8 w-32 h-32 bg-white/10 rounded-full"></div>
                            <div class="relative z-10">
                                <h2 class="text-2xl font-bold">健身增肌计划</h2>
                                <p class="text-sm opacity-80 mt-1">汗水是最好的雕刻刀</p>
                                <div class="relative w-20 h-20 mx-auto my-4">
                                    <svg class="w-full h-full" viewBox="0 0 100 100"><circle cx="50" cy="50" r="45" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="8"></circle><circle cx="50" cy="50" r="45" fill="none" stroke="white" stroke-width="8" stroke-linecap="round" transform="rotate(-90 50 50)" stroke-dasharray="282.7" stroke-dashoffset="197.9"></circle></svg>
                                    <div class="absolute inset-0 flex items-center justify-center text-2xl font-bold">30%</div>
                                </div>
                                <div class="grid grid-cols-2 gap-x-4 text-sm text-center">
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" /></svg><span>总番茄: <span class="font-semibold">40</span></span></div>
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" /></svg><span>5/1 - 9/1</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-6 mt-6">
                        <h3 class="text-lg font-bold" style="color: var(--text-primary);">包含任务</h3>
                        <div class="mt-4 space-y-3 plan-details-task-list">
                            <div class="custom-card p-4 flex items-center plan-details-task-card border-l-red-500 nav-link cursor-pointer" data-target="page-task-details">
                                <div class="flex-grow pointer-events-none">
                                    <p class="font-semibold" style="color: var(--text-primary);">周一：胸部+三头训练</p>
                                    <div class="text-sm mt-1 space-x-3" style="color: var(--text-secondary);"><span>优先级: <span class="font-medium" style="color: var(--priority-high);">高</span></span><span>🍅 4 番茄</span></div>
                                </div>
                                <button class="task-delete-button text-gray-300 p-2 ml-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pointer-events-none" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd"></path></svg></button>
                            </div>
                            <div class="custom-card p-4 flex items-center plan-details-task-card border-l-red-500 nav-link cursor-pointer" data-target="page-task-details">
                                <div class="flex-grow pointer-events-none">
                                    <p class="font-semibold" style="color: var(--text-primary);">周三：背部+二头训练</p>
                                    <div class="text-sm mt-1 space-x-3" style="color: var(--text-secondary);"><span>优先级: <span class="font-medium" style="color: var(--priority-high);">高</span></span><span>🍅 4 番茄</span></div>
                                </div>
                                <button class="task-delete-button text-gray-300 p-2 ml-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pointer-events-none" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd"></path></svg></button>
                            </div>
                        </div>
                    </div>
                </div>
                 <footer class="absolute bottom-4 left-0 right-0 w-full max-w-[420px] mx-auto px-6 z-40">
                    <div class="bg-white/70 backdrop-blur-lg rounded-full flex items-center justify-between p-2 shadow-2xl shadow-gray-400/20">
                        <button class="w-12 h-12 rounded-full flex items-center justify-center text-white gradient-button transform active:scale-90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z"></path></svg></button>
                        <span class="text-sm text-gray-500 font-medium px-2 text-center">通过语音为该计划生成任务</span>
                        <div class="relative">
                            <button class="add-button w-12 h-12 rounded-full flex items-center justify-center text-white shadow-md gradient-button transform active:scale-90" data-menu="add-menu-plan-details-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></button>
                            <div id="add-menu-plan-details-2" class="popup-menu absolute right-0 bottom-16 w-36 bg-white rounded-lg shadow-xl p-2 z-50 origin-bottom-right scale-95 opacity-0 pointer-events-none transition-all">
                               <a href="#" class="action-new-task flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">新建任务</a>
                           </div>
                        </div>
                    </div>
                </footer>
            </div>

            <div id="page-plan-details-3" class="page hidden-right">
                 <header class="flex-shrink-0 px-4 pt-4 pb-3 flex items-center relative">
                    <button class="back-link p-2 -ml-2 text-gray-600 hover:bg-gray-100 rounded-full"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg></button>
                    <h1 class="text-xl font-bold text-center flex-grow" style="color: var(--text-primary);">计划详情</h1>
                    <div class="w-10">
                        <button id="plan-details-options-btn-3" class="options-button p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100" data-menu="plan-details-menu-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" /></svg>
                        </button>
                         <div id="plan-details-menu-3" class="popup-menu absolute right-4 top-14 w-36 bg-white rounded-lg shadow-xl p-2 z-50 origin-top-right scale-95 opacity-0 pointer-events-none transition-all">
                            <a href="#" class="share-plan-btn-details flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">分享计划</a>
                             <a href="#" class="shelve-plan-btn-details flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">搁置计划</a>
                            <div class="border-t my-1"></div>
                            <a href="#" class="delete-plan-btn-details flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md">删除计划</a>
                        </div>
                    </div>
                </header>
                <div class="flex-grow overflow-y-auto main-content pb-28">
                    <div class="px-6 mt-2">
                        <div class="p-5 rounded-2xl text-white gradient-button relative overflow-hidden">
                             <div class="absolute -right-8 -bottom-8 w-32 h-32 bg-white/10 rounded-full"></div>
                            <div class="relative z-10">
                                <h2 class="text-2xl font-bold">阅读名著100本</h2>
                                <p class="text-sm opacity-80 mt-1">开卷有益</p>
                                <div class="relative w-20 h-20 mx-auto my-4">
                                    <svg class="w-full h-full" viewBox="0 0 100 100"><circle cx="50" cy="50" r="45" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="8"></circle><circle cx="50" cy="50" r="45" fill="none" stroke="white" stroke-width="8" stroke-linecap="round" transform="rotate(-90 50 50)" stroke-dasharray="282.7" stroke-dashoffset="240.3"></circle></svg>
                                    <div class="absolute inset-0 flex items-center justify-center text-2xl font-bold">15%</div>
                                </div>
                                <div class="grid grid-cols-2 gap-x-4 text-sm text-center">
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" /></svg><span>总番茄: <span class="font-semibold">200</span></span></div>
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" /></svg><span>1/1 - 12/31</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-6 mt-6">
                        <h3 class="text-lg font-bold" style="color: var(--text-primary);">包含任务</h3>
                         <div class="mt-4 space-y-3 plan-details-task-list">
                             <div class="custom-card p-4 flex items-center plan-details-task-card border-l-green-500 nav-link cursor-pointer" data-target="page-task-details">
                                <div class="flex-grow pointer-events-none">
                                    <p class="font-semibold" style="color: var(--text-primary);">读《百年孤独》</p>
                                    <div class="text-sm mt-1 space-x-3" style="color: var(--text-secondary);"><span>优先级: <span class="font-medium" style="color: var(--priority-low);">低</span></span><span>🍅 10 番茄</span></div>
                                </div>
                                <button class="task-delete-button text-gray-300 p-2 ml-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pointer-events-none" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd"></path></svg></button>
                            </div>
                        </div>
                    </div>
                </div>
                 <footer class="absolute bottom-4 left-0 right-0 w-full max-w-[420px] mx-auto px-6 z-40">
                    <div class="bg-white/70 backdrop-blur-lg rounded-full flex items-center justify-between p-2 shadow-2xl shadow-gray-400/20">
                        <button class="w-12 h-12 rounded-full flex items-center justify-center text-white gradient-button transform active:scale-90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z"></path></svg></button>
                        <span class="text-sm text-gray-500 font-medium px-2 text-center">通过语音为该计划生成任务</span>
                        <div class="relative">
                            <button class="add-button w-12 h-12 rounded-full flex items-center justify-center text-white shadow-md gradient-button transform active:scale-90" data-menu="add-menu-plan-details-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></button>
                            <div id="add-menu-plan-details-3" class="popup-menu absolute right-0 bottom-16 w-36 bg-white rounded-lg shadow-xl p-2 z-50 origin-bottom-right scale-95 opacity-0 pointer-events-none transition-all">
                               <a href="#" class="action-new-task flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">新建任务</a>
                           </div>
                        </div>
                    </div>
                </footer>
            </div>

            <div id="page-plan-details-4" class="page hidden-right">
                 <header class="flex-shrink-0 px-4 pt-4 pb-3 flex items-center relative">
                    <button class="back-link p-2 -ml-2 text-gray-600 hover:bg-gray-100 rounded-full"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg></button>
                    <h1 class="text-xl font-bold text-center flex-grow" style="color: var(--text-primary);">搁置的计划</h1>
                    <div class="w-10">
                        <button id="plan-details-options-btn-4" class="options-button p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100" data-menu="plan-details-menu-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" /></svg>
                        </button>
                         <div id="plan-details-menu-4" class="popup-menu absolute right-4 top-14 w-40 bg-white rounded-lg shadow-xl p-2 z-50 origin-top-right scale-95 opacity-0 pointer-events-none transition-all">
                            <a href="#" class="reactivate-plan-btn-details flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">重新激活计划</a>
                            <div class="border-t my-1"></div>
                            <a href="#" class="delete-plan-btn-details flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md">删除计划</a>
                        </div>
                    </div>
                </header>
                <div class="flex-grow overflow-y-auto main-content pb-28">
                    <div class="px-6 mt-2">
                        <div class="p-5 rounded-2xl bg-gray-400 text-white relative overflow-hidden">
                             <div class="absolute -right-8 -bottom-8 w-32 h-32 bg-white/10 rounded-full"></div>
                            <div class="relative z-10">
                                <h2 class="text-2xl font-bold">Side Project X</h2>
                                <p class="text-sm opacity-80 mt-1">一个搁置的副业项目</p>
                                <div class="relative w-20 h-20 mx-auto my-4">
                                    <svg class="w-full h-full" viewBox="0 0 100 100"><circle cx="50" cy="50" r="45" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="8"></circle><circle cx="50" cy="50" r="45" fill="none" stroke="white" stroke-width="8" stroke-linecap="round" transform="rotate(-90 50 50)" stroke-dasharray="282.7" stroke-dashoffset="169.6"></circle></svg>
                                    <div class="absolute inset-0 flex items-center justify-center text-2xl font-bold">40%</div>
                                </div>
                                <div class="grid grid-cols-2 gap-x-4 text-sm text-center">
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" /></svg><span>总番茄: <span class="font-semibold">50</span></span></div>
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" /></svg><span>长期</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-6 mt-6">
                        <h3 class="text-lg font-bold" style="color: var(--text-primary);">包含任务</h3>
                         <div class="mt-4 space-y-3 plan-details-task-list">
                             <div class="custom-card p-4 flex items-center plan-details-task-card border-l-gray-400 opacity-70">
                                <div class="flex-grow pointer-events-none">
                                    <p class="font-semibold" style="color: var(--text-primary);">市场调研</p>
                                    <div class="text-sm mt-1 space-x-3" style="color: var(--text-secondary);"><span>优先级: <span class="font-medium" style="color: var(--priority-medium);">中</span></span><span>🍅 10 番茄</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="page-plan-details-completed-cpa" class="page hidden-right">
                <header class="flex-shrink-0 px-4 pt-4 pb-3 flex items-center relative">
                    <button class="back-link p-2 -ml-2 text-gray-600 hover:bg-gray-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg>
                    </button>
                    <h1 class="text-xl font-bold text-center flex-grow" style="color: var(--text-primary);">已完成的计划</h1>
                    <div class="w-10">
                         <button class="options-button p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100" data-menu="plan-details-menu-completed-cpa">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" /></svg>
                        </button>
                         <div id="plan-details-menu-completed-cpa" class="popup-menu absolute right-4 top-14 w-36 bg-white rounded-lg shadow-xl p-2 z-50 origin-top-right scale-95 opacity-0 pointer-events-none transition-all">
                            <a href="#" class="share-plan-btn-details flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">分享计划</a>
                            <a href="#" class="delete-plan-btn-details flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mt-1">删除计划</a>
                        </div>
                    </div>
                </header>
                <div class="flex-grow overflow-y-auto main-content pb-28">
                    <div class="px-6 mt-2">
                        <div class="p-5 rounded-2xl text-white gradient-button relative overflow-hidden">
                            <div class="absolute -right-8 -bottom-8 w-32 h-32 bg-white/10 rounded-full"></div>
                            <div class="relative z-10">
                                <h2 class="text-2xl font-bold">CPA经济法通关</h2>
                                <p class="text-sm opacity-80 mt-1">功不唐捐，玉汝于成</p>
                                <div class="relative w-20 h-20 mx-auto my-4">
                                    <svg class="w-full h-full" viewBox="0 0 100 100"><circle cx="50" cy="50" r="45" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="8"></circle><circle cx="50" cy="50" r="45" fill="none" stroke="white" stroke-width="8" stroke-linecap="round" transform="rotate(-90 50 50)" stroke-dasharray="282.7" stroke-dashoffset="0"></circle></svg>
                                    <div class="absolute inset-0 flex flex-col items-center justify-center">
                                        <div class="text-xl font-bold">100%</div>
                                        <div class="text-xs font-semibold mt-1 opacity-80">已完成</div>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-x-4 text-sm text-center">
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" /></svg><span>总番茄: <span class="font-semibold">58</span></span></div>
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" /></svg><span>8/1 - 12/15</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-6 mt-6">
                        <h3 class="text-lg font-bold" style="color: var(--text-primary);">包含任务</h3>
                        <div id="completed-cpa-task-list" class="mt-4 space-y-3 plan-details-task-list">
                            <div class="custom-card p-4 flex items-center plan-details-task-card border-l-gray-400">
                                <div class="selection-checkbox hidden items-center mr-4">
                                    <input type="checkbox" class="h-5 w-5 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 cursor-pointer">
                                </div>
                                <div class="flex-grow">
                                    <p class="font-semibold text-strikethrough" style="color: var(--text-primary);">经济法基础理论</p>
                                    <div class="text-sm mt-1 space-x-3" style="color: var(--text-secondary);"><span>优先级: <span class="font-medium" style="color: var(--priority-high);">高</span></span><span>🍅 10 番茄</span></div>
                                </div>
                                <button class="task-delete-button text-gray-300 p-2 ml-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd"></path></svg></button>
                            </div>
                            <div class="custom-card p-4 flex items-center plan-details-task-card border-l-gray-400">
                                <div class="selection-checkbox hidden items-center mr-4">
                                    <input type="checkbox" class="h-5 w-5 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 cursor-pointer">
                                </div>
                                <div class="flex-grow">
                                    <p class="font-semibold text-strikethrough" style="color: var(--text-primary);">公司法律制度</p>
                                    <div class="text-sm mt-1 space-x-3" style="color: var(--text-secondary);"><span>优先级: <span class="font-medium" style="color: var(--priority-medium);">中</span></span><span>🍅 8 番茄</span></div>
                                </div>
                                <button class="task-delete-button text-gray-300 p-2 ml-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V8z" clip-rule="evenodd"></path></svg></button>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="selection-confirm-bar-cpa" class="hidden absolute bottom-0 left-0 right-0 w-full max-w-[420px] mx-auto p-4 z-40 bg-white/80 backdrop-blur-md shadow-2xl">
                    <div class="flex space-x-3">
                        <button id="cancel-selection-btn-cpa" class="flex-1 py-3 border border-gray-300 rounded-xl font-semibold text-gray-700 hover:bg-gray-100 transition-colors">取消</button>
                        <button class="flex-1 py-3 text-white rounded-xl font-semibold gradient-button">重启选中任务</button>
                    </div>
                </div>
                <footer class="absolute bottom-4 left-0 right-0 w-full max-w-[420px] mx-auto px-6 z-40">
                    <div class="bg-white/70 backdrop-blur-lg rounded-full flex items-center justify-between p-2 shadow-2xl shadow-gray-400/20">
                        <button class="w-12 h-12 rounded-full flex items-center justify-center text-white gradient-button transform active:scale-90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z"></path></svg></button>
                        <span class="text-sm text-gray-500 font-medium px-2 text-center">重新激活或添加新任务</span>
                        <div class="relative">
                            <button id="add-btn-completed-plan-cpa" class="w-12 h-12 rounded-full flex items-center justify-center text-white shadow-md gradient-button transform active:scale-90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></button>
                        </div>
                    </div>
                </footer>
            </div>
            
            <div id="page-plan-details-completed-ui" class="page hidden-right">
                <header class="flex-shrink-0 px-4 pt-4 pb-3 flex items-center relative">
                    <button class="back-link p-2 -ml-2 text-gray-600 hover:bg-gray-100 rounded-full"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg></button>
                    <h1 class="text-xl font-bold text-center flex-grow" style="color: var(--text-primary);">已完成的计划</h1>
                    <div class="w-10"></div>
                </header>
                <div class="flex-grow overflow-y-auto main-content pb-28">
                    <div class="px-6 mt-2">
                        <div class="p-5 rounded-2xl text-white gradient-button relative overflow-hidden">
                            <div class="absolute -right-8 -bottom-8 w-32 h-32 bg-white/10 rounded-full"></div>
                            <div class="relative z-10">
                                <h2 class="text-2xl font-bold">完成UI设计原型</h2>
                                <p class="text-sm opacity-80 mt-1">像素之美，匠心之作</p>
                                <div class="relative w-20 h-20 mx-auto my-4">
                                    <svg class="w-full h-full" viewBox="0 0 100 100"><circle cx="50" cy="50" r="45" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="8"></circle><circle cx="50" cy="50" r="45" fill="none" stroke="white" stroke-width="8" stroke-linecap="round" transform="rotate(-90 50 50)" stroke-dasharray="282.7" stroke-dashoffset="0"></circle></svg>
                                    <div class="absolute inset-0 flex flex-col items-center justify-center">
                                        <div class="text-xl font-bold">100%</div>
                                        <div class="text-xs font-semibold mt-1 opacity-80">已完成</div>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-x-4 text-sm text-center">
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" /></svg><span>总番茄: <span class="font-semibold">30</span></span></div>
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" /></svg><span>9/1 - 10/15</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-6 mt-6">
                        <h3 class="text-lg font-bold" style="color: var(--text-primary);">包含任务</h3>
                        <div id="completed-ui-task-list" class="mt-4 space-y-3 plan-details-task-list">
                            <div class="custom-card p-4 flex items-center plan-details-task-card border-l-gray-400">
                                <div class="selection-checkbox hidden items-center mr-4"><input type="checkbox" class="h-5 w-5 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 cursor-pointer"></div>
                                <div class="flex-grow">
                                    <p class="font-semibold text-strikethrough" style="color: var(--text-primary);">高保真原型绘制</p>
                                    <div class="text-sm mt-1 space-x-3" style="color: var(--text-secondary);"><span>优先级: <span class="font-medium" style="color: var(--priority-high);">高</span></span><span>🍅 15 番茄</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="selection-confirm-bar-ui" class="hidden absolute bottom-0 left-0 right-0 w-full max-w-[420px] mx-auto p-4 z-40 bg-white/80 backdrop-blur-md shadow-2xl">
                    <div class="flex space-x-3"><button id="cancel-selection-btn-ui" class="flex-1 py-3 border border-gray-300 rounded-xl font-semibold text-gray-700 hover:bg-gray-100 transition-colors">取消</button><button class="flex-1 py-3 text-white rounded-xl font-semibold gradient-button">重启选中任务</button></div>
                </div>
                <footer class="absolute bottom-4 left-0 right-0 w-full max-w-[420px] mx-auto px-6 z-40">
                    <div class="bg-white/70 backdrop-blur-lg rounded-full flex items-center justify-between p-2 shadow-2xl shadow-gray-400/20">
                        <button class="w-12 h-12 rounded-full flex items-center justify-center text-white gradient-button transform active:scale-90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z"></path></svg></button>
                        <span class="text-sm text-gray-500 font-medium px-2 text-center">重新激活或添加新任务</span>
                        <div class="relative">
                            <button id="add-btn-completed-plan-ui" class="w-12 h-12 rounded-full flex items-center justify-center text-white shadow-md gradient-button transform active:scale-90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></button>
                        </div>
                    </div>
                </footer>
            </div>

            <div id="page-plan-details-completed-react" class="page hidden-right">
                <header class="flex-shrink-0 px-4 pt-4 pb-3 flex items-center relative">
                    <button class="back-link p-2 -ml-2 text-gray-600 hover:bg-gray-100 rounded-full"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg></button>
                    <h1 class="text-xl font-bold text-center flex-grow" style="color: var(--text-primary);">已完成的计划</h1>
                    <div class="w-10"></div>
                </header>
                <div class="flex-grow overflow-y-auto main-content pb-28">
                    <div class="px-6 mt-2">
                        <div class="p-5 rounded-2xl text-white gradient-button relative overflow-hidden">
                            <div class="absolute -right-8 -bottom-8 w-32 h-32 bg-white/10 rounded-full"></div>
                            <div class="relative z-10">
                                <h2 class="text-2xl font-bold">学习React基础</h2>
                                <p class="text-sm opacity-80 mt-1">构建未来的用户界面</p>
                                <div class="relative w-20 h-20 mx-auto my-4">
                                    <svg class="w-full h-full" viewBox="0 0 100 100"><circle cx="50" cy="50" r="45" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="8"></circle><circle cx="50" cy="50" r="45" fill="none" stroke="white" stroke-width="8" stroke-linecap="round" transform="rotate(-90 50 50)" stroke-dasharray="282.7" stroke-dashoffset="0"></circle></svg>
                                    <div class="absolute inset-0 flex flex-col items-center justify-center">
                                        <div class="text-xl font-bold">100%</div>
                                        <div class="text-xs font-semibold mt-1 opacity-80">已完成</div>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-x-4 text-sm text-center">
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" /></svg><span>总番茄: <span class="font-semibold">25</span></span></div>
                                    <div class="flex items-center justify-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 opacity-80" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" /></svg><span>10/1 - 11/05</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-6 mt-6">
                        <h3 class="text-lg font-bold" style="color: var(--text-primary);">包含任务</h3>
                        <div id="completed-react-task-list" class="mt-4 space-y-3 plan-details-task-list">
                             <div class="custom-card p-4 flex items-center plan-details-task-card border-l-gray-400">
                                <div class="selection-checkbox hidden items-center mr-4"><input type="checkbox" class="h-5 w-5 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 cursor-pointer"></div>
                                <div class="flex-grow">
                                    <p class="font-semibold text-strikethrough" style="color: var(--text-primary);">学习State与Props</p>
                                    <div class="text-sm mt-1 space-x-3" style="color: var(--text-secondary);"><span>优先级: <span class="font-medium" style="color: var(--priority-medium);">中</span></span><span>🍅 10 番茄</span></div>
                                </div>
                            </div>
                            <div class="custom-card p-4 flex items-center plan-details-task-card border-l-gray-400">
                                <div class="selection-checkbox hidden items-center mr-4"><input type="checkbox" class="h-5 w-5 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 cursor-pointer"></div>
                                <div class="flex-grow">
                                    <p class="font-semibold text-strikethrough" style="color: var(--text-primary);">掌握React Hooks</p>
                                    <div class="text-sm mt-1 space-x-3" style="color: var(--text-secondary);"><span>优先级: <span class="font-medium" style="color: var(--priority-high);">高</span></span><span>🍅 15 番茄</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                 <div id="selection-confirm-bar-react" class="hidden absolute bottom-0 left-0 right-0 w-full max-w-[420px] mx-auto p-4 z-40 bg-white/80 backdrop-blur-md shadow-2xl">
                    <div class="flex space-x-3"><button id="cancel-selection-btn-react" class="flex-1 py-3 border border-gray-300 rounded-xl font-semibold text-gray-700 hover:bg-gray-100 transition-colors">取消</button><button class="flex-1 py-3 text-white rounded-xl font-semibold gradient-button">重启选中任务</button></div>
                </div>
                <footer class="absolute bottom-4 left-0 right-0 w-full max-w-[420px] mx-auto px-6 z-40">
                    <div class="bg-white/70 backdrop-blur-lg rounded-full flex items-center justify-between p-2 shadow-2xl shadow-gray-400/20">
                        <button class="w-12 h-12 rounded-full flex items-center justify-center text-white gradient-button transform active:scale-90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z"></path></svg></button>
                        <span class="text-sm text-gray-500 font-medium px-2 text-center">重新激活或添加新任务</span>
                        <div class="relative">
                            <button id="add-btn-completed-plan-react" class="w-12 h-12 rounded-full flex items-center justify-center text-white shadow-md gradient-button transform active:scale-90"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></button>
                        </div>
                    </div>
                </footer>
            </div>
            
            <div id="page-task-details" class="page hidden-right bg-white">
                <header class="flex-shrink-0 px-4 pt-4 pb-3 flex items-center relative">
                    <button class="back-link p-2 -ml-2 text-gray-600 hover:bg-gray-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg>
                    </button>
                    <h1 class="text-xl font-bold text-center flex-grow" style="color: var(--text-primary);">任务详情</h1>
                    <div class="w-10">
                        </div>
                </header>
                <div class="flex-grow overflow-y-auto main-content pb-28">
                    <div class="px-6 mt-4">
                        <h2 id="task-details-title" class="text-2xl font-bold" style="color: var(--text-primary);">学习考研英语</h2>
                        <p class="text-sm mt-1" style="color: var(--text-secondary);">今日: 09:00 - 11:00</p>
                    </div>

                    <div class="px-6 mt-6">
                        <div class="custom-card px-2 py-1">
                            <div class="divide-y divide-gray-100">
                                <a href="#" class="flex items-center justify-between p-4 hover:bg-gray-50/50 rounded-lg transition-colors">
                                    <div class="flex items-center space-x-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" /></svg>
                                        <span class="text-sm font-semibold" style="color: var(--text-primary);">所属计划</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span id="task-details-plan" class="text-sm font-medium" style="color: var(--text-secondary);">考研冲刺</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" /></svg>
                                    </div>
                                </a>
                                <a href="#" class="flex items-center justify-between p-4 hover:bg-gray-50/50 rounded-lg transition-colors">
                                    <div class="flex items-center space-x-4">
                                        <span class="text-2xl">🍅</span>
                                        <span class="text-sm font-semibold" style="color: var(--text-primary);">番茄钟</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span id="task-details-pomo" class="text-sm font-medium" style="color: var(--text-secondary);">4 个</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" /></svg>
                                    </div>
                                </a>
                                <a href="#" class="flex items-center justify-between p-4 hover:bg-gray-50/50 rounded-lg transition-colors">
                                    <div class="flex items-center space-x-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6H8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9" /></svg>
                                        <span class="text-sm font-semibold" style="color: var(--text-primary);">优先级</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span id="task-details-priority" class="text-sm font-medium" style="color: var(--priority-high);">高</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" /></svg>
                                    </div>
                                </a>
                                <div class="flex items-center justify-between p-4">
                                    <div class="flex items-center space-x-4">
                                         <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" /></svg>
                                        <span class="text-sm font-semibold" style="color: var(--text-primary);">任务提醒</span>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-6 mt-6">
                        <div class="custom-card p-4">
                            <h3 class="text-sm font-semibold mb-3" style="color: var(--text-primary);">子任务 (0/2)</h3>
                            <ul id="subtasks-list" class="space-y-1">
                                <li class="subtask-item">
                                    <input type="checkbox" id="subtask-1" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                    <label for="subtask-1" class="flex-grow text-sm text-gray-800">完成第一单元单词背诵</label>
                                </li>
                                <li class="subtask-item completed">
                                    <input type="checkbox" id="subtask-2" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" checked>
                                    <label for="subtask-2" class="flex-grow text-sm text-gray-800">做一套模拟题</label>
                                </li>
                            </ul>
                            <div class="mt-4 flex items-center space-x-2">
                                <input type="text" id="new-subtask-input" placeholder="添加新的子任务..." class="flex-grow w-full text-sm p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none">
                                <button id="add-subtask-btn" class="p-2 rounded-lg gradient-button text-white">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" /></svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    </div>
                <footer class="absolute bottom-6 left-0 right-0 w-full max-w-[420px] mx-auto px-6 z-40">
                    <a href="#" id="task-details-start-btn" class="nav-link" data-target="page-clock">
                        <button class="w-full text-white font-bold py-4 rounded-2xl gradient-button text-lg flex items-center justify-center space-x-2 active:scale-95 transition-transform duration-150">
                             <svg class="w-7 h-7" viewBox="0 0 24 24" fill="currentColor"><path d="M8 5v14l11-7z"></path></svg>
                             <span>开始专注</span>
                        </button>
                    </a>
                </footer>
            </div>
            <div id="page-review" class="page hidden-right bg-white">
                <header class="flex-shrink-0 px-4 pt-4 pb-3 flex items-center"><button class="back-link p-2 -ml-2 text-gray-600 hover:bg-gray-100 rounded-full"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg></button><h1 class="text-xl font-bold text-center flex-grow" style="color: var(--text-primary);">复盘总结</h1><div class="w-10"></div></header>
                <div class="flex-grow overflow-y-auto main-content pb-6">
                    <div class="px-6 mt-4 grid grid-cols-3 gap-4"><div class="custom-card p-3 rounded-2xl text-center"><p class="text-sm" style="color: var(--text-secondary);">今日学习</p><p class="text-2xl font-bold mt-1.5" style="color: var(--text-primary);">3.5<span class="text-base font-medium">小时</span></p></div><div class="custom-card p-3 rounded-2xl text-center"><p class="text-sm" style="color: var(--text-secondary);">任务完成</p><p class="text-2xl font-bold mt-1.5" style="color: var(--text-primary);">5<span class="text-base font-medium">个</span></p></div><div class="custom-card p-3 rounded-2xl text-center"><p class="text-sm" style="color: var(--text-secondary);">完成度</p><p class="text-2xl font-bold mt-1.5" style="color: var(--text-primary);">100<span class="text-base font-medium">%</span></p></div></div>
                    <div class="px-6 mt-8">
    <div class="custom-card p-4 flex justify-between items-center cursor-pointer hover:shadow-lg">
        <span class="font-medium text-gray-700">当前复盘范围: <span class="font-bold gradient-text">全部计划</span></span>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7"></path>
        </svg>
    </div>
</div>
                    <div class="px-6 mt-8"><div class="flex justify-between items-center"><h2 class="text-lg font-bold" style="color: var(--text-primary);">学习时长分析</h2><div class="flex items-center space-x-1 bg-gray-100 p-1 rounded-full"><button id="btn-week" class="px-4 py-1 text-sm font-semibold rounded-full bg-white shadow-sm" style="color:var(--accent-start)">周</button><button id="btn-month" class="px-4 py-1 text-sm font-semibold rounded-full" style="color: var(--text-secondary);">月</button></div></div><div class="mt-4 custom-card p-4"><div class="flex h-40 space-x-2"><div class="flex flex-col-reverse justify-between text-xs text-right" style="color: var(--text-secondary);"><span>0h</span><span>2h</span><span>4h</span><span>6h</span></div><div class="flex-grow flex justify-around items-end border-l border-gray-100 pl-2"><div class="w-3/5 rounded-t-lg chart-bar gradient-button" style="height: 30%;"></div><div class="w-3/5 rounded-t-lg chart-bar gradient-button" style="height: 50%;"></div><div class="w-3/5 rounded-t-lg chart-bar gradient-button" style="height: 75%;"></div><div class="w-3/5 rounded-t-lg chart-bar gradient-button" style="height: 35%;"></div><div class="w-3/5 rounded-t-lg chart-bar gradient-button" style="height: 55%;"></div><div class="w-3/5 rounded-t-lg chart-bar gradient-button" style="height: 85%;"></div><div class="w-3/5 rounded-t-lg chart-bar gradient-button" style="height: 20%;"></div></div></div><div class="flex justify-around items-end mt-2 pl-8"><span class="text-xs" style="color: var(--text-secondary);">一</span><span class="text-xs" style="color: var(--text-secondary);">二</span><span class="text-xs font-bold" style="color: var(--text-primary);">三</span><span class="text-xs" style="color: var(--text-secondary);">四</span><span class="text-xs" style="color: var(--text-secondary);">五</span><span class="text-xs" style="color: var(--text-secondary);">六</span><span class="text-xs" style="color: var(--text-secondary);">日</span></div></div></div>
                    <div class="px-6 mt-8 mb-6"><h2 class="text-lg font-bold" style="color: var(--text-primary);">成就墙</h2><div class="mt-4 grid grid-cols-3 gap-4"><div class="border-2 border-yellow-300 bg-yellow-50/80 p-3 rounded-2xl flex flex-col items-center text-center cursor-pointer hover:shadow-lg hover:border-yellow-400 transition-all"><div class="w-14 h-14 bg-yellow-400 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" /></svg></div><span class="mt-2 font-bold text-sm" style="color: var(--text-primary);">CPA经济法</span><span class="text-xs mt-1" style="color: var(--text-secondary);">2024/12/15</span></div><div class="border-2 border-blue-300 bg-blue-50/80 p-3 rounded-2xl flex flex-col items-center text-center cursor-pointer hover:shadow-lg hover:border-blue-400 transition-all"><div class="w-14 h-14 bg-blue-500 rounded-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" viewBox="0 0 20 20" fill="currentColor"><path d="M10.394 2.08a1 1 0 00-1.08.022L1.577 7.437a1 1 0 00-.577.923V14.5a1 1 0 001 1h16a1 1 0 001-1V8.36a1 1 0 00-.577-.923L11.475 2.102a1 1 0 00-1.08-.022zM10 12.25a2.25 2.25 0 100-4.5 2.25 2.25 0 000 4.5z" /><path fill-rule="evenodd" d="M10 18a1 1 0 001-1v-2.058l4-2.29V14a1 1 0 002 0v-4.5a1 1 0 00-.577-.923l-6-3.429a1 1 0 00-1.08.022L2.577 9.577A1 1 0 002 10.5V14a1 1 0 102 0v-1.942l4 2.29V17a1 1 0 001 1z" clip-rule="evenodd" /></svg></div><span class="mt-2 font-bold text-sm" style="color: var(--text-primary);">React学习</span><span class="text-xs mt-1" style="color: var(--text-secondary);">2024/10/20</span></div><div class="border-2 border-dashed border-gray-300 bg-gray-50/80 p-3 rounded-2xl flex flex-col items-center text-center cursor-pointer hover:shadow-lg hover:border-gray-400 transition-all"><div class="w-14 h-14 bg-gray-200 rounded-full flex items-center justify-center"><span class="text-3xl font-bold text-gray-400">?</span></div><span class="mt-2 font-bold text-sm" style="color: var(--text-primary);">下一个目标</span><span class="text-xs mt-1" style="color: var(--text-secondary);">进行中</span></div></div></div>
                </div>
            </div>

            <div id="page-calendar" class="page hidden-right bg-white">
                <div class="flex-shrink-0 px-4 pt-4 pb-3 flex items-center z-40 relative">
                    <button class="back-link p-2 -ml-2 text-gray-600 hover:bg-gray-100 rounded-full"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" /></svg></button>
                    <h1 class="text-xl font-bold text-center flex-grow" style="color: var(--text-primary);">日历</h1>
                    <div class="w-10"></div>
                </div>
                <div class="flex-grow overflow-y-auto main-content pb-20 relative">
                    <div class="px-5 mt-4"><div class="custom-card p-4"><div><div class="flex justify-between items-center mb-4"><button id="prev-month-btn" class="p-2 rounded-full hover:bg-gray-100"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg></button><div class="text-center"><h2 id="calendar-month" class="text-indigo-400 font-bold text-xl">5月</h2><p id="calendar-year" class="text-sm text-gray-400">2025年</p></div><button id="next-month-btn" class="p-2 rounded-full hover:bg-gray-100"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" /></svg></button></div><div class="grid grid-cols-7 text-center text-xs text-indigo-400 font-semibold mb-2"><span>日</span><span>一</span><span>二</span><span>三</span><span>四</span><span>五</span><span>六</span></div><div id="calendar-grid" class="grid grid-cols-7 gap-y-2 text-center"></div></div></div></div>
                </div>
                <div class="absolute bottom-6 right-6 z-50">
                    <div class="relative">
                        <button id="calendar-add-btn" class="add-button w-14 h-14 rounded-full flex items-center justify-center text-white shadow-lg gradient-button transform active:scale-90 transition-transform" data-menu="calendar-add-menu"><svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></button>
                        <div id="calendar-add-menu" class="popup-menu absolute right-0 bottom-16 w-36 bg-white rounded-lg shadow-xl p-2 origin-bottom-right scale-95 opacity-0 pointer-events-none transition-all">
                            <a href="#" class="action-new-reminder flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">新建提醒</a>
                            <a href="#" class="action-new-task flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">新建任务</a>
                            <a href="#" class="action-new-plan flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mt-1">新建计划</a>
                        </div>
                    </div>
                </div>
            </div>

            <div id="page-profile" class="page hidden-right bg-white">
                <header class="flex-shrink-0 px-4 pt-4 pb-2 flex items-center"><button class="back-link p-2 -ml-2 text-gray-600 hover:bg-gray-100 rounded-full"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg></button></header>
                <div class="flex-grow overflow-y-auto main-content pt-2">
                    <div class="px-6 mt-4">
                        <div class="custom-card p-4 flex items-center">
                            <img class="w-16 h-16 rounded-full object-cover border-2 border-white" src="https://images.unsplash.com/photo-1527980965255-d3b416303d12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=880&q=80" alt="User Avatar">
                            <div class="ml-4 flex-grow">
                                <h2 class="text-xl font-bold" style="color: var(--text-primary);">孙博为</h2>
                                <p class="text-sm mt-1" style="color: var(--text-secondary);">ID: PLAN78935</p>
                                <div class="flex items-center text-sm mt-1" style="color: var(--text-secondary);">
                                     <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-orange-400" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm11.707 3.707a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l5-5z" clip-rule="evenodd" /></svg>
                                    <span>已坚持打卡 128 天</span>
                                </div>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600 self-start mt-1"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" /></svg></button>
                        </div>
                    </div>
                    <div class="px-6 mt-6 grid grid-cols-2 gap-4">
                    <div id="open-xueba-modal-btn" class="custom-card p-4 flex flex-col items-center justify-center space-y-2 cursor-pointer hover:scale-105 transition-transform">
                        <div class="w-12 h-12 rounded-full flex items-center justify-center gradient-button">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <div class="text-center">
                            <span class="text-sm font-semibold" style="color: var(--text-primary);">学霸模式</span>
                            <p class="text-xs text-gray-400 mt-1">专注时屏蔽打扰</p>
                        </div>
                    </div>
                    <div class="custom-card p-4 flex flex-col items-center justify-center space-y-2 cursor-pointer hover:scale-105 transition-transform">
                        <div class="w-12 h-12 rounded-full flex items-center justify-center gradient-button"><svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0l2-2m-2 2l-2-2"></path></svg></div>
                        <span class="text-sm font-semibold" style="color: var(--text-primary);">官方模板</span>
                    </div>
                    </div>
                    <div class="px-6 mt-8">
                        <div class="custom-card px-2 py-1">
                            <div class="divide-y divide-gray-100">
                                <a href="#" class="flex items-center justify-between p-4 hover:bg-gray-50/50 rounded-lg transition-colors"><div class="flex items-center space-x-4"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg><span class="text-sm font-semibold" style="color: var(--text-primary);">通用设置</span></div><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" /></svg></a>
                                <a href="#" class="flex items-center justify-between p-4 hover:bg-gray-50/50 rounded-lg transition-colors"><div class="flex items-center space-x-4"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /></svg><span class="text-sm font-semibold" style="color: var(--text-primary);">回收站</span></div><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" /></svg></a>
                                <a href="#" class="flex items-center justify-between p-4 hover:bg-gray-50/50 rounded-lg transition-colors"><div class="flex items-center space-x-4"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg><span class="text-sm font-semibold" style="color: var(--text-primary);">关于我们</span></div><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" /></svg></a>
                            </div>
                        </div>
                    </div>
                    <div class="px-6 mt-8 mb-6">
                        <button class="w-full bg-red-50 text-red-600 font-bold py-3 rounded-xl hover:bg-red-100 transition-colors duration-200">退出登录</button>
                    </div>
                </div>
            </div>

            <div id="page-scan" class="page hidden-right flex-col bg-black text-white p-4">
                <header class="flex-shrink-0 w-full"><div class="flex justify-between items-center"><h2 class="text-lg font-semibold">扫描二维码</h2><button class="back-link text-3xl font-light text-gray-300 hover:text-white">&times;</button></div></header>
                <div class="flex-grow flex items-center justify-center"><div class="w-64 h-64 relative"><div class="absolute top-0 left-0 w-8 h-8 border-t-4 border-l-4 border-indigo-400 rounded-tl-lg"></div><div class="absolute top-0 right-0 w-8 h-8 border-t-4 border-r-4 border-indigo-400 rounded-tr-lg"></div><div class="absolute bottom-0 left-0 w-8 h-8 border-b-4 border-l-4 border-indigo-400 rounded-bl-lg"></div><div class="absolute bottom-0 right-0 w-8 h-8 border-b-4 border-r-4 border-indigo-400 rounded-br-lg"></div><div class="absolute top-1/2 left-4 right-4 h-px bg-indigo-400/50 animate-scan"></div></div></div>
                <p class="text-center text-gray-400 pb-8">将二维码/条形码放入框内，即可自动扫描</p>
            </div>

            <div id="page-clock" class="page hidden-right bg-white">
                <header class="flex-shrink-0 px-4 pt-3 pb-2"><div class="flex justify-between items-center text-xs font-bold text-gray-800"><span>12:00</span><div class="flex items-center space-x-1"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12.55a11 11 0 0 1 14.08 0"></path><path d="M1.42 9a16 16 0 0 1 21.16 0"></path><path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path><path d="M12 20h.01"></path></svg><svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor"><path d="M18.667 6.002a2.333 2.333 0 0 1 2.333 2.333v7.333a2.333 2.333 0 0 1-2.333-2.333H5.333a2.333 2.333 0 0 1-2.333-2.333V8.335a2.333 2.333 0 0 1 2.333-2.333h13.334zM22.167 8.335a1.167 1.167 0 0 0-1.167-1.167h-.833v9.667h.833a1.167 1.167 0 0 0 1.167-1.167V8.335z"></path></svg></div></div></header>
                <div class="flex-grow overflow-y-auto main-content">
                    <div class="px-5 pt-3 flex justify-between items-center"><button class="back-link p-2 -ml-2 text-gray-600"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg></button><div class="text-center"><h1 class="text-lg font-bold text-gray-800">专注中</h1></div><div class="w-10 h-10"></div></div>
                    <div class="px-5 mt-8"><div class="custom-card p-6"><div class="relative w-48 h-48 mx-auto"><svg class="w-full h-full" viewBox="0 0 200 200"><circle cx="100" cy="100" r="90" fill="none" stroke="#eef2ff" stroke-width="12"></circle><circle id="timer-progress" cx="100" cy="100" r="90" fill="none" stroke="url(#gradient)" stroke-width="12" stroke-linecap="round" class="timer-circle-progress" transform="rotate(-90 100 100)"></circle><defs><linearGradient id="gradient"><stop offset="0%" stop-color="var(--accent-start)" /><stop offset="100%" stop-color="var(--accent-end)" /></linearGradient></defs></svg><div class="absolute inset-0 flex flex-col items-center justify-center"><span id="time-display" class="text-5xl font-bold text-gray-800 tracking-wider">25:00</span><span id="status-display" class="text-sm text-gray-400 mt-2">工作进行中</span></div></div><div class="flex justify-center items-center space-x-8 mt-8"><button id="reset-btn" class="w-14 h-14 rounded-full flex items-center justify-center bg-gray-200/70 text-gray-600 shadow-sm hover:bg-gray-300/80 transition-all duration-150"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/></svg></button><button id="play-pause-btn" class="w-20 h-20 rounded-full flex items-center justify-center text-white shadow-lg shadow-indigo-500/30 active:scale-95 transition-all duration-150 gradient-button"><svg id="play-icon" xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 ml-1 hidden" viewBox="0 0 20 20" fill="currentColor"><path d="M4 3a1 1 0 00-1 1v12a1 1 0 001.555.832l10-6a1 1 0 000-1.664l-10-6A1 1 0 004 3z" /></svg><svg id="pause-icon" xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" viewBox="0 0 20 20" fill="currentColor"><path d="M5 4h3v12H5V4zm7 0h3v12h-3V4z" /></svg></button><button id="skip-btn" class="w-14 h-14 rounded-full flex items-center justify-center bg-gray-200/70 text-gray-600 shadow-sm hover:bg-gray-300/80 transition-all duration-150"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M5 4.5l10 7.5-10 7.5V4.5zM17 4.5v15h2V4.5h-2z" /></svg></button></div></div></div>
                    <div class="px-5 mt-6">
                         <h3 class="text-sm font-bold text-gray-500">番茄设置</h3>
                         <div class="custom-card p-4 space-y-3">
                            <div class="grid grid-cols-2 items-center"><span class="text-gray-700 font-medium">工作时间</span><div class="flex items-center space-x-2 justify-self-end"><button class="setting-btn w-8 h-8 rounded-md flex items-center justify-center bg-gray-200/80 text-gray-700 font-bold text-lg hover:bg-gray-300" data-target="work" data-amount="-1">-</button><span id="work-time-display" class="font-bold text-gray-800 w-10 text-center">25</span><button class="setting-btn w-8 h-8 rounded-md flex items-center justify-center bg-gray-200/80 text-gray-700 font-bold text-lg hover:bg-gray-300" data-target="work" data-amount="1">+</button></div></div>
                             <div class="grid grid-cols-2 items-center"><span class="text-gray-700 font-medium">休息时间</span><div class="flex items-center space-x-2 justify-self-end"><button class="setting-btn w-8 h-8 rounded-md flex items-center justify-center bg-gray-200/80 text-gray-700 font-bold text-lg hover:bg-gray-300" data-target="rest" data-amount="-1">-</button><span id="rest-time-display" class="font-bold text-gray-800 w-10 text-center">5</span><button class="setting-btn w-8 h-8 rounded-md flex items-center justify-center bg-gray-200/80 text-gray-700 font-bold text-lg hover:bg-gray-300" data-target="rest" data-amount="1">+</button></div></div>
                            <div class="grid grid-cols-2 items-center pt-3 border-t border-gray-200/70"><span class="text-gray-700 font-medium">时钟归属</span><div class="justify-self-end"><p id="clock-owner-text" class="font-semibold text-indigo-400">学习考研英语</p></div></div>
                         </div>
                    </div>
                </div>
            </div>
        </div>
        

        <div id="modal-overlay" class="modal-overlay">
    <div id="new-task-modal" class="modal-content hidden"><div class="flex justify-between items-center mb-6"><h2 class="text-xl font-bold">新建任务</h2><button class="close-modal text-gray-400 hover:text-gray-600 text-2xl">&times;</button></div><form action="#"><div class="space-y-4"><div><label class="text-sm font-semibold text-gray-600">任务标题</label><input type="text" placeholder="例如: 完成第三章习题" class="w-full mt-1 p-3 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none"></div><div><label class="text-sm font-semibold text-gray-600">执行日期</label><input type="text" placeholder="年 / 月 / 日" class="w-full mt-1 p-3 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none"></div><div class="relative"><label class="text-sm font-semibold text-gray-600">所属计划</label><input type="text" id="plan-input" readonly value="无计划" class="w-full mt-1 p-3 border border-gray-400 rounded-lg bg-white cursor-pointer"><div id="plan-dropdown" class="absolute hidden w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10"><a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">无计划</a><a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">考研冲刺</a><a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">健身增肌计划</a></div></div>
    <div>
        <label class="text-sm font-semibold text-gray-600">重复</label>
        <div class="flex mt-2 space-x-2">
            <button type="button" class="repeat-btn flex-1 py-2 border border-gray-400 rounded-lg text-sm active">不重复</button>
            <button type="button" class="repeat-btn flex-1 py-2 border border-gray-400 rounded-lg text-sm">每天</button>
            <button type="button" class="repeat-btn flex-1 py-2 border border-gray-400 rounded-lg text-sm">每周</button>
            <button type="button" class="repeat-btn flex-1 py-2 border border-gray-400 rounded-lg text-sm">每月</button>
        </div>
    </div>
    <div><label class="text-sm font-semibold text-gray-600">优先级</label><div class="flex mt-2 space-x-2"><button type="button" class="priority-btn flex-1 py-2 border border-gray-400 rounded-lg">低</button><button type="button" class="priority-btn flex-1 py-2 border border-gray-400 rounded-lg active">中</button><button type="button" class="priority-btn flex-1 py-2 border border-gray-400 rounded-lg">高</button></div></div><div>
                <label class="text-sm font-semibold text-gray-600">番茄钟数量</label>
                <div class="flex items-center justify-between mt-1 p-2 border border-gray-400 rounded-lg">
                    <button type="button" class="modal-pomo-btn w-8 h-8 rounded-md flex items-center justify-center bg-gray-200/80 text-gray-700 font-bold text-lg hover:bg-gray-300 transition-colors" data-action="decr">-</button>
                    <div class="text-gray-800">
                        <span id="modal-pomo-display" class="font-bold w-6 text-center inline-block">2</span>
                        <span>个</span>
                    </div>
                    <button type="button" class="modal-pomo-btn w-8 h-8 rounded-md flex items-center justify-center bg-gray-200/80 text-gray-700 font-bold text-lg hover:bg-gray-300 transition-colors" data-action="incr">+</button>
                </div>
            </div></div><div class="mt-8"><button type="submit" class="w-full text-white font-bold py-3 rounded-lg gradient-button">创建任务</button></div></form></div>
    <div id="new-reminder-modal" class="modal-content hidden"><div class="flex justify-between items-center mb-6"><h2 class="text-xl font-bold">新建提醒</h2><button class="close-modal text-gray-400 hover:text-gray-600 text-2xl">&times;</button></div><form action="#"><div class="space-y-4"><div><label class="text-sm font-semibold text-gray-600">提醒内容</label><input type="text" placeholder="例如: 晚上8点报名" class="w-full mt-1 p-3 border border-gray-400 rounded-lg"></div><div><label class="text-sm font-semibold text-gray-600">提醒时间</label><input type="text" placeholder="年 / 月 / 日 --:--" class="w-full mt-1 p-3 border border-gray-400 rounded-lg"></div></div><div class="mt-8"><button type="submit" class="w-full text-white font-bold py-3 rounded-lg gradient-button">创建提醒</button></div></form></div>
    <div id="new-plan-modal" class="modal-content hidden"><div class="flex justify-between items-center mb-6"><h2 class="text-xl font-bold">新建计划</h2><button class="close-modal text-gray-400 hover:text-gray-600 text-2xl">&times;</button></div><form action="#"><div class="space-y-4"><div><label class="text-sm font-semibold text-gray-600">计划名称</label><input type="text" placeholder="例如: 考研英语学习计划" class="w-full mt-1 p-3 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none"></div><div><label class="text-sm font-semibold text-gray-600">开始日期</label><input type="text" placeholder="年 / 月 / 日" class="w-full mt-1 p-3 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none"></div><div><label class="text-sm font-semibold text-gray-600">结束日期</label><input type="text" placeholder="年 / 月 / 日" class="w-full mt-1 p-3 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none"></div><div><label class="text-sm font-semibold text-gray-600">描述</label><textarea class="w-full mt-1 p-3 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none" rows="3" placeholder="可选"></textarea></div></div><div class="mt-8"><button type="submit" class="w-full text-white font-bold py-3 rounded-lg gradient-button">创建计划</button></div></form></div>
    
    <div id="snooze-reminder-modal" class="modal-content hidden"><div class="flex justify-between items-center mb-4"><h2 class="text-xl font-bold">推迟提醒</h2><button class="close-modal text-gray-400 hover:text-gray-600 text-2xl">&times;</button></div><div class="space-y-4"><p class="text-sm" style="color: var(--text-secondary);">选择推迟时间：</p><div class="flex justify-between space-x-2"><button class="snooze-btn flex-1 py-2 border border-gray-300 rounded-lg text-sm font-semibold">10 分钟</button><button class="snooze-btn flex-1 py-2 border border-gray-300 rounded-lg text-sm font-semibold active">30 分钟</button><button class="snooze-btn flex-1 py-2 border border-gray-300 rounded-lg text-sm font-semibold">1 小时</button></div><div><label class="text-sm font-semibold text-gray-600">或自定义分钟数</label><input type="number" class="w-full mt-1 p-3 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none"></div></div><div class="mt-8"><button type="submit" class="w-full text-white font-bold py-3 rounded-lg gradient-button">确认推迟</button></div></div>
    
    <div id="delete-confirm-modal" class="modal-content hidden">
        <h2 class="text-xl font-bold text-gray-800 text-center">确认删除</h2>
        <p id="delete-confirm-message" class="text-center text-gray-500 my-4 text-sm">您确定要永久删除这个已完成的计划吗？<br>此操作无法撤销。</p>
        <div class="flex justify-center space-x-4 mt-6">
            <button id="cancel-delete-btn" class="flex-1 py-3 border border-gray-300 rounded-lg font-semibold text-gray-700 hover:bg-gray-100 transition-colors">取消</button>
            <button id="confirm-delete-btn" class="flex-1 py-3 bg-red-500 text-white rounded-lg font-semibold hover:bg-red-600 transition-colors">确认删除</button>
        </div>
    </div>
    <div id="import-plan-modal" class="modal-content hidden">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold">导入计划</h2>
            <button class="close-modal text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
        </div>
        <form action="#">
            <div class="space-y-4">
                <div>
                    <label class="text-sm font-semibold text-gray-600">计划分享码</label>
                    <div class="relative flex items-center mt-1">
                        <input type="text" id="share-code-input" placeholder="请在此处粘贴分享码" class="w-full p-3 pr-20 border border-gray-400 rounded-lg focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 outline-none">
                        <button type="button" id="paste-code-btn" class="absolute right-2 bg-indigo-100 text-indigo-600 text-sm font-semibold px-3 py-1 rounded-md hover:bg-indigo-200 transition-colors">粘贴</button>
                    </div>
                </div>
            </div>
            <div class="mt-8">
                <button type="submit" class="w-full text-white font-bold py-3 rounded-lg gradient-button">确认导入</button>
            </div>
        </form>
    </div>
    <div id="restart-plan-modal" class="modal-content hidden">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold text-gray-800">重新激活计划</h2>
            <button class="close-modal text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
        </div>
        <p class="text-sm text-gray-600 mb-6">您可以为此已完成的计划新建任务，或选择重新开始已有的任务。</p>
        <div class="space-y-3">
            <button id="restart-option-new-task" class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <p class="font-semibold text-gray-800">新建单个任务</p>
                <p class="text-xs text-gray-500 mt-1">为这个计划添加一个全新的任务</p>
            </button>
            <button id="restart-option-select-tasks" class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <p class="font-semibold text-gray-800">选择部分任务重新开始</p>
                <p class="text-xs text-gray-500 mt-1">从列表中选择一个或多个任务来重新激活</p>
            </button>
            <button id="restart-option-all-tasks" class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <p class="font-semibold text-gray-800">重新开始所有任务</p>
                <p class="text-xs text-gray-500 mt-1">将计划内的所有任务恢复为待办状态</p>
            </button>
        </div>
    </div>
    <div id="xueba-mode-info-modal" class="modal-content hidden">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">学霸模式</h2>
            <button class="close-modal text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
        </div>
        <div class="space-y-4">
            <p class="text-sm" style="color: var(--text-secondary);">
                专注期间将为您屏蔽所有手机通知，并锁定App，必须完成该次专注后方可解锁。
            </p>
            <div class="bg-white border border-gray-200 rounded-lg p-4 flex justify-between items-center">
                <span class="font-semibold" style="color: var(--text-primary);">开启学霸模式</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="xueba-mode-actual-toggle">
                    <span class="toggle-slider"></span>
                </label>
            </div>
        </div>
        </div>

    <div id="ai-confirm-modal" class="modal-content hidden">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">AI为你创建了任务</h2>
            <button class="close-modal text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
        </div>
        <p class="text-sm text-gray-500 mb-4">请确认为您解析出的信息是否准确：</p>
        <div class="space-y-3 bg-slate-50 p-4 rounded-lg">
            <div id="ai-confirm-title" class="font-bold text-gray-800 text-lg">明天下午3点和设计团队开会</div>
            <div class="text-sm text-gray-600">
                <span class="font-semibold">时间:</span>
                <span id="ai-confirm-time">2025年6月24日 15:00</span>
            </div>
            <div class="text-sm text-gray-600">
                <span class="font-semibold">所属计划:</span>
                <span id="ai-confirm-plan">无计划</span>
            </div>
        </div>
        <div class="flex justify-center space-x-4 mt-6">
            <button id="ai-edit-btn" class="flex-1 py-3 border border-gray-300 rounded-lg font-semibold text-gray-700 hover:bg-gray-100 transition-colors">编辑</button>
            <button id="ai-confirm-btn" class="flex-1 py-3 text-white rounded-lg font-semibold gradient-button">确认创建</button>
        </div>
    </div>
</div>

        
        <div id="share-action-sheet-overlay" class="absolute inset-0 bg-slate-900/40 backdrop-blur-sm z-50 flex items-end">
            <div id="share-action-sheet-content" class="bg-slate-100 w-full rounded-t-2xl p-2.5">
                <div class="bg-white rounded-xl">
                    <button class="action-sheet-option w-full text-center p-3.5 text-lg text-blue-500 font-medium border-b border-gray-200">生成分享码</button>
                    <button class="action-sheet-option w-full text-center p-3.5 text-lg text-blue-500 font-medium">生成带二维码的图片</button>
                </div>
                <button id="cancel-share-btn" class="w-full text-center p-3.5 text-lg text-blue-500 font-bold bg-white rounded-xl mt-2">取消</button>
            </div>
        </div>
        
        <div id="onboarding-overlay">
            <div class="relative w-full px-6 pb-28 text-center">
                <svg class="onboarding-arrow w-12 h-12 text-white mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 11l3-3m0 0l3 3m-3-3v8m0-13a9 9 0 110 18 9 9 0 010-18z" />
                </svg>
                <h2 class="text-2xl font-bold text-white mt-4">体验AI创建任务</h2>
                <p class="text-base text-gray-200 mt-2">点击下方的麦克风按钮，试着对我说出你的待办事项，比如“周五晚上八点去看电影”。</p>
                <button id="close-onboarding-btn" class="mt-6 px-6 py-2 bg-white/20 text-white font-semibold rounded-full hover:bg-white/30 transition">我知道了</button>
            </div>
        </div>
        </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // --- App State & Core Navigation ---
    const pageContainer = document.getElementById('page-container');
    let pageHistory = ['page-home'];
    let activePage = document.getElementById('page-home');
    let itemPendingDeletion = null;
    let itemPendingDeletionType = '';
    let currentPlanConfig = {};

    function showPage(targetId, isBack = false) {
        const pages = document.querySelectorAll('.page');
        const currentId = pageHistory[pageHistory.length - 1];
        if (currentId === targetId && !isBack) return;
        
        if (isBack) {
            if (pageHistory.length > 1) pageHistory.pop();
        } else {
            pageHistory.push(targetId);
        }
        
        const newActivePageId = pageHistory[pageHistory.length - 1];
        activePage = document.getElementById(newActivePageId);

        if (activePage) {
            const mainContent = activePage.querySelector('.main-content');
            if (mainContent) mainContent.scrollTop = 0;
        }

        pages.forEach(page => {
            const isTarget = page.id === newActivePageId;
            const isCurrent = page.id === currentId;
            
            // Set background color based on page type
            if(page.id === 'page-scan') {
                page.style.backgroundColor = 'black';
            } else if (page.id.startsWith('page-plan-details') || page.id === 'page-home' || page.id === 'page-plan' || page.id === 'page-review') {
                page.style.backgroundColor = 'var(--bg-shell)';
            } else {
                page.style.backgroundColor = 'var(--bg-page)';
            }
            
            // Animate page transition
            if (isTarget) {
                page.classList.remove('hidden-right', 'hidden-left');
            } else if (isCurrent) {
                page.classList.add(isBack ? 'hidden-right' : 'hidden-left');
            }
        });
        
        // Auto-start timer when navigating to clock page
        if (targetId === 'page-clock' && !isBack && window.pomodoroTimer) {
             window.pomodoroTimer.resetAndStart();
        }
    }

    // --- Share Action Sheet ---
    const shareActionSheetOverlay = document.getElementById('share-action-sheet-overlay');
    
    function openShareSheet() {
        if (shareActionSheetOverlay) shareActionSheetOverlay.classList.add('active');
    }

    function closeShareSheet() {
        if (shareActionSheetOverlay) shareActionSheetOverlay.classList.remove('active');
    }
    
    // --- Centralized Click Handler ---
    document.body.addEventListener('click', e => {
        // Handle share button clicks
        const shareBtn = e.target.closest('.share-plan-btn, .share-plan-btn-details');
        if (shareBtn) {
            e.stopPropagation();
            e.preventDefault();
            openShareSheet();
            return;
        }

        // Handle navigation link clicks
        const navLink = e.target.closest('.nav-link');
        if (navLink && navLink.dataset.target) {
            e.preventDefault();
            // Prevent navigation for specific buttons inside a nav-link
            if (e.target.closest('#toggle-completed-btn') || e.target.closest('.delete-item-icon') || e.target.closest('.task-delete-button')) {
                return;
            }
            const targetPageId = navLink.dataset.target;
            // Update task details title when navigating
            if (targetPageId === 'page-task-details') {
                const taskCard = navLink.closest('.task-card, .plan-details-task-card');
                if (taskCard) {
                     const taskTitle = taskCard.querySelector('.font-bold, .font-semibold')?.textContent.trim() || "未知任务";
                     const taskDetailsPage = document.getElementById('page-task-details');
                     taskDetailsPage.querySelector('#task-details-title').textContent = taskTitle;
                     taskDetailsPage.querySelector('#task-details-start-btn').dataset.taskName = taskTitle;
                }
            }
            // Update clock owner task name
            if (targetPageId === 'page-clock') {
                const taskName = navLink.dataset.taskName || '未命名专注';
                const clockOwnerEl = document.getElementById('clock-owner-text');
                if(clockOwnerEl) clockOwnerEl.textContent = taskName;
            }
            showPage(targetPageId, false);
            return;
        }

        // Handle back link clicks
        const backLink = e.target.closest('.back-link');
        if (backLink) {
            e.preventDefault();
            if (pageHistory.length > 1) {
                showPage(pageHistory[pageHistory.length - 2], true);
            }
            return;
        }
        
        // --- NEW: Handle Shelve Plan Button ---
        const shelveBtn = e.target.closest('.shelve-plan-btn-details');
        if (shelveBtn) {
            e.preventDefault();
            e.stopPropagation();
            const planDetailsPage = shelveBtn.closest('.page');
            const planCard = document.querySelector(`.nav-link[data-target="${planDetailsPage.id}"]`);
            const shelvedContainer = document.getElementById('more-shelved');
            if (planCard && shelvedContainer) {
                planCard.classList.add('opacity-70');
                planCard.dataset.originalTarget = planDetailsPage.id;
                planCard.dataset.target = 'page-plan-details-4';
                const progressBar = planCard.querySelector('.gradient-button');
                if (progressBar) {
                    progressBar.style.backgroundImage = 'none';
                    progressBar.style.backgroundColor = '#9ca3af'; // gray-400
                }
                shelvedContainer.appendChild(planCard);
                showPage('page-plan', true);
            }
             document.querySelectorAll('.popup-menu').forEach(menu => menu.classList.add('opacity-0', 'pointer-events-none'));
            return;
        }

        // --- NEW: Handle Reactivate Plan Button ---
        const reactivateBtn = e.target.closest('.reactivate-plan-btn-details');
        if (reactivateBtn) {
            e.preventDefault();
            e.stopPropagation();
            const planCard = document.querySelector(`.nav-link[data-target="page-plan-details-4"]`);
            const inProgressContainer = document.querySelector('#page-plan .mt-4.space-y-4');
            if (planCard && inProgressContainer) {
                planCard.classList.remove('opacity-70');
                planCard.dataset.target = planCard.dataset.originalTarget;
                 const progressBar = planCard.querySelector('.h-2\\.5.rounded-full');
                 if (progressBar) {
                    progressBar.style.backgroundColor = '';
                    progressBar.style.backgroundImage = 'linear-gradient(to right, var(--accent-start), var(--accent-end))';
                }
                inProgressContainer.appendChild(planCard);
                showPage('page-plan', true);
            }
            document.querySelectorAll('.popup-menu').forEach(menu => menu.classList.add('opacity-0', 'pointer-events-none'));
            return;
        }
    });
    
    // --- Tasks Container Logic (Stack/Expand/List) ---
    const tasksContainer = document.getElementById('tasks-container');
    if (tasksContainer) {
        const newCollapseBtn = document.getElementById('new-collapse-btn');
        tasksContainer.addEventListener('click', function(e) {
            const playButtonLink = e.target.closest('a.nav-link[data-target="page-clock"]');
            if (playButtonLink || tasksContainer.classList.contains('list-view') || this.classList.contains('tasks-expanded')) {
                return;
            }
            const taskCard = e.target.closest('.task-card');
            if (taskCard) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.add('tasks-expanded');
            }
        });
        if (newCollapseBtn) {
            newCollapseBtn.addEventListener('click', function() {
                tasksContainer.classList.remove('tasks-expanded');
            });
        }
    }
    
    // --- Modal Logic ---
    const modalOverlay = document.getElementById('modal-overlay');
    function showModal(modalId) { 
        if (activePage) activePage.querySelector('.main-content')?.classList.add('noscroll');
        modalOverlay.classList.add('active'); 
        modalOverlay.querySelectorAll('.modal-content').forEach(m => m.classList.add('hidden')); 
        document.getElementById(modalId)?.classList.remove('hidden');
    }
    function hideAllModals() { 
        if (activePage) activePage.querySelector('.main-content')?.classList.remove('noscroll');
        modalOverlay.classList.remove('active');
        itemPendingDeletion = null;
        itemPendingDeletionType = '';
    }
    modalOverlay.addEventListener('click', function(e) { if (e.target === this) hideAllModals(); });
    document.querySelectorAll('.close-modal, #cancel-delete-btn').forEach(btn => btn.addEventListener('click', hideAllModals));
    
    // --- Popup Menu Logic ---
    function togglePopupMenu(e) {
        e.stopPropagation(); 
        const button = e.currentTarget;
        const menuId = button.dataset.menu;
        if (!menuId) return;
        const menu = document.getElementById(menuId);
        if (!menu) return;
        const isHidden = menu.classList.contains('opacity-0');
        document.querySelectorAll('.popup-menu').forEach(m => {
            if (m.id !== menuId) m.classList.add('opacity-0', 'pointer-events-none');
        });
        menu.classList.toggle('opacity-0');
        menu.classList.toggle('pointer-events-none');
    }
    document.querySelectorAll('.options-button, .add-button, #reminder-options-btn, #sort-tasks-btn').forEach(btn => btn.addEventListener('click', togglePopupMenu));
    window.addEventListener('click', (e) => { 
        if (!e.target.closest('.popup-menu') && !e.target.closest('[data-menu]')) {
             document.querySelectorAll('.popup-menu').forEach(menu => menu.classList.add('opacity-0', 'pointer-events-none'));
        }
    });

    // --- Action Button Listeners ---
    document.querySelectorAll('.action-new-task').forEach(btn => btn.addEventListener('click', e => { e.preventDefault(); showModal('new-task-modal'); }));
    document.querySelectorAll('.action-new-reminder').forEach(btn => btn.addEventListener('click', e => { e.preventDefault(); showModal('new-reminder-modal'); }));
    document.querySelectorAll('.action-new-plan').forEach(btn => btn.addEventListener('click', e => { e.preventDefault(); showModal('new-plan-modal'); }));
    const reminderMenu = document.getElementById('reminder-menu');

document.getElementById('mark-reminder-done-btn')?.addEventListener('click', e => {
    e.preventDefault();
    document.getElementById('reminder-text')?.classList.add('text-strikethrough');
    // 新增：点击后关闭菜单
    if (reminderMenu) reminderMenu.classList.add('opacity-0', 'pointer-events-none');
});
document.getElementById('snooze-reminder-btn')?.addEventListener('click', e => {
    e.preventDefault();
    showModal('snooze-reminder-modal');
    // 新增：点击后关闭菜单
    if (reminderMenu) reminderMenu.classList.add('opacity-0', 'pointer-events-none');
});
    document.querySelectorAll('.priority-btn, .snooze-btn, .repeat-btn').forEach(btn => { 
        btn.addEventListener('click', function() { 
            this.parentElement.querySelectorAll(`.${this.classList[0]}`).forEach(b => b.classList.remove('active')); 
            this.classList.add('active'); 
        }); 
    });

    // --- Home Calendar ---
    (function initHomeCalendar() {
        const monthEl = document.getElementById('home-calendar-month');
        const weekEl = document.getElementById('home-calendar-week');
        if (!monthEl || !weekEl) return;
        const today = new Date();
        const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
        monthEl.textContent = `${today.getFullYear()}年 ${today.getMonth() + 1}月`;
        let firstDayOfWeek = today.getDate() - today.getDay();
        let weekHTML = '';
        for (let i = 0; i < 7; i++) {
            const date = new Date(today.getFullYear(), today.getMonth(), firstDayOfWeek + i);
            const isToday = date.toDateString() === today.toDateString();
            const dayClass = isToday ? 'text-white rounded-full gradient-button' : 'text-gray-700';
            weekHTML += `<div class="flex flex-col items-center space-y-2 p-1"><span class="text-xs text-gray-500">${weekDays[date.getDay()]}</span><span class="w-8 h-8 flex items-center justify-center font-bold ${dayClass}">${date.getDate()}</span></div>`;
        }
        weekEl.innerHTML = weekHTML;
    })();
    
    // --- Full Calendar Page ---
    (function initFullCalendar() {
        const calGridEl = document.getElementById('calendar-grid');
        if (!calGridEl) return;
        const calYearEl = document.getElementById('calendar-year');
        const calMonthTextEl = document.getElementById('calendar-month');
        let calCurrentDate = new Date();
        function generateCalendar(date) {
            const year = date.getFullYear(), month = date.getMonth(), todayForCal = new Date();
            if (calYearEl) calYearEl.textContent = `${year}年`;
            if (calMonthTextEl) calMonthTextEl.textContent = `${month + 1}月`;
            const firstDayOfMonth = new Date(year, month, 1).getDay();
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            calGridEl.innerHTML = '';
            for (let i = 0; i < firstDayOfMonth; i++) calGridEl.innerHTML += '<div></div>';
            for (let i = 1; i <= daysInMonth; i++) {
                const isCurrentDay = (year === todayForCal.getFullYear() && month === todayForCal.getMonth() && i === todayForCal.getDate());
                calGridEl.innerHTML += `<div class="flex items-center justify-center h-12"><span class="${isCurrentDay ? 'w-8 h-8 flex items-center justify-center font-bold text-white bg-indigo-400 rounded-full' : 'font-medium text-gray-700 hover:bg-gray-100 rounded-full w-8 h-8 flex items-center justify-center'}">${i}</span></div>`;
            }
        }
        document.getElementById('prev-month-btn')?.addEventListener('click', () => { calCurrentDate.setMonth(calCurrentDate.getMonth() - 1); generateCalendar(calCurrentDate); });
        document.getElementById('next-month-btn')?.addEventListener('click', () => { calCurrentDate.setMonth(calCurrentDate.getMonth() + 1); generateCalendar(calCurrentDate); });
        generateCalendar(calCurrentDate);
    })();
    
    // --- Pomodoro Clock ---
    if (document.getElementById('page-clock')) {
        const timeDisplay = document.getElementById('time-display'), statusDisplay = document.getElementById('status-display'), playPauseBtn = document.getElementById('play-pause-btn'), playIcon = document.getElementById('play-icon'), pauseIcon = document.getElementById('pause-icon'), resetBtn = document.getElementById('reset-btn'), skipBtn = document.getElementById('skip-btn'), workTimeDisplay = document.getElementById('work-time-display'), restTimeDisplay = document.getElementById('rest-time-display'), progressCircle = document.getElementById('timer-progress');
        const totalCircleLength = 565.48;
        let timer, isRunning = false, isWorkSession = true, workTime = 25, restTime = 5, timeRemaining = workTime * 60, totalTime = workTime * 60;
        const timerFunctions = {
            updateDisplay: function() { const minutes = Math.floor(timeRemaining / 60).toString().padStart(2, '0'); const seconds = (timeRemaining % 60).toString().padStart(2, '0'); timeDisplay.textContent = `${minutes}:${seconds}`; const progress = (totalTime - timeRemaining) / totalTime; if (progressCircle) progressCircle.style.strokeDashoffset = totalCircleLength * (1 - progress); },
            start: function() { if (isRunning) return; isRunning = true; playIcon?.classList.add('hidden'); pauseIcon?.classList.remove('hidden'); timer = setInterval(() => { if (timeRemaining > 0) { timeRemaining--; this.updateDisplay(); } else { clearInterval(timer); this.switchSession(); } }, 1000); },
            pause: function() { isRunning = false; playIcon?.classList.remove('hidden'); pauseIcon?.classList.add('hidden'); clearInterval(timer); },
            reset: function() { this.pause(); timeRemaining = isWorkSession ? workTime * 60 : restTime * 60; totalTime = timeRemaining; this.updateDisplay(); },
            resetAndStart: function() { isWorkSession = true; statusDisplay.textContent = "工作进行中"; this.reset(); this.start(); },
            switchSession: function() { isWorkSession = !isWorkSession; statusDisplay.textContent = isWorkSession ? "工作进行中" : "休息一下"; this.reset(); setTimeout(() => this.start(), 1000); },
            updateSettings: function(target, amount) { if (isRunning) return; if (target === 'work') { workTime = Math.max(1, workTime + amount); workTimeDisplay.textContent = workTime; } else if (target === 'rest') { restTime = Math.max(1, restTime + amount); restTimeDisplay.textContent = restTime; } if ((isWorkSession && target === 'work') || (!isWorkSession && target === 'rest')) this.reset(); }
        };
        playPauseBtn?.addEventListener('click', () => isRunning ? timerFunctions.pause() : timerFunctions.start());
        resetBtn?.addEventListener('click', () => timerFunctions.reset());
        skipBtn?.addEventListener('click', () => timerFunctions.switchSession());
        document.querySelectorAll('.setting-btn').forEach(btn => btn.addEventListener('click', () => timerFunctions.updateSettings(btn.dataset.target, parseInt(btn.dataset.amount))));
        window.pomodoroTimer = { resetAndStart: timerFunctions.resetAndStart.bind(timerFunctions), pause: timerFunctions.pause.bind(timerFunctions) };
        timerFunctions.updateDisplay();
    }
    
    // --- Plan Page Toggles & Deletion Logic ---
    function setupToggle(btnId, iconId, sectionId) {
    const toggleBtn = document.getElementById(btnId);
    const toggleIcon = document.getElementById(iconId);
    const section = document.getElementById(sectionId);
    if (!toggleBtn || !section) return;

    // 新的判断逻辑：只有“进行中”的展开按钮使用上下箭头（旋转180度）
    const use180degRotate = btnId.includes('in-progress'); 

    toggleBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        section.classList.toggle('plan-visible-section');
        // 新的旋转逻辑：其他按钮（如搁置和已完成）的左右箭头都旋转90度
        toggleIcon.classList.toggle(use180degRotate ? 'rotate-180' : 'rotate-90');
        
        // “已完成”分类的特殊逻辑保持不变
        if (btnId.includes('completed')) {
            const isOpening = section.classList.contains('plan-visible-section');
            const deleteIcons = document.querySelectorAll('.delete-item-icon');
            const clearAllContainer = document.getElementById('clear-all-container');
            if (isOpening) {
                deleteIcons.forEach(icon => icon.classList.remove('hidden'));
                clearAllContainer?.classList.remove('hidden');
            } else {
                deleteIcons.forEach(icon => icon.classList.add('hidden'));
                clearAllContainer?.classList.add('hidden');
            }
        }
    });
}

    setupToggle('toggle-in-progress-btn', 'toggle-in-progress-icon', 'more-in-progress');
    setupToggle('toggle-shelved-btn', 'toggle-shelved-icon', 'more-shelved'); // NEW
    setupToggle('toggle-completed-btn', 'toggle-completed-icon', 'more-completed');

    document.querySelectorAll('.plan-details-task-list, #more-completed, .delete-plan-btn-details').forEach(container => {
        container.addEventListener('click', function(e) {
            const targetDeleteButton = e.target.closest('.delete-item-icon, .task-delete-button, .delete-plan-btn-details');
            if (!targetDeleteButton) return;
            e.preventDefault(); e.stopPropagation();
            if (targetDeleteButton.classList.contains('delete-plan-btn-details')) {
                 itemPendingDeletionType = 'plan-details-plan';
                 document.getElementById('delete-confirm-message').innerHTML = '您确定要永久删除整个计划及其所有任务吗？<br>此操作无法撤销。';
            } else {
                 itemPendingDeletion = e.target.closest('.plan-completed-item, .plan-details-task-card');
                 itemPendingDeletionType = 'task-or-completed-plan';
                 document.getElementById('delete-confirm-message').innerHTML = '您确定要永久删除这个项目吗？<br>此操作无法撤销。';
            }
            showModal('delete-confirm-modal');
        });
    });

    document.getElementById('confirm-delete-btn')?.addEventListener('click', function() {
        if (itemPendingDeletionType === 'plan-details-plan') {
            if (pageHistory.length > 1) showPage(pageHistory[pageHistory.length - 2], true);
        } else if (itemPendingDeletionType === 'task-or-completed-plan' && itemPendingDeletion) {
            itemPendingDeletion.style.transition = 'opacity 0.3s, max-height 0.4s, margin 0.4s, padding 0.4s, border 0.4s';
            itemPendingDeletion.style.opacity = '0';
            itemPendingDeletion.style.maxHeight = '0px';
            itemPendingDeletion.style.padding = '0';
            itemPendingDeletion.style.margin = '0';
            setTimeout(() => itemPendingDeletion.remove(), 400);
        }
        hideAllModals();
    });

    document.getElementById('clear-all-btn')?.addEventListener('click', () => {
        moreCompletedSection.querySelectorAll('.plan-completed-item').forEach(item => {
             item.style.transition = 'opacity 0.3s, transform 0.3s';
             item.style.opacity = '0';
             setTimeout(() => item.remove(), 300);
        });
    });
    
    // --- Onboarding, AI, and other Modals ---
(function initModalsAndOverlays() {
    // Onboarding
    const onboardingOverlay = document.getElementById('onboarding-overlay');
    // 删除了对 localStorage 的检查，现在只要弹窗元素存在就会执行
    if (onboardingOverlay) {
        setTimeout(() => onboardingOverlay.classList.add('active'), 500);
    }
    document.getElementById('close-onboarding-btn')?.addEventListener('click', () => {
        onboardingOverlay.classList.remove('active');
        // 删除了对 localStorage 的设置
    });

    // ... (此函数内其他代码保持不变)


        // AI Voice Flow
        document.getElementById('ai-voice-btn')?.addEventListener('click', (e) => { e.preventDefault(); showModal('ai-confirm-modal'); });
        document.getElementById('ai-edit-btn')?.addEventListener('click', () => { hideAllModals(); showModal('new-task-modal'); });
        document.getElementById('ai-confirm-btn')?.addEventListener('click', () => { hideAllModals(); alert('任务已创建！'); });

        // Other modals
        document.getElementById('import-share-code-btn')?.addEventListener('click', e => { e.preventDefault(); showModal('import-plan-modal'); });
        document.getElementById('open-xueba-modal-btn')?.addEventListener('click', () => showModal('xueba-mode-info-modal'));
        document.getElementById('xueba-mode-actual-toggle')?.addEventListener('change', () => setTimeout(hideAllModals, 500));
        
        // Completed plan restart flow
        function setupCompletedPlanPage(planCode) {
            document.getElementById(`add-btn-completed-plan-${planCode}`)?.addEventListener('click', (e) => {
                e.stopPropagation();
                currentPlanConfig = { pageId: `page-plan-details-completed-${planCode}`, taskListId: `completed-${planCode}-task-list`, confirmBarId: `selection-confirm-bar-${planCode}` };
                showModal('restart-plan-modal');
            });
             document.getElementById(`cancel-selection-btn-${planCode}`)?.addEventListener('click', () => {
                const page = document.getElementById(currentPlanConfig.pageId);
                page.querySelector(`#${currentPlanConfig.taskListId}`).classList.remove('selection-mode-active');
                page.querySelector('footer').classList.remove('hidden');
                page.querySelector(`#${currentPlanConfig.confirmBarId}`).classList.add('hidden');
            });
        }
        setupCompletedPlanPage('cpa');
        setupCompletedPlanPage('ui');
        setupCompletedPlanPage('react');

        document.getElementById('restart-option-new-task')?.addEventListener('click', () => { hideAllModals(); showModal('new-task-modal'); });
        document.getElementById('restart-option-all-tasks')?.addEventListener('click', () => { hideAllModals(); alert('计划中的所有任务已重新激活!'); });
        document.getElementById('restart-option-select-tasks')?.addEventListener('click', () => {
            hideAllModals();
            if (currentPlanConfig.pageId) {
                const page = document.getElementById(currentPlanConfig.pageId);
                page.querySelector(`#${currentPlanConfig.taskListId}`).classList.add('selection-mode-active');
                page.querySelector('footer').classList.add('hidden');
                page.querySelector(`#${currentPlanConfig.confirmBarId}`).classList.remove('hidden');
            }
        });
    })();
    
    // --- Home Page View & Sort Logic ---
    const viewToggleBtn = document.getElementById('view-toggle-btn');
    if (viewToggleBtn) {
        viewToggleBtn.addEventListener('click', () => {
            tasksContainer.classList.toggle('list-view');
            tasksContainer.classList.remove('tasks-expanded');
            document.getElementById('view-icon-list').classList.toggle('hidden');
            document.getElementById('view-icon-stack').classList.toggle('hidden');
        });
    }
    document.getElementById('sort-tasks-menu')?.addEventListener('click', e => {
        if (e.target.classList.contains('sort-option')) {
            e.preventDefault();
            document.getElementById('sort-tasks-text').textContent = e.target.textContent;
        }
    });

    // --- Sub-task Logic ---
    (function initSubtasks() {
        const taskDetailsPage = document.getElementById('page-task-details');
        if (!taskDetailsPage) return;
        const subtasksList = taskDetailsPage.querySelector('#subtasks-list');
        const newSubtaskInput = taskDetailsPage.querySelector('#new-subtask-input');
        const addSubtaskBtn = taskDetailsPage.querySelector('#add-subtask-btn');
        const subtaskCounterTitle = taskDetailsPage.querySelector('h3[class*="subtask"]');
        function updateSubtaskCounter() {
            if (!subtasksList || !subtaskCounterTitle) return;
            const total = subtasksList.children.length;
            const completed = subtasksList.querySelectorAll('.subtask-item.completed').length;
            subtaskCounterTitle.textContent = `子任务 (${completed}/${total})`;
        }
        subtasksList?.addEventListener('change', e => {
            if (e.target.type === 'checkbox') {
                e.target.closest('.subtask-item').classList.toggle('completed', e.target.checked);
                updateSubtaskCounter();
            }
        });
        addSubtaskBtn?.addEventListener('click', () => {
            const title = newSubtaskInput.value.trim();
            if (title) {
                const newId = `subtask-${Date.now()}`;
                const li = document.createElement('li');
                li.className = 'subtask-item';
                li.innerHTML = `<input type="checkbox" id="${newId}" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"><label for="${newId}" class="flex-grow text-sm text-gray-800">${title}</label>`;
                subtasksList.appendChild(li);
                newSubtaskInput.value = '';
                updateSubtaskCounter();
            }
        });
        updateSubtaskCounter(); // Initial count
    })();
    
    // --- Initial page load ---
    showPage('page-home');
});
</script>