# 核心理念

> AI驱动，极致简约，计划共享，智能自适应

# 一、 版本信息

> 版本号：V0.1

> 创建日期：2025.5.26

> 审核人：孙博为

# 二、 变更日志

| **时间**    | **版本号** | **变更人** | **主要变更内容** |
| --------- | ------- | ------- | ---------- |
| 2025.5.26 | V0.1    | 孙博为     |            |
|           |         |         |            |
|           |         |         |            |

# 三、 文档说明

## 名词解释

| **术语 / 缩略词** | **说明**                                                                                                                                                  |
| ------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 分享           | **分享单位：** **只能分享“计划”**，不能分享单个任务或提醒，保证分享内容的价值和体系性。**操作：**                                                                                                |
| 计划           | **定义：** 一个由多个“任务”组成的长期目标集合。**它本身不可执行，是任务的容器。****创建：****呈现：**                                                                                            |
| 任务           | **定义：** 一个具体、需要专注时间完成的动作，是组成“计划”的基本单位。**属性：** 必须包含 **标题** 和 **番茄钟数量 (🍅 x N)**。可以归属于某个“计划”。**创建：****呈现：** 在“日程”页的“今日待办”列表中，以可点击的卡片形式呈现，上面有醒目的“▶️ 开始”按钮。 |
| 提醒           | **定义：** 一个简单的、无需番茄钟的到期通知。**创建：****呈现：** 在“日程”页的“今日待办”列表中，以一个带“🔔”图标的、不可执行的条目显示。                                                                         |
| 日历           | **定位：** **它不是一个独立的页面，而是“日程”页的核心组成部分。****呈现：**                                                                                                           |

# 四、 需求背景

> ## 目标市场
>
> * 打造一款能深度理解并适应用户学习习惯的革命性智能学习伴侣。通过AI智能规划、高质量模板、计划共享和自适应调整，帮助备考人群克服从计划到执行的障碍，实现真正个性化、高效的学习体验。
>
> * 核心人群：25-35岁自由职业者/职场新人/学生群体/备考
>
> * 痛点拆解：
>
>   * 多数用户卡在「任务拆解」环节
>
>   * 现有番茄工具仅解决执行层，缺乏规划辅助
>
>   * 手动拆解消耗启动精力
>
>   * 现有软件不够简洁

> ## 解决方案：四大核心支柱
>
> 1. **AI任务分解引擎：** 以自然语言输入为唯一入口，AI自动识别意图并完成计划、任务、提醒的创建与拆解。
>
> 2. **计划模板与分享体系：** 用户不仅能通过分享码一键复制“学霸”的完整计划，还能从官方模板市场获取高质量的学习路径。
>
> 3. **智能成长与复盘：** 通过用户对任务时长的反馈，AI能够自我学习和调整，让未来的计划越来越懂用户。
>
> 4. **沉浸式专注与游戏化：** “学霸模式”屏蔽一切干扰，结合“鸡哥”助手和集卡式成就系统，让学习过程专注且有趣。
>
> **核心设计哲学： 以“今天”为执行中心，以“日历”为规划中心，通过AI和手动两种方式，无缝连接用户的长期计划与每日行动。**

## 核心用户流程 (Core User Flow)

#### 流程一：AI驱动的“零思考”路径

1. **用户** 打开App，对手机说：“帮我规划一下，一个月内复习完《经济法》。”

2. **AI** 自动将目标拆解为4个周计划，每周包含2-3个核心任务，并预估好番茄钟。

3. 支持用户手动调节任务顺序，时间长短等

4. **App** 在首页提示：“计划已生成！今天的任务是‘复习第一章：总论’，需要3个番茄钟，要现在开始吗？”

5. 用户点击 **\[开始]**，直接进入沉浸式番茄钟界面。

#### 流程二：社区驱动的“抄作业”路径

1. **用户** 在考研论坛看到一张分享截图，上面有“CPA会计通关计划”的二维码。

2. 打开App，使用“扫一扫”功能扫描二维码。

3. **App** 预览计划详情，用户点击 **\[一键使用]**。

4. 整个计划被完整复制到用户的日程中，当天即可开始执行。

## 产品 / 数据现状

APP还未上线

![](images/diagram.png)

## 竞品分析

| 产品         | **主要信息** | 专注方式 | 盈利模式 | **关键结论**          | **截图或视频**                                       |                                                 |                                                 |                                                 |                                                 |
| ---------- | -------- | ---- | ---- | ----------------- | ----------------------------------------------- | ----------------------------------------------- | ----------------------------------------------- | ----------------------------------------------- | ----------------------------------------------- |
| **番茄TODO** |          | 番茄时钟 | 会员增值 | UI设计非常差功能很细，实用性一般 | ![](images/20acb40a00e3cc2a4af86e1e0e81599.png) | ![](images/0fffa2c4b81ee139ea755d172c0391f.png) | ![](images/54f10e8e6749b9c547fe9581c14c55f.png) | ![](images/4cb3fb6ad6a57a652fcfb568f5d3d65.png) | ![](images/2badf0de0dc5b26893a53df9898f6a5.png) |

# 五、 需求范围

> 核心功能：通过一句话将任务分解，并完成任务拆分

# 六、 功能详细说明

## 产品流程图

> 流程图如下

![](images/diagram-1.png)

## 交互原型图

> 在空白行输入“/Figma” ，插入 Figma 设计稿

![](images/image-9.png)

新

![](images/image-8.png)



![](images/image-6.png)

新

![](images/image-7.png)



![](images/image-3.png)

新

![](images/image-4.png)

![](images/image-5.png)



![](images/image.png)



![](images/image-1.png)



![](images/image-2.png)

![](images/image-24.png)

新

![](images/image-23.png)



![](images/image-22.png)

![](images/image-18.png)

新

![](images/image-21.png)



![](images/image-19.png)

![](images/image-20.png)

![](images/image-17.png)

![](images/image-15.png)

![](images/image-16.png)





## 功能说明

| **模块** | **功能**   | **功能详细说明**                                                                                                                                            | **交互图**                                          |
| ------ | -------- | ----------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------ |
| 日程     | 查看日期     | 1.点击向下按键，点击之后会显示当月2.支持左右滑动，左右滑动会按照星期为单位进行切换3.点击具体日子，点击+号，可以进行提醒或者任务做待办和提醒                                                                             | ![](images/image-10.png)![](images/image-11.png) |
|        | 新增提醒     | 仅需提醒当天需要做的事：如-某人生日，某一天的报考等，可设置提醒的时间，通常为一次性时间                                                                                                          | ![](images/image-12.png)                         |
|        | 新增任务     | 任务是计划的一个组成，需要用到番茄计时的内容，需要填写完成的时间，支持设置子任务，可以归属到计划里，可以设置提醒任务开始的时间（非必填）                                                                                  | ![](images/image-13.png)                         |
| 记录     | 数据记录     | 个人数据的记录，包括日数据，周数据以及月数据，包含学习的时间，任务的个人数，完成度的趋势分析                                                                                                        | ![](images/image-14.png)                         |
| 扫一扫    | 扫一扫      | 扫描别人分享的学习计划，一键复制到自己的计划中，然后由用户进行修改                                                                                                                     | ![](images/image-25.png)                         |
| 分享     | 分享       | 支持分享自己的已完成计划或者未完成计划                                                                                                                                   | 支持分享到朋友圈，微信，以及复制连接和生成二维码图片                       |
| 今日任务   | 首页今日任务提醒 | 进行今日任务的提醒，第一页是第一优先级，点击之后，会按照当前顺序进行排列，用户可以点击进行位置换取，右滑删除任务，点击开始按钮进行番茄时钟完成任务消除，左滑点击任务修改                                                                  |                                                  |
| 输入框    | +号       | 支持新建任务、提醒、计划                                                                                                                                          |                                                  |
|        | 语音输入     | 通过用户的一句话，帮忙新建任务、提醒、计划                                                                                                                                 |                                                  |
| 时钟     | 番茄时钟界面   | 1.支持用户直接进行计划外的番茄计时学习2.支持用户自己改动番茄时长和休息时间                                                                                                               |                                                  |
| 计划     | 计划页面     | 1.点击向下可以支持全部任务预览，平时只留三个计划和一个提醒，提醒一直在右边第二个2.支持计划的删除3.已完成的任务折叠记录4.已完成的任务支持分享和清除，清除支持一键清除，清除后30天内在回收站可以找到进行恢复5.计划是多个任务组成，每个任务由一个卡片组成，用户可以组合任务的顺序，名称，删除任务 |                                                  |
| 公告     | 公告提醒悬挂   | 1.鸡哥在首页会提醒当日待办2.一般的提醒和解释说明                                                                                                                            |                                                  |
| 任务     | 新增任务     | 1.任务颗粒度较细，支持新建，删除2.任务包含是否提醒，开始时间和结束时间，具体用时                                                                                                            |                                                  |
| 我的     | 学霸模式     | 1.即勿扰模式，如果开始学霸模式，仅在手机屏幕显示番茄时钟                                                                                                                         |                                                  |
|        | ~~回收站~~  | ~~1.保留30天内用户删除的任务，计划，如果用户清空，即全部删除~~                                                                                                                   |                                                  |
|        | 头像       | 1.首次登陆默认头像，支持修改                                                                                                                                       |                                                  |
|        | 名字       | 1.首次登录，系统默认起名，支持修改，2.记录每天打开的次数，有一次使用交互，即多学习1天                                                                                                         |                                                  |
|        | 已完成      | 1.显示用户已完成的计划卡片，支持分享，宝可梦集卡形式                                                                                                                           |                                                  |
|        | 通用设置     | 1.用户可以选择注销用户，注销无法回复2.用户可以设置提醒铃声                                                                                                                       |                                                  |





# 七、 项目规划

>

